<?php

use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\DocumentNaam */
?>
<div class="document-naam-view">

    <?= DetailView::widget([
        'model' => $model,
        'attributes' => [
            'id',
            'type',
            [
                'attribute' => 'layout_template',
                'format' => 'raw',
                'value' => function ($model) {
                    return $model->layout_template;
                }
            ],
            'created_at',
            'updated_at',
            [
                'attribute' => 'created_by',
                'value' => function ($model) {
                    return $model->createdBy->username;
                }
            ],
            [
                'attribute' => 'created_by',
                'value' => function ($model) {
                    return $model->createdBy->username;
                }
            ],
        ],
    ]) ?>

</div>