<?php

namespace common\components;

use Yii;

class EncryptDecryptID
{
    /**
     * Encrypts an ID
     * 
     * @param mixed $id The ID to encrypt
     * @return string Base64 encoded encrypted string
     */
    public static function encryptId($id)
    {
        $data = serialize($id);
        return strtr(base64_encode(Yii::$app->security->encryptByKey(
            $data,
            self::getSecretKey()
        )), '+/', '-_');
    }

    /**
     * Decrypts an encrypted ID
     * 
     * @param string $encrypted The encrypted string
     * @return mixed The original ID
     * @throws \yii\base\InvalidConfigException
     */
    public static function decryptId($encrypted)
    {
        $data = base64_decode(strtr($encrypted, '-_', '+/'));
        $decrypted = Yii::$app->security->decryptByKey(
            $data,
            self::getSecretKey()
        );

        if ($decrypted === false) {
            throw new \yii\base\InvalidConfigException('Invalid encrypted data');
        }

        return unserialize($decrypted);
    }

    /**
     * Get the encryption secret key
     * 
     * @return string
     * @throws \yii\base\Exception
     */
    protected static function getSecretKey()
    {
        $key = Yii::$app->params['encryptionKey'] ?? null;

        if (!$key) {
            throw new \yii\base\Exception('Encryption key is not configured');
        }

        return $key;
    }
}
