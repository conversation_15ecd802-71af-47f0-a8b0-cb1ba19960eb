<?php

namespace common\models;

use Yii;
use common\components\EncryptDecryptID;
use yii\db\ActiveRecord;
use yii\helpers\Url;

class DocumentUpload extends ActiveRecord
{
    public $upload_file;

    public static function tableName()
    {
        return '{{%document_upload}}';
    }

    // public function rules()
    // {
    //     return [
    //         [['user_id'], 'required'],
    //         // [['original_filename', 'filename', 'file_path'], 'required', 'when' => function ($model) {
    //         //     return $model->upload_file !== null;
    //         // }, 'whenClient' => "function (attribute) { return $('#documentupload-upload_file').val() !== ''; }"],
    //         [['user_id', 'file_size', 'created_at', 'updated_at'], 'integer'],
    //         [['filename', 'original_filename', 'file_path'], 'string', 'max' => 255],
    //         [['file_type'], 'string', 'max' => 50],
    //         [['upload_file'], 'file', 'skipOnEmpty' => true, 'extensions' => ['pdf', 'doc', 'docx', 'txt'], 'maxSize' => 1024 * 1024 * 10], // 10MB max
    //         [['user_id'], 'exist', 'skipOnError' => true, 'targetClass' => User::class, 'targetAttribute' => ['user_id' => 'id']],
    //     ];
    // }

    public function rules()
    {
        return [
            [['upload_file'], 'file', 'skipOnEmpty' => true, 'extensions' => ['pdf', 'png', 'jpg', 'jpeg'], 'maxSize' => 1024 * 1024 * 10, 'maxFiles' => 10],
            [['user_id'], 'required'],
            [['user_id', 'file_size', 'created_at', 'updated_at'], 'integer'],
            [['filename', 'original_filename', 'file_path'], 'string', 'max' => 255],
            [['file_type'], 'string', 'max' => 50],
            [['user_id'], 'exist', 'skipOnError' => true, 'targetClass' => User::class, 'targetAttribute' => ['user_id' => 'id']],
        ];
    }

    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'user_id' => 'User ID',
            'filename' => 'Filename',
            'original_filename' => 'Original Filename',
            'file_path' => 'File Path',
            'file_type' => 'File Type',
            'file_size' => 'File Size',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'upload_file' => 'Upload Document',
        ];
    }

    public function getUser()
    {
        return $this->hasOne(User::class, ['id' => 'user_id']);
    }

    public function getEncryptedId()
    {
        return EncryptDecryptID::encryptId($this->id);
    }

    public function getServeUrl()
    {
        return Url::to(['/document-upload/serve-document', 'id' => $this->encryptedId]);
    }
}
