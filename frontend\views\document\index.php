<?php

use common\components\CreateNewButton;
use common\models\DocumentType;
use common\widgets\RecordSearchWidget;
use yii\helpers\Url;
use yii\helpers\Html;
use yii\bootstrap5\Modal;
use kartik\grid\GridView;
use yii2ajaxcrud\ajaxcrud\CrudAsset;
use yii2ajaxcrud\ajaxcrud\BulkButtonWidget;

/* @var $this yii\web\View */
/* @var $searchModel common\models\search\RapportSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Documenten';
// $this->params['breadcrumbs'][] = $this->title;

CrudAsset::register($this);

$documents = DocumentType::find()->all();

// $this->registerJs("
// $(document).on('click', '#open-template-modal', function(e) {
//     e.preventDefault();

//     // Check if content exists
//     const content = $('#template-links-content').html();
//     if (!content) {
//         console.error('No content found in #template-links-content');
//         return;
//     }

//     // Check if modal exists
//     if ($('#ajaxCrudModal').length === 0) {
//         console.error('Modal #ajaxCrudModal not found');
//         return;
//     }

//     // Inject content and show modal
//     $('#ajaxCrudModal .modal-body').html(content);

//     // Initialize modal if not already initialized
//     const modal = new bootstrap.Modal(document.getElementById('ajaxCrudModal'));
//     modal.show();
// });
// ");
?>
<div class="rapport-index">
    <div id="ajaxCrudDatatable">
        <?= GridView::widget([
            'id' => 'crud-datatable',
            'dataProvider' => $dataProvider,
            // 'filterModel' => $searchModel,
            'pjax' => true,
            'columns' => require(__DIR__ . '/_columns.php'),
            'toolbar' => [
                [
                    'content' =>
                    CreateNewButton::widget([])
                        .
                        Html::a(
                            '<i class="fa fa-redo"></i>',
                            [''],
                            ['data-pjax' => 1, 'class' => 'btn btn-outline-success ms-1', 'title' => Yii::t('yii2-ajaxcrud', 'Reset Grid')]
                        )
                ],
            ],
            'striped' => false,
            'hover' => true,
            'condensed' => true,
            'responsive' => true,
            'panel' => [
                'type' => 'default',
                'heading' => '<i class="fa fa-list"></i> <b>' . $this->title . '</b>',
                'before' => RecordSearchWidget::widget([
                    'id' => 'document-search',
                    'placeholder' => 'Search...',
                    'width' => 'w-75',
                    'paramName' => 'globalSearch'  // This will be converted to FunctionalitySearch[globalSearch]
                ]),
                'after' => BulkButtonWidget::widget([
                    'buttons' => Html::a(
                        '<i class="fa fa-trash"></i>&nbsp; ' . Yii::t('yii2-ajaxcrud', 'Delete All'),
                        ["bulkdelete"],
                        [
                            'class' => 'btn btn-danger btn-xs',
                            'role' => 'modal-remote-bulk',
                            'data-confirm' => false,
                            'data-method' => false, // for overide yii data api
                            'data-request-method' => 'post',
                            'data-confirm-title' => Yii::t('yii2-ajaxcrud', 'Delete'),
                            'data-confirm-message' => Yii::t('yii2-ajaxcrud', 'Delete Confirm')
                        ]
                    ),
                ]) .
                    '<div class="clearfix"></div>',
            ]
        ]) ?>
    </div>

    <!-- Links to create documents -->
    <!-- <div id="template-links-content" style="display:none;">
        <h2 class="text-center mb-3">Soorten documenten</h2>
        <div class="list-group">
            <?php foreach ($documents as $document): ?>
                <?= Html::a($document->type, ['document/create', 'document_id' => $document->id], [
                    'class' => 'list-group-item btn btn-outline-dark mt-1',
                    // 'class' => 'btn btn-outline-dark'
                    // 'target' => '_blank', // optional: open in new tab
                ]) ?>
            <?php endforeach; ?>
        </div>
    </div> -->
</div>
<?php Modal::begin([
    "id" => "ajaxCrudModal",
    "footer" => "", // always need it for jquery plugin
    'size' => Modal::SIZE_LARGE,
    "clientOptions" => [
        "tabindex" => false,
        "backdrop" => "static",
        "keyboard" => false,
    ],
    "options" => [
        "tabindex" => false
    ]
]) ?>

<!-- <div class="modal-body"></div> -->
<?php Modal::end(); ?>