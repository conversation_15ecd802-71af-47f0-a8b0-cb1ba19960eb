<?php

namespace backend\controllers;

use Yii;
use common\models\DocumentTypeVraag;
use common\models\search\DocumentTypeVraagSearch;
use yii\db\Transaction;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use \yii\web\Response;
use yii\helpers\Html;

/**
 * DocumenttypeVraagController implements the CRUD actions for DocumenttypeVraag model.
 */
class DocumenttypeVraagController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['post'],
                    'bulkdelete' => ['post'],
                ],
            ],
        ];
    }

    /**
     * Lists all DocumenttypeVraag models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new DocumenttypeVraagSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }


    /**
     * Displays a single DocumenttypeVraag model.
     * @param string $id
     * @return mixed
     */
    public function actionView($id)
    {
        $request = Yii::$app->request;
        if ($request->isAjax) {
            Yii::$app->response->format = Response::FORMAT_JSON;
            return [
                'title' => "DocumenttypeVraag #" . $id,
                'content' => $this->renderAjax('view', [
                    'model' => $this->findModel($id),
                ]),
                'footer' => Html::button(Yii::t('yii2-ajaxcrud', 'Close'), ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal']) .
                    Html::a(Yii::t('yii2-ajaxcrud', 'Update'), ['update', 'id' => $id], ['class' => 'btn btn-primary', 'role' => 'modal-remote'])
            ];
        } else {
            return $this->render('view', [
                'model' => $this->findModel($id),
            ]);
        }
    }

    /**
     * Creates a new DocumenttypeVraag model.
     * For ajax request will return json object
     * and for non-ajax request if creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $request = Yii::$app->request;
        $model = new DocumenttypeVraag();


        if ($request->isAjax) {
            Yii::$app->response->format = Response::FORMAT_JSON;

            if ($request->isGet) {
                return [
                    'title' => 'Create New Document Questions',
                    'content' => $this->renderAjax('create', [
                        'model' => $model,
                    ]),
                    'footer' => Html::button('Close', ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal']) .
                        Html::button('Save', ['class' => 'btn btn-primary', 'type' => 'submit'])
                ];
            }

            if ($request->isPost) {
                $postData = $request->post('DocumentTypeVraag', []); // Default to empty array if null
                $documentId = $postData['document_id'] ?? null; // Use null if missing

                $selectedQuestions = $request->post('Questions', []);
                $questionsOrder = $request->post('QuestionsOrder', []);

                // Check if required data is missing
                if (!$documentId) {
                    return [
                        'title' => 'Error!',
                        'content' => '<span class="text-danger">Document ID is missing!</span>',
                        'footer' => Html::button('Close', ['class' => 'btn btn-default', 'data-bs-dismiss' => 'modal'])
                    ];
                }

                $transaction = Yii::$app->db->beginTransaction(Transaction::SERIALIZABLE);
                try {
                    // Delete existing relationships
                    DocumenttypeVraag::deleteAll(['document_id' => $documentId]);

                    // Create new relationships
                    foreach ($selectedQuestions as $questionId) {
                        $documentVraag = new DocumenttypeVraag();
                        $documentVraag->document_id = $documentId;
                        $documentVraag->vraag_id = $questionId;
                        $documentVraag->vraag_nummer = isset($questionsOrder[$questionId]) ? $questionsOrder[$questionId] : 0;

                        if (!$documentVraag->save()) {
                            throw new \Exception('Failed to save question relationship: ' . json_encode($documentVraag->errors));
                        }
                    }

                    $transaction->commit();

                    return [
                        'forceReload' => '#crud-datatable-pjax',
                        'title' => 'Create Document Questions',
                        'content' => '<span class="text-success">Questions have been assigned successfully</span>',
                        'footer' => Html::button('Close', ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal']) .
                            Html::a('Create More', ['create'], ['class' => 'btn btn-primary', 'role' => 'modal-remote'])
                    ];
                } catch (\Exception $e) {
                    $transaction->rollBack();
                    return [
                        'title' => 'Create Document Questions',
                        'content' => '<span class="text-danger">Failed to save questions: ' . Html::encode($e->getMessage()) . '</span>',
                        'footer' => Html::button('Close', ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal'])
                    ];
                }
            }
        } else {
            // Handle non-ajax request
            if ($model->load($request->post())) {
                return $this->redirect(['view', 'id' => $model->id]);
            }
            return $this->render('create', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Updates an existing DocumenttypeVraag model.
     * For ajax request will return json object
     * and for non-ajax request if update is successful, the browser will be redirected to the 'view' page.
     * @param string $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $request = Yii::$app->request;
        $model = $this->findModel($id);

        if ($request->isAjax) {
            Yii::$app->response->format = Response::FORMAT_JSON;

            if ($request->isGet) {
                return [
                    'title' => "Update DocumenttypeVraag #" . $id,
                    'content' => $this->renderAjax('update', [
                        'model' => $model,
                    ]),
                    'footer' => Html::button('Close', ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal']) .
                        Html::button('Save', ['class' => 'btn btn-primary', 'type' => 'submit'])
                ];
            }

            if ($request->isPost) {
                $documentId = $model->document_id;
                $selectedQuestions = $request->post('Questions', []);
                $questionsOrder = $request->post('QuestionsOrder', []);

                $transaction = Yii::$app->db->beginTransaction(Transaction::SERIALIZABLE);
                try {
                    // Delete existing relationships for this document
                    DocumenttypeVraag::deleteAll(['document_id' => $documentId]);

                    // Create/update relationships for all selected questions
                    foreach ($selectedQuestions as $questionId) {
                        $documentVraag = new DocumenttypeVraag();
                        $documentVraag->document_id = $documentId;
                        $documentVraag->vraag_id = $questionId;
                        $documentVraag->vraag_nummer = isset($questionsOrder[$questionId]) ? $questionsOrder[$questionId] : 0;

                        if (!$documentVraag->save()) {
                            throw new \Exception('Failed to save question relationship: ' . json_encode($documentVraag->errors));
                        }
                    }

                    $transaction->commit();

                    return [
                        'forceReload' => '#crud-datatable-pjax',
                        'title' => "DocumenttypeVraag #" . $id,
                        'content' => '<span class="text-success">Questions have been updated successfully</span>',
                        'footer' => Html::button('Close', ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal'])
                    ];
                } catch (\Exception $e) {
                    $transaction->rollBack();
                    return [
                        'title' => "Update DocumenttypeVraag #" . $id,
                        'content' => '<span class="text-danger">Failed to update questions: ' . Html::encode($e->getMessage()) . '</span>',
                        'footer' => Html::button('Close', ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal'])
                    ];
                }
            }
        } else {
            if ($model->load($request->post())) {
                return $this->redirect(['view', 'id' => $model->id]);
            }
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Delete an existing DocumenttypeVraag model.
     * For ajax request will return json object
     * and for non-ajax request if deletion is successful, the browser will be redirected to the 'index' page.
     * @param string $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $request = Yii::$app->request;
        $this->findModel($id)->delete();

        if ($request->isAjax) {
            /*
            *   Process for ajax request
            */
            Yii::$app->response->format = Response::FORMAT_JSON;
            return ['forceClose' => true, 'forceReload' => '#crud-datatable-pjax'];
        } else {
            /*
            *   Process for non-ajax request
            */
            return $this->redirect(['index']);
        }
    }

    /**
     * Delete multiple existing DocumenttypeVraag model.
     * For ajax request will return json object
     * and for non-ajax request if deletion is successful, the browser will be redirected to the 'index' page.
     * @param string $id
     * @return mixed
     */
    public function actionBulkdelete()
    {
        $request = Yii::$app->request;
        $pks = explode(',', $request->post('pks')); // Array or selected records primary keys
        foreach ($pks as $pk) {
            $model = $this->findModel($pk);
            $model->delete();
        }

        if ($request->isAjax) {
            /*
            *   Process for ajax request
            */
            Yii::$app->response->format = Response::FORMAT_JSON;
            return ['forceClose' => true, 'forceReload' => '#crud-datatable-pjax'];
        } else {
            /*
            *   Process for non-ajax request
            */
            return $this->redirect(['index']);
        }
    }

    /**
     * Finds the DocumenttypeVraag model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param string $id
     * @return DocumenttypeVraag the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = DocumenttypeVraag::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
}
