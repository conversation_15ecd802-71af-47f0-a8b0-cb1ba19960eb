<?php

namespace common\models\search;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\Functionality;

/**
 * FunctionalitySearch represents the model behind the search form about `common\models\Functionality`.
 */
class FunctionalitySearch extends Functionality
{
    public $globalSearch;

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'created_by', 'updated_by'], 'integer'],
            [['name', 'description', 'controler', 'created_at', 'updated_at'], 'safe'],
            [['globalSearch'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = Functionality::find();

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // Add global search condition
        if (!empty($this->globalSearch)) {
            $query->andWhere([
                'or',
                ['like', 'name', $this->globalSearch],
                ['like', 'controller', $this->globalSearch],
                ['like', 'type', $this->globalSearch],
                ['like', 'description', $this->globalSearch],
                ['like', 'created_by', $this->globalSearch],
                ['like', 'updated_by', $this->globalSearch],
                // Add other fields you want to search
            ]);
        }

        $query->andFilterWhere([
            'id' => $this->id,
            'created_by' => $this->created_by,
            'updated_by' => $this->updated_by,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ]);

        $query->andFilterWhere(['like', 'name', $this->name])
            ->andFilterWhere(['like', 'description', $this->description])
            ->andFilterWhere(['like', 'controller', $this->controller]);

        return $dataProvider;
    }
}
