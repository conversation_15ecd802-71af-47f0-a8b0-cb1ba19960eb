<?php

namespace common\helpers;

use common\models\Notificatie;
use common\models\UserNotificatie;
use Yii;

class NotificationHelper
{
    /**
     * Get unread notifications count for current user
     * @return int
     */
    public static function getUnreadCount()
    {
        if (Yii::$app->user->isGuest) {
            return 0;
        }

        return UserNotificatie::find()
            ->where(['sent_to_user_id' => Yii::$app->user->id])
            ->andWhere(['is_read' => 0])
            ->count();
    }

    /**
     * Get recent notifications for current user
     * @param int $limit
     * @return array|\yii\db\ActiveRecord[]
     */
    public static function getRecentNotifications($limit = 5)
    {
        if (Yii::$app->user->isGuest) {
            return [];
        }

        return UserNotificatie::find()
            ->joinWith('notificatie')
            ->where(['sent_to_user_id' => Yii::$app->user->id])
            ->orderBy(['notificatie.created_at' => SORT_DESC])
            ->limit($limit)
            ->all();
    }

    /**
     * Mark notification as read
     * @param int $notificationId
     * @return bool
     */
    public static function markAsRead($notificationId)
    {
        if (Yii::$app->user->isGuest) {
            Yii::warning('Cannot mark notification as read: User is guest', 'notification');
            return false;
        }

        $notification = UserNotificatie::findOne([
            'id' => $notificationId,
            'sent_to_user_id' => Yii::$app->user->id
        ]);

        if ($notification) {
            Yii::info("Found notification ID: {$notificationId}, current is_read value: {$notification->is_read}", 'notification');
            $notification->is_read = 1;
            $result = $notification->save();

            if (!$result) {
                Yii::error('Failed to save notification: ' . json_encode($notification->getErrors()), 'notification');
            } else {
                Yii::info("Successfully marked notification ID: {$notificationId} as read", 'notification');
            }

            return $result;
        }

        Yii::warning("Notification not found with ID: {$notificationId} for user: " . Yii::$app->user->id, 'notification');
        return false;
    }

    /**
     * Get notification type icon
     * @param string $type
     * @return string
     */
    public static function getTypeIcon($type)
    {
        return match ($type) {
            'success' => 'check-circle',
            'warning' => 'alert-triangle',
            'danger' => 'alert-octagon',
            'info' => 'info',
            default => 'bell',
        };
    }

    /**
     * Get time elapsed string
     * @param string $datetime
     * @return string
     */
    public static function getTimeElapsed($datetime)
    {
        $now = new \DateTime();
        $ago = new \DateTime($datetime);
        $diff = $now->diff($ago);

        if ($diff->d > 0) {
            return $diff->d . ' days ago';
        }
        if ($diff->h > 0) {
            return $diff->h . ' hours ago';
        }
        if ($diff->i > 0) {
            return $diff->i . ' minutes ago';
        }
        return 'just now';
    }

    /**
     * Create a new notification and send it to specified users
     * @param string $title
     * @param string $message
     * @param string $type
     * @param array $userIds
     * @return bool
     */
    public static function createNotification($title, $message, $type, $userIds = [])
    {
        $notification = new Notificatie([
            'title' => $title,
            'message' => $message,
            'type' => $type,
            'created_at' => date('Y-m-d H:i:s'),
            'created_by' => Yii::$app->user->id,
        ]);

        if ($notification->save()) {
            foreach ($userIds as $userId) {
                $userNotification = new UserNotificatie([
                    'notificatie_id' => $notification->id,
                    'sent_to_user_id' => $userId,
                    'is_read' => 0,
                ]);
                $userNotification->save();
            }
            return true;
        }

        return false;
    }

    /**
     * Get notifications data for SSE update
     * @return array
     */
    public static function getNotificationsData()
    {
        if (Yii::$app->user->isGuest) {
            return [
                'unreadCount' => 0,
                'notifications' => [],
            ];
        }

        $notifications = self::getRecentNotifications();
        $unreadCount = self::getUnreadCount();

        $notificationsData = [];
        foreach ($notifications as $userNotification) {
            $notification = $userNotification->notificatie;
            $notificationsData[] = [
                'id' => $userNotification->id,
                'title' => $notification->title,
                'message' => $notification->message,
                'type' => $notification->type,
                'typeIcon' => self::getTypeIcon($notification->type),
                'createdAt' => self::getTimeElapsed($notification->created_at),
                'isRead' => (bool)$userNotification->is_read,
            ];
        }

        return [
            'unreadCount' => $unreadCount,
            'notifications' => $notificationsData,
        ];
    }
}
