<?php

use yii\db\Migration;

class m250415_170204_add_access_token_to_user_table extends Migration
{

    public function up()
    {
        $this->addColumn('{{%user}}', 'access_token', $this->string(64)->unique());
    }

    public function down()
    {
        $this->dropColumn('{{%user}}', 'access_token');
    }

    /**
     * {@inheritdoc}
     */
    public function safeUp() {}

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        echo "m250415_170204_add_access_token_to_user_table cannot be reverted.\n";

        return false;
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m250415_170204_add_access_token_to_user_table cannot be reverted.\n";

        return false;
    }
    */
}
