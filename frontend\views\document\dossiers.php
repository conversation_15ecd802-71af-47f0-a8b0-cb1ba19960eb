<?php

use yii\helpers\Html;

$this->title = "Dossiers";

$this->registerJsFile("https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js", ['depends' => [\yii\web\JqueryAsset::class]]);

$this->registerJs("
    $('#searchPerson').on('keyup', function() {
        var value = $(this).val().toLowerCase();
        $('.accordion-item').each(function() {
            var name = $(this).data('name');
            $(this).toggle(name.includes(value));
        });
    });
");
?>

<input type="search" id="searchPerson" class="form-control mb-3" placeholder="Search person folder...">

<div class="accordion" id="documentsAccordion">
    <?php $index = 0;
    foreach ($grouped as $person => $docs): ?>
        <div class="accordion-item" data-name="<?= strtolower($person) ?>">
            <h2 class="accordion-header" id="heading<?= $index ?>">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse<?= $index ?>">
                    📁 <?= Html::encode($person) ?>
                </button>
            </h2>
            <div id="collapse<?= $index ?>" class="accordion-collapse collapse" data-bs-parent="#documentsAccordion">
                <div class="accordion-body">
                    <p><strong>Documenten:</strong></p>
                    <ul class="list-group">
                        <?php foreach ($docs as $doc):
                            // Choose badge color based on source
                            $badgeClass = strtolower($doc['source']) === 'system' ? 'bg-primary text-primary bg-opacity-25' : 'bg-light text-muted';
                        ?>
                            <li class="list-group-item d-flex justify-content-between">
                                <span>
                                    <?= Html::a($doc['label'], $doc['url'], ['target' => '_blank']) ?>
                                    <span class="badge <?= $badgeClass ?> ms-1 rounded-pill" style="vertical-align: middle;">
                                        <?= ucfirst($doc['source']) ?>
                                    </span>
                                </span>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            </div>
        </div>
    <?php $index++;
    endforeach; ?>
</div>