<?php

namespace frontend\controllers;

use common\components\EncryptDecryptID;
use Yii;
use common\models\DocumentUpload;
use common\models\search\DocumentUploadSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\helpers\FileHelper;
use \yii\web\Response;
use yii\helpers\Html;
use yii\web\UploadedFile;

/**
 * DocumentUploadController implements the CRUD actions for DocumentUpload model.
 */
class DocumentUploadController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['post'],
                    'bulkdelete' => ['post'],
                    'ajax-upload' => ['post'], // <-- add this
                ],
            ],
        ];
    }

    /**
     * Lists all DocumentUpload models.
     * @return mixed
     */
    public function actionIndex()
    {
        $model = new DocumentUpload();
        $searchModel = new DocumentUploadSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'model' => $model,
        ]);
    }


    /**
     * Displays a single DocumentUpload model.
     * @param integer $id
     * @return mixed
     */
    // public function actionView($id)
    // {   
    //     $request = Yii::$app->request;
    //     if($request->isAjax)
    //     {
    //         Yii::$app->response->format = Response::FORMAT_JSON;
    //         return [
    //             'title' => "DocumentUpload #".$id,
    //             'content' =>$this->renderAjax('view', [
    //                 'model' => $this->findModel($id),
    //             ]),
    //             'footer' => Html::button(Yii::t('yii2-ajaxcrud', 'Close'), ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal']).
    //                 Html::a(Yii::t('yii2-ajaxcrud', 'Update'), ['update', 'id' => $id], ['class' => 'btn btn-primary', 'role' => 'modal-remote'])
    //         ];
    //     }
    //     else
    //     {
    //         return $this->render('view', [
    //             'model' => $this->findModel($id),
    //         ]);
    //     }
    // }

    /**
     * Creates a new DocumentUpload model.
     * For ajax request will return json object
     * and for non-ajax request if creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $request = Yii::$app->request;
        $model = new DocumentUpload();

        if ($request->isAjax) {
            /*
            *   Process for ajax request
            */
            Yii::$app->response->format = Response::FORMAT_JSON;
            if ($request->isGet) {
                return [
                    'title' => Yii::t('yii2-ajaxcrud', 'Create New') . " DocumentUpload",
                    'content' => $this->renderAjax('create', [
                        'model' => $model,
                    ]),
                    'footer' => Html::button(Yii::t('yii2-ajaxcrud', 'Close'), ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal']) .
                        Html::button(Yii::t('yii2-ajaxcrud', 'Create'), ['class' => 'btn btn-primary', 'type' => 'submit'])
                ];
            } else if ($model->load($request->post()) && $model->save()) {
                return [
                    'forceReload' => '#crud-datatable-pjax',
                    'title' => Yii::t('yii2-ajaxcrud', 'Create New') . " DocumentUpload",
                    'content' => '<span class="text-success">' . Yii::t('yii2-ajaxcrud', 'Create') . ' DocumentUpload ' . Yii::t('yii2-ajaxcrud', 'Success') . '</span>',
                    'footer' =>  Html::button(Yii::t('yii2-ajaxcrud', 'Close'), ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal']) .
                        Html::a(Yii::t('yii2-ajaxcrud', 'Create More'), ['create'], ['class' => 'btn btn-primary', 'role' => 'modal-remote'])
                ];
            } else {
                return [
                    'title' => Yii::t('yii2-ajaxcrud', 'Create New') . " DocumentUpload",
                    'content' => $this->renderAjax('create', [
                        'model' => $model,
                    ]),
                    'footer' => Html::button(Yii::t('yii2-ajaxcrud', 'Close'), ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal']) .
                        Html::button(Yii::t('yii2-ajaxcrud', 'Save'), ['class' => 'btn btn-primary', 'type' => 'submit'])
                ];
            }
        } else {
            /*
            *   Process for non-ajax request
            */
            if ($model->load($request->post()) && $model->save()) {
                return $this->redirect(['view', 'id' => $model->id]);
            } else {
                return $this->render('create', [
                    'model' => $model,
                ]);
            }
        }
    }

    /**
     * Updates an existing DocumentUpload model.
     * For ajax request will return json object
     * and for non-ajax request if update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $request = Yii::$app->request;
        $model = $this->findModel($id);

        if ($request->isAjax) {
            /*
            *   Process for ajax request
            */
            Yii::$app->response->format = Response::FORMAT_JSON;
            if ($request->isGet) {
                return [
                    'title' => Yii::t('yii2-ajaxcrud', 'Update') . " DocumentUpload #" . $id,
                    'content' => $this->renderAjax('update', [
                        'model' => $model,
                    ]),
                    'footer' => Html::button(Yii::t('yii2-ajaxcrud', 'Close'), ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal']) .
                        Html::button(Yii::t('yii2-ajaxcrud', 'Save'), ['class' => 'btn btn-primary', 'type' => 'submit'])
                ];
            } else if ($model->load($request->post()) && $model->save()) {
                return [
                    'forceReload' => '#crud-datatable-pjax',
                    'title' => "DocumentUpload #" . $id,
                    'content' => $this->renderAjax('view', [
                        'model' => $model,
                    ]),
                    'footer' => Html::button(Yii::t('yii2-ajaxcrud', 'Close'), ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal']) .
                        Html::a(Yii::t('yii2-ajaxcrud', 'Update'), ['update', 'id' => $id], ['class' => 'btn btn-primary', 'role' => 'modal-remote'])
                ];
            } else {
                return [
                    'title' => Yii::t('yii2-ajaxcrud', 'Update') . " DocumentUpload #" . $id,
                    'content' => $this->renderAjax('update', [
                        'model' => $model,
                    ]),
                    'footer' => Html::button(Yii::t('yii2-ajaxcrud', 'Close'), ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal']) .
                        Html::button(Yii::t('yii2-ajaxcrud', 'Save'), ['class' => 'btn btn-primary', 'type' => 'submit'])
                ];
            }
        } else {
            /*
            *   Process for non-ajax request
            */
            if ($model->load($request->post()) && $model->save()) {
                return $this->redirect(['view', 'id' => $model->id]);
            } else {
                return $this->render('update', [
                    'model' => $model,
                ]);
            }
        }
    }

    /**
     * Delete an existing DocumentUpload model.
     * For ajax request will return json object
     * and for non-ajax request if deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    // public function actionDelete($id)
    // {
    //     $request = Yii::$app->request;
    //     $this->findModel($id)->delete();

    //     if ($request->isAjax) {
    //         /*
    //         *   Process for ajax request
    //         */
    //         Yii::$app->response->format = Response::FORMAT_JSON;
    //         return ['forceClose' => true, 'forceReload' => '#crud-datatable-pjax'];
    //     } else {
    //         /*
    //         *   Process for non-ajax request
    //         */
    //         return $this->redirect(['index']);
    //     }
    // }

    /**
     * Delete multiple existing DocumentUpload model.
     * For ajax request will return json object
     * and for non-ajax request if deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionBulkdelete()
    {
        $request = Yii::$app->request;
        $pks = explode(',', $request->post('pks')); // Array or selected records primary keys
        foreach ($pks as $pk) {
            $model = $this->findModel($pk);
            $model->delete();
        }

        if ($request->isAjax) {
            /*
            *   Process for ajax request
            */
            Yii::$app->response->format = Response::FORMAT_JSON;
            return ['forceClose' => true, 'forceReload' => '#crud-datatable-pjax'];
        } else {
            /*
            *   Process for non-ajax request
            */
            return $this->redirect(['index']);
        }
    }

    /**
     * Finds the DocumentUpload model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return DocumentUpload the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = DocumentUpload::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    public function actionUpload()
    {
        $model = new DocumentUpload();

        if (Yii::$app->request->isPost) {
            $customNames = Yii::$app->request->post('custom_names', []);
            $files = UploadedFile::getInstances($model, 'upload_file');
            $userId = Yii::$app->user->id;

            $publicPath = Yii::getAlias('@frontend/web/uploads/documents/');
            if (!file_exists($publicPath)) {
                mkdir($publicPath, 0775, true);
            }

            foreach ($files as $index => $file) {
                $customBaseName = $customNames[$index] ?? $file->baseName;
                $customFileName = 'uploaded_' . preg_replace('/[^A-Za-z0-9_\-]/', '_', $customBaseName) . '.' . $file->extension;
                $fullPath = $publicPath . $customFileName;
                $relativePath = 'uploads/documents/' . $customFileName;

                if ($file->saveAs($fullPath)) {
                    chmod($fullPath, 0644);
                    $upload = new DocumentUpload();
                    $upload->user_id = $userId;
                    $upload->upload_file = $file;
                    $upload->original_filename = $file->name;
                    $upload->filename = $customFileName;
                    $upload->file_path = $relativePath;
                    $upload->file_type = $file->type;
                    $upload->file_size = $file->size;
                    $upload->created_at = time();
                    $upload->updated_at = time();
                    $upload->save(false); // You can use validate() if needed
                }
            }

            Yii::$app->session->setFlash('success', 'Documents uploaded successfully.');
            return $this->redirect(['index']);
        }

        return $this->render('upload', [
            'model' => $model,
        ]);
    }

    // public function actionAjaxUpload()
    // {
    //     $model = new DocumentUpload();

    //     if (Yii::$app->request->isPost) {
    //         $model->upload_file = UploadedFile::getInstance($model, 'upload_file');

    //         if ($model->upload_file) {
    //             // Assume you have related $document model or use a temp name
    //             $userId = Yii::$app->user->id;
    //             $documentName = 'Uploaded_' . date('Y-m-d_H-i-s'); // Or fetch from related Document model
    //             $extension = $model->upload_file->extension;
    //             $filename = $documentName . '.' . $extension;

    //             // 📁 Save to the same frontend folder
    //             $publicPath = Yii::getAlias('@frontend/web/uploads/documents/');
    //             if (!file_exists($publicPath)) {
    //                 mkdir($publicPath, 0775, true);
    //             }

    //             $fullPath = $publicPath . $filename;
    //             $relativePath = 'uploads/documents/' . $filename;

    //             if ($model->upload_file->saveAs($fullPath)) {
    //                 // ✅ Save metadata
    //                 $model->user_id = $userId;
    //                 $model->original_filename = $model->upload_file->name;
    //                 $model->filename = $filename;
    //                 $model->file_path = $relativePath;
    //                 $model->file_type = $model->upload_file->type;
    //                 $model->file_size = $model->upload_file->size;
    //                 $model->created_at = time();
    //                 $model->updated_at = time();

    //                 if ($model->save(false)) {
    //                     Yii::$app->session->setFlash('success', 'Document uploaded successfully.');
    //                     return $this->redirect(['view', 'id' => $model->id]);
    //                 }
    //             }
    //         }
    //     }

    //     return $this->render('upload', [
    //         'model' => $model,
    //     ]);
    // }

    public function actionView($id)
    {
        $model = DocumentUpload::findOne($id);
        if (!$model) {
            throw new NotFoundHttpException('Document not found.');
        }

        return $this->render('view', ['model' => $model]);
    }

    // public function actionDownload($id)
    // {
    //     $model = DocumentUpload::findOne($id);
    //     if (!$model || !file_exists(Yii::getAlias('@frontend/web') . $model->file_path)) {
    //         throw new NotFoundHttpException('File not found.');
    //     }

    //     return Yii::$app->response->sendFile(
    //         Yii::getAlias('@frontend/web') . $model->file_path,
    //         $model->original_filename
    //     );
    // }

    public function actionServeDocument($id)
    {
        try {
            $decryptedId = EncryptDecryptID::decryptId($id);
        } catch (\Exception $e) {
            throw new NotFoundHttpException('Invalid document ID.');
        }

        $document = DocumentUpload::findOne($decryptedId);

        if (!$document) {
            throw new NotFoundHttpException('Document not found.');
        }

        $publicPath = Yii::getAlias('@frontend/web/') . $document->file_path;
        $filename = $document->original_filename;

        if (!file_exists($publicPath)) {
            throw new NotFoundHttpException('File not found on server.');
        }

        // Stream the file
        return Yii::$app->response->sendFile(
            $publicPath,
            $filename,
            ['inline' => true]
        );
    }

    public function actionDelete($id)
    {
        try {
            $decryptedId = EncryptDecryptID::decryptId($id);
        } catch (\Exception $e) {
            throw new NotFoundHttpException('Invalid document ID.');
        }

        $model = $this->findModel($decryptedId);
        $filePath = Yii::getAlias('@frontend/web/') . $model->file_path;

        // Delete file from filesystem
        if (file_exists($filePath)) {
            unlink($filePath);
        }

        // Delete record from database
        $model->delete();

        if (Yii::$app->request->isAjax) {
            Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
            return ['success' => true];
        }

        return $this->redirect(['index']);
    }
}
