<?php

namespace common\models\search;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\Documenttype;

/**
 * DocumenttypeSearch represents the model behind the search form about `common\models\Documenttype`.
 */
class DocumentTypeSearch extends DocumentType
{
    public $globalSearch;

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'created_by', 'updated_by'], 'integer'],
            [['type', 'created_at', 'updated_at'], 'safe'],
            [['globalSearch'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = Documenttype::find()
            ->alias('dn')  // Changed alias to be more specific
            ->joinWith(['createdBy cb', 'updatedBy ub']);

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => [
                'defaultOrder' => [
                    'id' => SORT_DESC
                ]
            ],
        ]);

        $this->load($params);

        if (!$this->validate()) {
            return $dataProvider;
        }

        // Add global search condition
        if (!empty($this->globalSearch)) {
            $query->andWhere([
                'or',
                ['like', 'dn.type', $this->globalSearch],
                ['like', 'cb.username', $this->globalSearch],
                ['like', 'ub.username', $this->globalSearch],
                ['like', 'dn.id', $this->globalSearch],  // Added ID search
            ]);
        }

        // Grid filtering conditions
        $query->andFilterWhere([
            'dn.id' => $this->id,
            'dn.created_at' => $this->created_at,
            'dn.updated_at' => $this->updated_at,
            'dn.created_by' => $this->created_by,
            'dn.updated_by' => $this->updated_by,
        ]);

        $query->andFilterWhere(['like', 'dn.type', $this->type]);

        return $dataProvider;
    }
}
