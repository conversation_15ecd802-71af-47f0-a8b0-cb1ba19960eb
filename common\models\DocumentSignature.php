<?php

namespace common\models;

use Yii;
use yii\behaviors\TimestampBehavior;
use yii\db\Expression;

/**
 * This is the model class for table "document_signature".
 *
 * @property int $id
 * @property int $document_id
 * @property int $user_id
 * @property string $signature_base64
 * @property string|null $created_at
 * @property string|null $signed_at
 * @property string|null $updated_at
 * @property int|null $version
 * @property string|null $ip_address
 * @property string|null $user_agent
 *
 * @property Document $document
 * @property User $user
 */
class DocumentSignature extends \yii\db\ActiveRecord
{


    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'document_signature';
    }

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'createdAtAttribute' => 'signed_at',
                'updatedAtAttribute' => 'updated_at',
                'value' => new Expression('CURRENT_TIMESTAMP'),
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['signed_at', 'ip_address', 'user_agent'], 'default', 'value' => null],
            [['version'], 'default', 'value' => 1],
            [['document_id', 'user_id', 'signature_base64'], 'required'],
            [['document_id', 'user_id', 'version'], 'integer'],
            [['signature_base64'], 'string'],
            [['created_at', 'signed_at', 'updated_at'], 'safe'],
            [['ip_address'], 'string', 'max' => 45],
            [['user_agent'], 'string', 'max' => 255],
            [['document_id'], 'exist', 'skipOnError' => true, 'targetClass' => Document::class, 'targetAttribute' => ['document_id' => 'id']],
            [['user_id'], 'exist', 'skipOnError' => true, 'targetClass' => User::class, 'targetAttribute' => ['user_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'document_id' => 'Document ID',
            'user_id' => 'User ID',
            'signature_base64' => 'Signature Base64',
            'created_at' => 'Created At',
            'signed_at' => 'Signed At',
            'updated_at' => 'Updated At',
            'version' => 'Version',
            'ip_address' => 'Ip Address',
            'user_agent' => 'User Agent',
        ];
    }

    /**
     * Gets query for [[Document]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getDocument()
    {
        return $this->hasOne(Document::class, ['id' => 'document_id']);
    }

    /**
     * Gets query for [[User]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUser()
    {
        return $this->hasOne(User::class, ['id' => 'user_id']);
    }
}
