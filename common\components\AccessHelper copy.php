<?php

namespace common\components;

use Yii;
use yii\base\Component;
use common\models\RoleFunctionality;
use common\models\UserPermission;

class AccessHelperCopy extends Component
{
    private static function getCurrentRoleId()
    {
        $user = Yii::$app->user->identity;
        return $user ? $user : null;
    }

    public function canAccess($route)
    {
        $user = self::getCurrentRoleId();

        // 1. Check user-specific permission override
        $userPermission = UserPermission::find()
            ->where(['user_id' => $user->id, 'route' => $route])
            ->one();

        if (!empty($userPermission)) {
            return (bool) $userPermission['can_access'];
        }

        // 2. Fall back to role permission
        return RoleFunctionality::find()
            ->where(['role_id' => $user->role_id])
            ->andWhere(['route' => $route])
            ->andWhere(['active' => 1])
            ->exists();
    }

    public static function buildActionTemplate()
    {
        $user = self::getCurrentRoleId();

        $routes = RoleFunctionality::find()
            ->where([
                'role_id' => $user->role_id,
                'active' => 1,
            ])
            ->select('route')
            ->asArray()
            ->column();

        $controller = Yii::$app->controller->id;
        $access = Yii::$app->accessHelper;

        $template = '';

        foreach ($routes as $route) {
            // Extract action from route
            $action = str_replace('/', '', str_replace($controller . '/', '', $route));
            if ($access->canAccess($route)) {
                $template .= "{" . $action . "} ";
            }
        }

        return trim($template);
    }
}
