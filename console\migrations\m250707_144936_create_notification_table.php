<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%notification}}`.
 */
class m250707_144936_create_notification_table extends Migration
{
    public function safeUp()
    {
        $this->createTable('{{%notification}}', [
            'id' => $this->primaryKey(),
            'key' => $this->string()->notNull()->unique(), // used for trigger mapping
            'title' => $this->string()->notNull(),
            'message_template' => $this->text()->notNull(), // e.g. "User {username} created a report."
            'enabled' => $this->boolean()->defaultValue(true),
            'send_email' => $this->boolean()->defaultValue(false),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
        ]);
    }

    public function safeDown()
    {
        $this->dropTable('{{%notification}}');
    }
}
