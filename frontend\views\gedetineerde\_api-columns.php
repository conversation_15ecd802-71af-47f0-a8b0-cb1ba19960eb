<?php

use yii\helpers\Url;

$columns = [
    [
        'class' => 'kartik\grid\CheckboxColumn',
        'width' => '20px',
        'checkboxOptions' => function ($model) {
            // Determine the correct value to use for the checkbox
            $value = isset($model['persoonid']) ? $model['persoonid'] : (isset($model['pk']) ? $model['pk'] : (isset($model['id']) ? $model['id'] : 'unknown'));

            return [
                'value' => $value,
            ];
        },
    ],
    [
        'class' => 'kartik\grid\SerialColumn',
        'width' => '30px',
    ],
];

// Get the first item from the data to determine the columns
if (!empty($gedetineerdeData)) {
    $firstItem = reset($gedetineerdeData);
    foreach ($firstItem as $key => $value) {
        // Skip adding column if the value is an array or object
        if (!is_array($value) && !is_object($value)) {
            $columns[] = [
                'attribute' => $key,
                'label' => ucfirst($key), // Capitalize the first letter of the key for the label
                'format' => 'raw',
                'value' => function ($model) use ($key) {
                    return is_string($model[$key]) ? $model[$key] : json_encode($model[$key]);
                },
            ];
        }
    }
}

// Add the action column at the end
$columns[] = [
    'class' => 'kartik\grid\ActionColumn',
    'dropdown' => false,
    'noWrap' => 'true',
    'template' => '{view}',
    'vAlign' => 'middle',
    'urlCreator' => function ($action, $model, $key, $index) {
        // Determine the correct ID to use
        $id = isset($model['persoonid']) ? $model['persoonid'] : (isset($model['id']) ? $model['id'] : (isset($model['pk']) ? $model['pk'] : $key));

        // For API records, always use the view action with source=api parameter
        if ($action === 'view') {
            return Url::to(['view', 'id' => $id, 'source' => 'api']);
        }

        // For other actions, use the same pattern
        return Url::to([$action, 'id' => $id, 'source' => 'api']);
    },
    'viewOptions' => ['role' => 'modal-remote', 'title' => Yii::t('yii2-ajaxcrud', 'View'), 'data-toggle' => 'tooltip', 'class' => 'btn btn-sm btn-outline-success'],
];

return $columns;
