<?php

use kartik\form\ActiveForm;
use kartik\select2\Select2;
use kartik\switchinput\SwitchInput;
use yii\helpers\Html;

/* @var $this yii\web\View */
/* @var $model common\models\Notification */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="notification-form">

    <?php $form = ActiveForm::begin(); ?>

    <?= $form->field($model, 'key')->textInput(['placeholder' => 'e.g. document/create']); ?>

    <?= $form->field($model, 'title')->textInput(['placeholder' => 'Notification Title']); ?>

    <?= $form->field($model, 'message_template')->textarea(['rows' => 4, 'placeholder' => 'e.g. User {username} created a report.']); ?>

    <?= $form->field($model, 'channels')->widget(Select2::class, [
        'data' => [
            'database' => 'Database',
            'email' => 'Email',
        ],
        'options' => ['placeholder' => 'Select channels...', 'multiple' => true],
        'pluginOptions' => ['allowClear' => true],
    ]); ?>

    <div class="row">
        <div class="col-md-6">
            <?= $form->field($model, 'enabled')->widget(SwitchInput::classname(), [
                'type' => SwitchInput::CHECKBOX,
            ]) ?>
        </div>
        <div class="col-md-6">
            <?= $form->field($model, 'send_email')->widget(SwitchInput::classname(), [
                'type' => SwitchInput::CHECKBOX,
            ]) ?>
        </div>
    </div>
    <?php if (!Yii::$app->request->isAjax) { ?>
        <div class="form-group">
            <?= Html::submitButton($model->isNewRecord ? 'Create' : 'Update', ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']) ?>
        </div>
    <?php } ?>

    <?php ActiveForm::end(); ?>

</div>