<?php

namespace common\components;

use Yii;
use yii\httpclient\Client;

class BaseApiServiceCopy
{
    protected $baseUrl;
    protected $token;

    public function __construct($baseUrl, $token)
    {
        $this->baseUrl = $baseUrl;
        $this->token = $token;
    }

    protected function request($method, $endpoint, $data = [])
    {
        $client = new Client(['baseUrl' => $this->baseUrl]);

        $request = $client->createRequest()
            ->setMethod($method)
            ->setUrl($endpoint)
            ->addHeaders([
                'Authorization' => 'Bearer ' . $this->token,
                'Accept' => 'application/json'
            ]);

        if (in_array(strtoupper($method), ['POST', 'PUT', 'PATCH'])) {
            $request->setFormat(Client::FORMAT_JSON)
                ->setData($data);
        } else {
            $request->setData($data);
        }

        $response = $request->send();

        if ($response->isOk) {
            return $response->data;
        }

        Yii::error("API Request failed: {$method} {$endpoint} - " . $response->statusCode, __METHOD__);
        return null;
    }

    public function get($endpoint, $params = [])
    {
        return $this->request('GET', $endpoint, $params);
    }

    public function post($endpoint, $data = [])
    {
        return $this->request('POST', $endpoint, $data);
    }

    public function put($endpoint, $data = [])
    {
        return $this->request('PUT', $endpoint, $data);
    }

    public function delete($endpoint, $params = [])
    {
        return $this->request('DELETE', $endpoint, $params);
    }
}
