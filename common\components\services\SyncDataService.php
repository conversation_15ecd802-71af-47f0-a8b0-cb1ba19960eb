<?php

namespace common\components\services;

use common\components\BaseApiService;

class SyncDataService extends BaseApiService
{
    public function __construct()
    {
        parent::__construct(
            \Yii::$app->params['apiBaseUrl'],
            \Yii::$app->params['sync_data.token']
        );
    }

    public function getData()
    {
        return $this->get('sync?synced=0');
    }

    public function updateOneRecord($id)
    {
        try {
            // First get the record to ensure it exists
            $record = $this->get("sync?pk={$id}");
            if (!$record) {
                \Yii::warning("Sync record with pk {$id} not found", __METHOD__);
                return false;
            }

            // Update the sync status
            $result = $this->updateRecord($id, ['synced' => 1]);
            return $result !== null;
        } catch (\Exception $e) {
            \Yii::error("Failed to update sync record {$id}: " . $e->getMessage(), __METHOD__);
            return false;
        }
    }

    public function updateRecord($id, $data)
    {
        try {
            // Merge pk into the request body
            $payload = array_merge(['pk' => $id], $data);
            return $this->put("sync", $payload);
        } catch (\Exception $e) {
            \Yii::error("Failed to update record {$id}: " . $e->getMessage(), __METHOD__);
            return null;
        }
    }

    /**
     * Update multiple records in batch
     * @param array $ids Array of record IDs to update
     * @return array Results with success/failure status for each ID
     */
    public function updateMultipleRecords($ids)
    {
        $results = [];

        foreach ($ids as $id) {
            $results[$id] = $this->updateOneRecord($id);
        }

        return $results;
    }
}
