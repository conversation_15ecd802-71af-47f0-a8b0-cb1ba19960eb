{"name": "yiisoft/yii2-app-advanced", "description": "Yii 2 Advanced Project Template", "keywords": ["yii2", "framework", "advanced", "project template"], "homepage": "https://www.yiiframework.com/", "type": "project", "license": "BSD-3-<PERSON><PERSON>", "support": {"issues": "https://github.com/yiisoft/yii2/issues?state=open", "forum": "https://www.yiiframework.com/forum/", "wiki": "https://www.yiiframework.com/wiki/", "irc": "ircs://irc.libera.chat:6697/yii", "source": "https://github.com/yiisoft/yii2"}, "minimum-stability": "stable", "require": {"php": ">=7.4.0", "yiisoft/yii2": "~2.0.45", "yiisoft/yii2-bootstrap5": "~2.0.2", "yiisoft/yii2-symfonymailer": "~2.0.3", "hail812/yii2-adminlte3": "~1.1", "kartik-v/yii2-widgets": "*", "kartik-v/yii2-icons": "*", "kartik-v/yii2-checkbox-x": "*", "kartik-v/yii2-datecontrol": "*", "kartik-v/yii2-dialog": "*", "kartik-v/yii2-dynagrid": "*", "kartik-v/yii2-editable": "*", "kartik-v/yii2-export": "*", "kartik-v/yii2-field-range": "*", "kartik-v/yii2-helpers": "*", "kartik-v/yii2-money": "*", "kartik-v/yii2-mpdf": "*", "kartik-v/yii2-tabs-x": "*", "kartik-v/yii2-nav-x": "*", "kartik-v/yii2-context-menu": "*", "kartik-v/yii2-builder": "*", "kartik-v/yii2-slider": "*", "kartik-v/yii2-password": "*", "kartik-v/yii2-sortable-input": "*", "kartik-v/yii2-social": "*", "kartik-v/yii2-markdown": "*", "kartik-v/yii2-ipinfo": "*", "kartik-v/yii2-label-inplace": "*", "kartik-v/yii2-validators": "*", "kartik-v/yii2-date-range": "*", "kartik-v/yii2-detail-view": "*", "kartik-v/yii2-widget-activeform": "dev-master", "kartik-v/yii2-widget-growl": "*", "biladina/yii2-ajaxcrud-bs4": "~3.0", "kartik-v/yii2-widget-select2": "dev-master", "raoul2000/yii2-workflow": "^1.2", "kartik-v/yii2-sortable": "^1.2", "bower-asset/sweetalert2": "^11.7", "cornernote/yii2-workflow-manager": "*", "2amigos/yii2-ckeditor-widget": "^2.1", "yiisoft/yii2-twig": "^2.5", "phpoffice/phpspreadsheet": "^4.4", "bedezign/yii2-audit": "^1.0", "kartik-v/yii2-widget-fileinput": "dev-master", "rmrevin/yii2-fontawesome": "2.10.*", "edofre/yii2-fullcalendar": "V1.0.11", "kartik-v/yii2-widget-timepicker": "dev-master", "miloschuman/yii2-highcharts-widget": "^11.0"}, "require-dev": {"yiisoft/yii2-debug": "~2.1.0", "yiisoft/yii2-gii": "~2.2.0", "yiisoft/yii2-faker": "~2.0.0", "phpunit/phpunit": "~9.5.0", "codeception/codeception": "^5.0.0 || ^4.0", "codeception/lib-innerbrowser": "^4.0 || ^3.0 || ^1.1", "codeception/module-asserts": "^3.0 || ^1.1", "codeception/module-yii2": "^1.1", "codeception/module-filesystem": "^3.0 || ^2.0 || ^1.1", "codeception/verify": "^3.0 || ^2.2"}, "autoload-dev": {"psr-4": {"common\\tests\\": ["common/tests/", "common/tests/_support"], "backend\\tests\\": ["backend/tests/", "backend/tests/_support"], "frontend\\tests\\": ["frontend/tests/", "frontend/tests/_support"]}}, "config": {"allow-plugins": {"yiisoft/yii2-composer": true}, "process-timeout": 1800, "fxp-asset": {"enabled": false}}, "repositories": [{"type": "composer", "url": "https://asset-packagist.org"}]}