<?php

namespace common\widgets;

use Yii;
use yii\base\Widget;
use yii\helpers\Html;

class ProfileWidget extends Widget
{
    public $options = [];

    public function init()
    {
        parent::init();
    }

    public function run()
    {
        if (Yii::$app->user->isGuest) {
            return Html::tag(
                'li',
                Html::a(
                    '<i data-lucide="log-in" class="align-middle" style="width: 16px; height: 16px;"></i>',
                    ['/site/login'],
                    ['class' => 'nav-link']
                ),
                ['class' => 'nav-item']
            );
        }

        return $this->render('profile', [
            'user' => Yii::$app->user->identity,
            'options' => $this->options
        ]);
    }
}
