<?php

namespace console\controllers;

use Yii;
use yii\console\Controller;
use yii\console\ExitCode;
use common\models\Notification;
use common\models\NotificationTrigger;
use common\models\NotificationRole;
use common\models\Role;
use common\components\NotificationManager;

/**
 * Console controller for managing notifications
 */
class NotificationController extends Controller
{
    /**
     * Lists all configured notifications
     */
    public function actionList()
    {
        $notifications = Notification::find()->with(['notificationRoles.role'])->all();

        if (empty($notifications)) {
            $this->stdout("No notifications configured.\n");
            return ExitCode::OK;
        }

        $this->stdout("Configured Notifications:\n");
        $this->stdout(str_repeat("-", 80) . "\n");

        foreach ($notifications as $notification) {
            $this->stdout("ID: {$notification->id}\n");
            $this->stdout("Key: {$notification->key}\n");
            $this->stdout("Title: {$notification->title}\n");
            $this->stdout("Enabled: " . ($notification->enabled ? 'Yes' : 'No') . "\n");
            $this->stdout("Send Email: " . ($notification->send_email ? 'Yes' : 'No') . "\n");

            $roles = [];
            foreach ($notification->notificationRoles as $notificationRole) {
                if ($notificationRole->role) {
                    $roles[] = $notificationRole->role->name;
                }
            }
            $this->stdout("Roles: " . (empty($roles) ? 'None assigned' : implode(', ', $roles)) . "\n");
            $this->stdout("Message: {$notification->message_template}\n");
            $this->stdout(str_repeat("-", 80) . "\n");
        }

        return ExitCode::OK;
    }

    /**
     * Lists all configured notification triggers
     */
    public function actionListTriggers()
    {
        $triggers = NotificationTrigger::find()->all();

        if (empty($triggers)) {
            $this->stdout("No notification triggers configured.\n");
            return ExitCode::OK;
        }

        $this->stdout("Configured Notification Triggers:\n");
        $this->stdout(str_repeat("-", 80) . "\n");

        foreach ($triggers as $trigger) {
            $this->stdout("ID: {$trigger->id}\n");
            $this->stdout("Route: {$trigger->route}\n");
            $this->stdout("Notification Key: {$trigger->notification_key}\n");
            $this->stdout("Trigger Type: {$trigger->trigger_type}\n");
            $this->stdout("Model Class: {$trigger->model_class}\n");
            $this->stdout("Model ID Param: {$trigger->model_id_param}\n");
            $this->stdout("Fields: {$trigger->fields}\n");
            $this->stdout("Link Template: {$trigger->link_template}\n");
            $this->stdout(str_repeat("-", 80) . "\n");
        }

        return ExitCode::OK;
    }

    /**
     * Enables email notifications for a notification key
     * @param string $key The notification key
     */
    public function actionEnableEmail($key)
    {
        $notification = Notification::findOne(['key' => $key]);
        if (!$notification) {
            $this->stderr("Notification with key '{$key}' not found.\n");
            return ExitCode::DATAERR;
        }

        $notification->send_email = 1;
        if ($notification->save()) {
            $this->stdout("Email notifications enabled for '{$key}'.\n");
            return ExitCode::OK;
        } else {
            $this->stderr("Failed to enable email notifications: " . json_encode($notification->errors) . "\n");
            return ExitCode::DATAERR;
        }
    }

    /**
     * Disables email notifications for a notification key
     * @param string $key The notification key
     */
    public function actionDisableEmail($key)
    {
        $notification = Notification::findOne(['key' => $key]);
        if (!$notification) {
            $this->stderr("Notification with key '{$key}' not found.\n");
            return ExitCode::DATAERR;
        }

        $notification->send_email = 0;
        if ($notification->save()) {
            $this->stdout("Email notifications disabled for '{$key}'.\n");
            return ExitCode::OK;
        } else {
            $this->stderr("Failed to disable email notifications: " . json_encode($notification->errors) . "\n");
            return ExitCode::DATAERR;
        }
    }

    /**
     * Assigns a role to a notification
     * @param string $notificationKey The notification key
     * @param string $roleName The role name
     */
    public function actionAssignRole($notificationKey, $roleName)
    {
        $notification = Notification::findOne(['key' => $notificationKey]);
        if (!$notification) {
            $this->stderr("Notification with key '{$notificationKey}' not found.\n");
            return ExitCode::DATAERR;
        }

        $role = Role::findOne(['name' => $roleName]);
        if (!$role) {
            $this->stderr("Role '{$roleName}' not found.\n");
            return ExitCode::DATAERR;
        }

        $existing = NotificationRole::findOne([
            'notification_id' => $notification->id,
            'role_id' => $role->id
        ]);

        if ($existing) {
            $this->stdout("Role '{$roleName}' is already assigned to notification '{$notificationKey}'.\n");
            return ExitCode::OK;
        }

        $notificationRole = new NotificationRole([
            'notification_id' => $notification->id,
            'role_id' => $role->id
        ]);

        if ($notificationRole->save()) {
            $this->stdout("Role '{$roleName}' assigned to notification '{$notificationKey}'.\n");
            return ExitCode::OK;
        } else {
            $this->stderr("Failed to assign role: " . json_encode($notificationRole->errors) . "\n");
            return ExitCode::DATAERR;
        }
    }

    /**
     * Lists all available roles
     */
    public function actionListRoles()
    {
        $roles = Role::find()->all();

        if (empty($roles)) {
            $this->stdout("No roles found.\n");
            return ExitCode::OK;
        }

        $this->stdout("Available Roles:\n");
        $this->stdout(str_repeat("-", 40) . "\n");

        foreach ($roles as $role) {
            $this->stdout("ID: {$role->id} | Name: {$role->name}\n");
            if ($role->description) {
                $this->stdout("Description: {$role->description}\n");
            }
            $this->stdout(str_repeat("-", 40) . "\n");
        }

        return ExitCode::OK;
    }

    /**
     * Tests a notification by triggering it manually
     * @param string $key The notification key
     */
    public function actionTest($key)
    {
        $this->stdout("Testing notification: {$key}\n");

        $testData = [
            'username' => 'Test User',
            'document_name' => 'Test Document',
            'document_type' => 'Test Type',
            'created_by_user' => 'Test Creator'
        ];

        NotificationManager::trigger($key, $testData);

        $this->stdout("Notification test completed. Check the notification_user table for results.\n");
        return ExitCode::OK;
    }
}
