<?php

use common\models\Role;
use kartik\form\ActiveForm;
use kartik\select2\Select2;
use yii\bootstrap5\Html;
use yii\helpers\ArrayHelper;
use yii\helpers\Url;

$this->title = "Routes";
$roles = ArrayHelper::map(Role::find()->all(), 'id', 'name');
$roleId = Yii::$app->request->get('role_id');
$addRouteUrl = Url::to(['role-functionality/save-custom-route']);

if ($roleId) {
    $model->role_id = $roleId;
}

$this->registerJsVar('addRouteUrl', $addRouteUrl);

// Assume $routes contains the routes assigned to the selected role
// and $routes contains all available routes


?>
<div>
    <h3 class="mb-3">Routes to wijzen aan rollen</h3>
    <?php $form = ActiveForm::begin(); ?>

    <?= $form->field($model, 'role_id')->widget(Select2::classname(), [
        'data' => $roles,
        'options' => [
            'placeholder' => 'Select a role...',
            // 'onchange' => 'this.form.submit();',
        ],
        'pluginOptions' => [
            'allowClear' => true,
        ]
    ]); ?>

    <div class="row mt-4">
        <div class="col-md-5">
            <h5 id="clear-cache">All Routes</h5>
            <div class="mb-2">
                <div class="input-group" style="max-width: 500px;">
                    <input type="text" name="custom_route" id="custom_route" class="form-control" placeholder="Search or add route (press Enter to add)" autocomplete="off">
                    <button type="button" class="btn btn-primary" id="custom-route-btn">Add Route</button>
                </div>
            </div>
            <ul id="all-routes" class="list-group sortable-list" style="min-height:300px;">
                <?php foreach ($routes as $route): ?>
                    <?php if (!in_array($route, $assignedRoutes)): ?>
                        <li class="list-group-item" data-route="<?= Html::encode($route) ?>">
                            <?= Html::encode($route) ?>
                        </li>
                    <?php endif; ?>
                <?php endforeach; ?>
            </ul>
        </div>
        <div class="col-md-2 d-flex align-items-center justify-content-center">
            <span class="bi bi-arrow-left-right" style="font-size:2rem;"></span>
        </div>
        <div class="col-md-5">
            <h5>Accessible Routes for Role</h5>
            <ul id="role-routes" class="list-group sortable-list" style="min-height:300px;">
                <?php foreach ($assignedRoutes as $route): ?>
                    <li class="list-group-item d-flex justify-content-between align-items-center" data-route="<?= Html::encode($route) ?>">
                        <span><?= Html::encode($route) ?></span>
                        <button type="button" class="text-danger btn btn-remove-route" title="Remove route">
                            &times;
                        </button>
                        <input type="hidden" name="RoleFunctionality[route][]" value="<?= Html::encode($route) ?>">
                    </li>
                <?php endforeach; ?>
            </ul>
        </div>
    </div>

    <div class="form-group mt-4">
        <?= Html::submitButton(
            $model->isNewRecord ? 'Save' : 'Update',
            ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']
        ) ?>
    </div>

    <?php ActiveForm::end(); ?>
</div>

<?php
$this->registerJs(<<<JS
$(function() {
    // Change URL role_id when role is changed
    $(document).on('change', '#rolefunctionality-role_id', function() {
        var roleId = $(this).val();
        var url = new URL(window.location.href);
        if (roleId) {
            url.searchParams.set('role_id', roleId);
        } else {
            url.searchParams.delete('role_id');
        }
        window.location.href = url.toString();
    });
    // Multi-select logic
    $(document).on('click', '.sortable-list li', function(e) {
        if (e.ctrlKey || e.metaKey) {
            $(this).toggleClass('selected');
        } else if (e.shiftKey) {
            var parent = $(this).parent();
            var items = parent.children('li');
            var first = items.index(parent.children('li.selected').first());
            var last = items.index(this);
            if (first > -1) {
                var [start, end] = [Math.min(first, last), Math.max(first, last)];
                items.slice(start, end + 1).addClass('selected');
            } else {
                $(this).addClass('selected');
            }
        } else {
            $(this).addClass('selected').siblings().removeClass('selected');
        }
    });
    // Search filter for all-routes
    $('#custom_route').on('keyup', function() {
        var value = $(this).val().toLowerCase();
        $('#all-routes li').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });
    // Sortable with multi-drag
    var dragging = false;
    $("#all-routes, #role-routes").sortable({
        connectWith: ".sortable-list",
        placeholder: "ui-state-highlight",
        start: function(e, ui) {
            var selected = ui.item.parent().children('.selected');
            if (!ui.item.hasClass('selected')) {
                selected.removeClass('selected');
                ui.item.addClass('selected');
            }
            var elements = ui.item.parent().children('.selected').not(ui.item);
            ui.item.data('multidrag', elements);
            elements.hide();
            dragging = true;
        },
        stop: function(e, ui) {
            var elements = ui.item.data('multidrag');
            if (elements) {
                elements.insertAfter(ui.item);
                elements.show();
                ui.item.removeData('multidrag');
            }
            dragging = false;
        },
        receive: function(e, ui) {
            var elements = ui.item.data('multidrag');
            if (elements) {
                elements.removeClass('selected');
            }
        },
        update: function(event, ui) {
            // Always remove all hidden inputs
            $('#role-routes input[type="hidden"]').remove();
            // Always add hidden inputs for every item in role-routes
            $('#role-routes li').each(function() {
                var route = $(this).data('route');
                $(this).append('<input type="hidden" name="RoleFunctionality[route][]" value="'+route+'">');
            });
        }
    }).disableSelection();

    // Also ensure on page load:
    // Add hidden inputs for all items in role-routes
    $('#role-routes input[type="hidden"]').remove();
    $('#role-routes li').each(function() {
        var route = $(this).data('route');
        $(this).append('<input type="hidden" name="RoleFunctionality[route][]" value="'+route+'">');
    });
});

// Remove a route from the #route-role list
$(document).on('click', '.btn-remove-route', function(e) {
    e.preventDefault();
    $(this).closest('li').remove();
});


// Add route on click
$('#custom-route-btn').on('click', function(e) {
    var customRoute = $('#custom_route').val().trim();
    if (!customRoute) return;

    $.ajax({
        url: addRouteUrl,
        method: 'POST',
        headers: {
            'X-CSRF-Token': yii.getCsrfToken()
        },
        data: {
            custom_route: customRoute
        },
        dataType: 'json'
    })
    .done(function(data) {
        if (data.success) {
            Toast.fire({ icon: 'success', title: data.message || 'Route added!' });
            setTimeout(() => window.location.reload(), 1000);
        } else {
            Toast.fire({ icon: 'error', title: data.message || 'Failed to add route' });
        }
    })
    .fail(function() {
        Toast.fire({ icon: 'error', title: 'Failed to add route' });
    });
});

$('#clear-cache').on('click', function(e){
    $.ajax({
        url: '/backoffice/index.php?r=role-functionality%2Fclear-cache',
        // method: 'POST',
        headers: {
            'X-CSRF-Token': yii.getCsrfToken()
        },
    })
    .done(function(data) {
        if (data.success) {
            Toast.fire({ icon: 'success', title: data.message });
            setTimeout(() => window.location.reload(), 1000);
        } else {
            Toast.fire({ icon: 'error', title: data.message });
        }
    })
    .fail(function() {
        Toast.fire({ icon: 'error', title: data.message });
    });
})

JS);
?>

<style>
    .sortable-list {
        min-height: 300px;
        border: 1px solid #ccc;
        background: #fafafa;
    }

    .list-group.sortable-list {
        max-height: 430px;
        overflow-y: auto;
    }

    .ui-state-highlight {
        height: 2.5em;
        line-height: 1.2em;
        background: #e0e0e0;
    }

    .list-group-item.selected {
        background: #b3d7ff !important;
        color: #222;
    }
</style>