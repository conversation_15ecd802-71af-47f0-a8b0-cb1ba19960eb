<?php

use kartik\form\ActiveForm;
use yii\helpers\Html;
use kartik\select2\Select2;

/* @var $this yii\web\View */
/* @var $model common\models\Functionality */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="functionality-form">

    <?php $form = ActiveForm::begin(); ?>

    <!-- <?= $form->field($model, 'name')->textInput(['maxlength' => true]) ?> -->

    <?= $form->field($model, 'controller')->textInput(['maxlength' => true]) ?>

    <div class="row">
        <div class="col-md-6">
            <?= $form->field($model, 'type')->widget(Select2::classname(), [
                'data' => \yii\helpers\ArrayHelper::map(
                    \common\models\FunctionalityType::find()->all(),
                    'name',
                    'name'
                ),
                'options' => ['placeholder' => 'Select type...'],
                'pluginOptions' => [
                    'allowClear' => true,
                    'dropdownParent' => '#ajaxCrudModal'
                ],
            ]); ?>
        </div>
        <div class="col-md-6">
            <?= $form->field($model, 'parameters')->textInput(['maxlength' => true]) ?>
        </div>
    </div>


    <?= $form->field($model, 'description')->textarea(['rows' => 6]) ?>

    <?php if (!Yii::$app->request->isAjax) { ?>
        <div class="form-group">
            <?= Html::submitButton($model->isNewRecord ? 'Create' : 'Update', ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']) ?>
        </div>
    <?php } ?>

    <?php ActiveForm::end(); ?>

</div>