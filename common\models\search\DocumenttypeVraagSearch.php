<?php

namespace common\models\search;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\DocumenttypeVraag;

/**
 * DocumenttypeVraagSearch represents the model behind the search form about `common\models\DocumenttypeVraag`.
 */
class DocumentTypeVraagSearch extends DocumentTypeVraag
{
    public $globalSearch;

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'document_id', 'vraag_id', 'vraag_nummer', 'created_by', 'updated_by'], 'integer'],
            [['created_at', 'updated_at'], 'safe'],
            [['globalSearch'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = DocumenttypeVraag::find()
            ->alias('dv')
            ->joinWith([
                'document d',
                'vraag v',
                'createdBy cb',
                'updatedBy ub'
            ]);

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => [
                'defaultOrder' => [
                    'vraag_nummer' => SORT_ASC,
                    'id' => SORT_DESC
                ]
            ],
        ]);

        // Important: this ensures the related data is properly sorted
        $dataProvider->sort->attributes['document.type'] = [
            'asc' => ['d.type' => SORT_ASC],
            'desc' => ['d.type' => SORT_DESC],
        ];

        $dataProvider->sort->attributes['vraag.vraag'] = [
            'asc' => ['v.vraag' => SORT_ASC],
            'desc' => ['v.vraag' => SORT_DESC],
        ];

        $dataProvider->sort->attributes['createdBy.username'] = [
            'asc' => ['cb.username' => SORT_ASC],
            'desc' => ['cb.username' => SORT_DESC],
        ];

        $dataProvider->sort->attributes['updatedBy.username'] = [
            'asc' => ['ub.username' => SORT_ASC],
            'desc' => ['ub.username' => SORT_DESC],
        ];

        $this->load($params);

        if (!$this->validate()) {
            return $dataProvider;
        }

        // Add global search condition
        if (!empty($this->globalSearch)) {
            $query->andWhere([
                'or',
                ['like', 'dv.id', $this->globalSearch],
                ['like', 'dv.vraag_nummer', $this->globalSearch],
                ['like', 'd.type', $this->globalSearch],
                ['like', 'v.vraag', $this->globalSearch],
                ['like', 'cb.username', $this->globalSearch],
                ['like', 'ub.username', $this->globalSearch],
            ]);
        }

        // Grid filtering conditions
        $query->andFilterWhere([
            'dv.id' => $this->id,
            'dv.document_id' => $this->document_id,
            'dv.vraag_id' => $this->vraag_id,
            'dv.vraag_nummer' => $this->vraag_nummer,
            'dv.created_at' => $this->created_at,
            'dv.updated_at' => $this->updated_at,
            'dv.created_by' => $this->created_by,
            'dv.updated_by' => $this->updated_by,
        ]);

        // Remove duplicates that might occur due to joins
        $query->groupBy('dv.id');

        return $dataProvider;
    }
}
