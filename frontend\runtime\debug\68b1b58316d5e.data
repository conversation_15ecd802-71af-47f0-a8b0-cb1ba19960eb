a:14:{s:6:"config";s:15911:"a:5:{s:10:"phpVersion";s:5:"8.3.3";s:10:"yiiVersion";s:6:"2.0.53";s:11:"application";a:8:{s:3:"yii";s:6:"2.0.53";s:4:"name";s:3:"FMZ";s:7:"version";s:3:"1.0";s:8:"language";s:5:"en-US";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:5:"8.3.3";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:71:{s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/faker";s:49:"C:\Web\Reclassering\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.7.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:47:"C:\Web\Reclassering\vendor/yiisoft/yii2-gii/src";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:57:"C:\Web\Reclassering\vendor/yiisoft/yii2-symfonymailer/src";}}s:29:"hail812/yii2-adminlte-widgets";a:3:{s:4:"name";s:29:"hail812/yii2-adminlte-widgets";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:25:"@hail812/adminlte/widgets";s:60:"C:\Web\Reclassering\vendor/hail812/yii2-adminlte-widgets/src";}}s:23:"yiisoft/yii2-bootstrap4";a:3:{s:4:"name";s:23:"yiisoft/yii2-bootstrap4";s:7:"version";s:8:"2.0.12.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap4";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-bootstrap4/src";}}s:22:"hail812/yii2-adminlte3";a:3:{s:4:"name";s:22:"hail812/yii2-adminlte3";s:7:"version";s:7:"1.1.9.0";s:5:"alias";a:1:{s:18:"@hail812/adminlte3";s:53:"C:\Web\Reclassering\vendor/hail812/yii2-adminlte3/src";}}s:18:"kartik-v/yii2-mpdf";a:3:{s:4:"name";s:18:"kartik-v/yii2-mpdf";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:12:"@kartik/mpdf";s:49:"C:\Web\Reclassering\vendor/kartik-v/yii2-mpdf/src";}}s:25:"kartik-v/yii2-krajee-base";a:3:{s:4:"name";s:25:"kartik-v/yii2-krajee-base";s:7:"version";s:7:"3.0.5.0";s:5:"alias";a:1:{s:12:"@kartik/base";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-krajee-base/src";}}s:20:"kartik-v/yii2-dialog";a:3:{s:4:"name";s:20:"kartik-v/yii2-dialog";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:14:"@kartik/dialog";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-dialog/src";}}s:23:"kartik-v/yii2-popover-x";a:3:{s:4:"name";s:23:"kartik-v/yii2-popover-x";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:15:"@kartik/popover";s:54:"C:\Web\Reclassering\vendor/kartik-v/yii2-popover-x/src";}}s:21:"kartik-v/yii2-builder";a:3:{s:4:"name";s:21:"kartik-v/yii2-builder";s:7:"version";s:7:"1.6.9.0";s:5:"alias";a:1:{s:15:"@kartik/builder";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-builder/src";}}s:22:"kartik-v/yii2-editable";a:3:{s:4:"name";s:22:"kartik-v/yii2-editable";s:7:"version";s:7:"1.8.0.0";s:5:"alias";a:1:{s:16:"@kartik/editable";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-editable/src";}}s:18:"kartik-v/yii2-grid";a:3:{s:4:"name";s:18:"kartik-v/yii2-grid";s:7:"version";s:7:"3.5.3.0";s:5:"alias";a:1:{s:12:"@kartik/grid";s:49:"C:\Web\Reclassering\vendor/kartik-v/yii2-grid/src";}}s:21:"kartik-v/yii2-helpers";a:3:{s:4:"name";s:21:"kartik-v/yii2-helpers";s:7:"version";s:7:"1.3.9.0";s:5:"alias";a:1:{s:15:"@kartik/helpers";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-helpers/src";}}s:24:"kartik-v/yii2-checkbox-x";a:3:{s:4:"name";s:24:"kartik-v/yii2-checkbox-x";s:7:"version";s:7:"1.0.7.0";s:5:"alias";a:1:{s:16:"@kartik/checkbox";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-checkbox-x/src";}}s:26:"kartik-v/yii2-context-menu";a:3:{s:4:"name";s:26:"kartik-v/yii2-context-menu";s:7:"version";s:7:"1.2.4.0";s:5:"alias";a:1:{s:13:"@kartik/cmenu";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-context-menu/src";}}s:25:"kartik-v/yii2-datecontrol";a:3:{s:4:"name";s:25:"kartik-v/yii2-datecontrol";s:7:"version";s:7:"1.9.9.0";s:5:"alias";a:1:{s:19:"@kartik/datecontrol";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-datecontrol/src";}}s:22:"kartik-v/yii2-sortable";a:3:{s:4:"name";s:22:"kartik-v/yii2-sortable";s:7:"version";s:7:"1.2.2.0";s:5:"alias";a:1:{s:16:"@kartik/sortable";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-sortable/src";}}s:25:"kartik-v/yii2-field-range";a:3:{s:4:"name";s:25:"kartik-v/yii2-field-range";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:13:"@kartik/field";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-field-range/src";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:8:"2.0.16.0";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-httpclient/src";}}s:25:"kartik-v/yii2-detail-view";a:3:{s:4:"name";s:25:"kartik-v/yii2-detail-view";s:7:"version";s:7:"1.8.7.0";s:5:"alias";a:1:{s:14:"@kartik/detail";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-detail-view/src";}}s:24:"kartik-v/yii2-date-range";a:3:{s:4:"name";s:24:"kartik-v/yii2-date-range";s:7:"version";s:7:"1.7.3.0";s:5:"alias";a:1:{s:17:"@kartik/daterange";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-date-range/src";}}s:22:"kartik-v/yii2-dynagrid";a:3:{s:4:"name";s:22:"kartik-v/yii2-dynagrid";s:7:"version";s:7:"1.5.5.0";s:5:"alias";a:1:{s:16:"@kartik/dynagrid";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-dynagrid/src";}}s:16:"yiisoft/yii2-jui";a:3:{s:4:"name";s:16:"yiisoft/yii2-jui";s:7:"version";s:7:"2.0.7.0";s:5:"alias";a:1:{s:8:"@yii/jui";s:43:"C:\Web\Reclassering\vendor/yiisoft/yii2-jui";}}s:27:"kartik-v/yii2-label-inplace";a:3:{s:4:"name";s:27:"kartik-v/yii2-label-inplace";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/label";s:58:"C:\Web\Reclassering\vendor/kartik-v/yii2-label-inplace/src";}}s:19:"kartik-v/yii2-icons";a:3:{s:4:"name";s:19:"kartik-v/yii2-icons";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/icons";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-icons/src";}}s:19:"kartik-v/yii2-money";a:3:{s:4:"name";s:19:"kartik-v/yii2-money";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/money";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-money/src";}}s:20:"kartik-v/yii2-ipinfo";a:3:{s:4:"name";s:20:"kartik-v/yii2-ipinfo";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:14:"@kartik/ipinfo";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-ipinfo/src";}}s:22:"kartik-v/yii2-markdown";a:3:{s:4:"name";s:22:"kartik-v/yii2-markdown";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/markdown";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-markdown/src";}}s:24:"kartik-v/yii2-dropdown-x";a:3:{s:4:"name";s:24:"kartik-v/yii2-dropdown-x";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/dropdown";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-dropdown-x/src";}}s:19:"kartik-v/yii2-nav-x";a:3:{s:4:"name";s:19:"kartik-v/yii2-nav-x";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:11:"@kartik/nav";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-nav-x/src";}}s:22:"kartik-v/yii2-password";a:3:{s:4:"name";s:22:"kartik-v/yii2-password";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/password";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-password/src";}}s:20:"kartik-v/yii2-slider";a:3:{s:4:"name";s:20:"kartik-v/yii2-slider";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:14:"@kartik/slider";s:47:"C:\Web\Reclassering\vendor/kartik-v/yii2-slider";}}s:28:"kartik-v/yii2-sortable-input";a:3:{s:4:"name";s:28:"kartik-v/yii2-sortable-input";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:17:"@kartik/sortinput";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-sortable-input/src";}}s:20:"kartik-v/yii2-tabs-x";a:3:{s:4:"name";s:20:"kartik-v/yii2-tabs-x";s:7:"version";s:7:"1.2.9.0";s:5:"alias";a:1:{s:12:"@kartik/tabs";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-tabs-x/src";}}s:30:"kartik-v/yii2-widget-typeahead";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-typeahead";s:7:"version";s:7:"1.0.4.0";s:5:"alias";a:1:{s:17:"@kartik/typeahead";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-typeahead/src";}}s:20:"kartik-v/yii2-social";a:3:{s:4:"name";s:20:"kartik-v/yii2-social";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:14:"@kartik/social";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-social/src";}}s:30:"kartik-v/yii2-widget-touchspin";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-touchspin";s:7:"version";s:7:"1.2.4.0";s:5:"alias";a:1:{s:17:"@kartik/touchspin";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-touchspin/src";}}s:32:"kartik-v/yii2-widget-switchinput";a:3:{s:4:"name";s:32:"kartik-v/yii2-widget-switchinput";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:19:"@kartik/switchinput";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-switchinput";}}s:24:"kartik-v/yii2-validators";a:4:{s:4:"name";s:24:"kartik-v/yii2-validators";s:7:"version";s:7:"1.0.3.0";s:5:"alias";a:1:{s:18:"@kartik/validators";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-validators/src";}s:9:"bootstrap";s:27:"kartik\validators\Bootstrap";}s:28:"kartik-v/yii2-widget-spinner";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-spinner";s:7:"version";s:7:"1.0.1.0";s:5:"alias";a:1:{s:15:"@kartik/spinner";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-spinner/src";}}s:28:"kartik-v/yii2-widget-sidenav";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-sidenav";s:7:"version";s:7:"1.0.1.0";s:5:"alias";a:1:{s:15:"@kartik/sidenav";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-sidenav";}}s:27:"kartik-v/yii2-widget-rating";a:3:{s:4:"name";s:27:"kartik-v/yii2-widget-rating";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:14:"@kartik/rating";s:58:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-rating/src";}}s:31:"kartik-v/yii2-widget-rangeinput";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-rangeinput";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:13:"@kartik/range";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-rangeinput/src";}}s:26:"kartik-v/yii2-widget-growl";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-growl";s:7:"version";s:7:"1.1.2.0";s:5:"alias";a:1:{s:13:"@kartik/growl";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-growl/src";}}s:28:"kartik-v/yii2-widget-depdrop";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-depdrop";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:15:"@kartik/depdrop";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-depdrop/src";}}s:31:"kartik-v/yii2-widget-colorinput";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-colorinput";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:13:"@kartik/color";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-colorinput/src";}}s:35:"kartik-v/yii2-widget-datetimepicker";a:3:{s:4:"name";s:35:"kartik-v/yii2-widget-datetimepicker";s:7:"version";s:7:"1.5.1.0";s:5:"alias";a:1:{s:16:"@kartik/datetime";s:66:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-datetimepicker/src";}}s:31:"kartik-v/yii2-widget-datepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-datepicker";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:12:"@kartik/date";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-datepicker/src";}}s:26:"kartik-v/yii2-widget-alert";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-alert";s:7:"version";s:7:"1.1.5.0";s:5:"alias";a:1:{s:13:"@kartik/alert";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-alert/src";}}s:26:"kartik-v/yii2-widget-affix";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-affix";s:7:"version";s:7:"1.0.0.0";s:5:"alias";a:1:{s:13:"@kartik/affix";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-affix";}}s:21:"kartik-v/yii2-widgets";a:3:{s:4:"name";s:21:"kartik-v/yii2-widgets";s:7:"version";s:7:"3.4.1.0";s:5:"alias";a:1:{s:15:"@kartik/widgets";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-widgets/src";}}s:33:"kartik-v/yii2-bootstrap5-dropdown";a:3:{s:4:"name";s:33:"kartik-v/yii2-bootstrap5-dropdown";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:19:"@kartik/bs5dropdown";s:64:"C:\Web\Reclassering\vendor/kartik-v/yii2-bootstrap5-dropdown/src";}}s:26:"biladina/yii2-ajaxcrud-bs4";a:4:{s:4:"name";s:26:"biladina/yii2-ajaxcrud-bs4";s:7:"version";s:7:"3.0.0.0";s:5:"alias";a:1:{s:22:"@yii2ajaxcrud/ajaxcrud";s:57:"C:\Web\Reclassering\vendor/biladina/yii2-ajaxcrud-bs4/src";}s:9:"bootstrap";s:31:"yii2ajaxcrud\ajaxcrud\Bootstrap";}s:28:"kartik-v/yii2-widget-select2";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-select2";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@kartik/select2";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-select2/src";}}s:31:"kartik-v/yii2-widget-activeform";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-activeform";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/form";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-activeform/src";}}s:23:"raoul2000/yii2-workflow";a:3:{s:4:"name";s:23:"raoul2000/yii2-workflow";s:7:"version";s:7:"1.2.0.0";s:5:"alias";a:1:{s:19:"@raoul2000/workflow";s:54:"C:\Web\Reclassering\vendor/raoul2000/yii2-workflow/src";}}s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:8:"2.0.50.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:20:"kartik-v/yii2-export";a:3:{s:4:"name";s:20:"kartik-v/yii2-export";s:7:"version";s:7:"1.4.3.0";s:5:"alias";a:1:{s:14:"@kartik/export";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-export/src";}}s:28:"raoul2000/yii2-workflow-view";a:3:{s:4:"name";s:28:"raoul2000/yii2-workflow-view";s:7:"version";s:7:"0.0.2.0";s:5:"alias";a:1:{s:24:"@raoul2000/workflow/view";s:59:"C:\Web\Reclassering\vendor/raoul2000/yii2-workflow-view/src";}}s:32:"cornernote/yii2-workflow-manager";a:3:{s:4:"name";s:32:"cornernote/yii2-workflow-manager";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:28:"@cornernote/workflow/manager";s:63:"C:\Web\Reclassering\vendor/cornernote/yii2-workflow-manager/src";}}s:28:"2amigos/yii2-ckeditor-widget";a:3:{s:4:"name";s:28:"2amigos/yii2-ckeditor-widget";s:7:"version";s:7:"2.1.0.0";s:5:"alias";a:1:{s:19:"@dosamigos/ckeditor";s:59:"C:\Web\Reclassering\vendor/2amigos/yii2-ckeditor-widget/src";}}s:17:"yiisoft/yii2-twig";a:3:{s:4:"name";s:17:"yiisoft/yii2-twig";s:7:"version";s:7:"2.5.1.0";s:5:"alias";a:1:{s:9:"@yii/twig";s:48:"C:\Web\Reclassering\vendor/yiisoft/yii2-twig/src";}}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.27.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:49:"C:\Web\Reclassering\vendor/yiisoft/yii2-debug/src";}}s:27:"2amigos/yii2-chartjs-widget";a:3:{s:4:"name";s:27:"2amigos/yii2-chartjs-widget";s:7:"version";s:7:"2.1.3.0";s:5:"alias";a:1:{s:18:"@dosamigos/chartjs";s:58:"C:\Web\Reclassering\vendor/2amigos/yii2-chartjs-widget/src";}}s:19:"bedezign/yii2-audit";a:4:{s:4:"name";s:19:"bedezign/yii2-audit";s:7:"version";s:7:"1.2.7.0";s:5:"alias";a:1:{s:20:"@bedezign/yii2/audit";s:50:"C:\Web\Reclassering\vendor/bedezign/yii2-audit/src";}s:9:"bootstrap";s:29:"bedezign\yii2\audit\Bootstrap";}s:30:"kartik-v/yii2-widget-fileinput";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-fileinput";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/file";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-fileinput/src";}}s:24:"rmrevin/yii2-fontawesome";a:3:{s:4:"name";s:24:"rmrevin/yii2-fontawesome";s:7:"version";s:8:"2.10.3.0";s:5:"alias";a:1:{s:24:"@rmrevin/yii/fontawesome";s:51:"C:\Web\Reclassering\vendor/rmrevin/yii2-fontawesome";}}s:24:"edofre/yii2-fullcalendar";a:3:{s:4:"name";s:24:"edofre/yii2-fullcalendar";s:7:"version";s:8:"1.0.11.0";s:5:"alias";a:1:{s:20:"@edofre/fullcalendar";s:55:"C:\Web\Reclassering\vendor/edofre/yii2-fullcalendar/src";}}s:31:"kartik-v/yii2-widget-timepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-timepicker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/time";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-timepicker/src";}}s:34:"miloschuman/yii2-highcharts-widget";a:3:{s:4:"name";s:34:"miloschuman/yii2-highcharts-widget";s:7:"version";s:8:"11.0.0.0";s:5:"alias";a:1:{s:23:"@miloschuman/highcharts";s:65:"C:\Web\Reclassering\vendor/miloschuman/yii2-highcharts-widget/src";}}}}";s:3:"log";s:16996:"a:1:{s:8:"messages";a:39:{i:0;a:6:{i:0;s:55:"Bootstrap with kartik\validators\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.981262;i:4;a:0:{}i:5;i:2898544;}i:1;a:6:{i:0;s:59:"Bootstrap with yii2ajaxcrud\ajaxcrud\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.981847;i:4;a:0:{}i:5;i:3003768;}i:2;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.981853;i:4;a:0:{}i:5;i:3004064;}i:3;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.982126;i:4;a:0:{}i:5;i:3034064;}i:4;a:6:{i:0;s:57:"Bootstrap with bedezign\yii2\audit\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.982384;i:4;a:0:{}i:5;i:3061536;}i:5;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.98321;i:4;a:0:{}i:5;i:3216352;}i:6;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.983218;i:4;a:0:{}i:5;i:3216992;}i:7;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:**********.987482;i:4;a:0:{}i:5;i:4214608;}i:8;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.019366;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5538704;}i:9;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:1756476803.019477;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5540984;}i:14;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.051683;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5598800;}i:17;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.053732;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5619520;}i:20;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=7) AND (`status`=10)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.071001;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6089336;}i:23;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1756476803.09223;i:4;a:0:{}i:5;i:6781672;}i:24;a:6:{i:0;s:129:"SELECT `data` FROM `cache` WHERE `id` = 'reclassering8301292eb993d3ff19f0b1cfe988b789' AND (`expire` = 0 OR `expire` >1756476803)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.095444;i:4;a:0:{}i:5;i:6969688;}i:27;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1756476803.09716;i:4;a:0:{}i:5;i:7035928;}i:28;a:6:{i:0;s:129:"SELECT `data` FROM `cache` WHERE `id` = 'reclassering00d716905c8ed414aa0103ba17815795' AND (`expire` = 0 OR `expire` >1756476803)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.097263;i:4;a:0:{}i:5;i:7044456;}i:37;a:6:{i:0;s:38:"Route requested: 'document/get-vragen'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:1756476803.098401;i:4;a:0:{}i:5;i:7076800;}i:38;a:6:{i:0;s:33:"Route to run: document/get-vragen";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:1756476803.102137;i:4;a:0:{}i:5;i:7236168;}i:39;a:6:{i:0;s:129:"SELECT `data` FROM `cache` WHERE `id` = 'reclasseringee3884871c292e38fe83a1ac97c0da3a' AND (`expire` = 0 OR `expire` >1756476803)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.104037;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:73;s:8:"function";s:3:"get";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:33;s:8:"function";s:20:"getCachedPermissions";s:5:"class";s:30:"common\components\AccessHelper";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:44:"C:\Web\Reclassering\frontend\config\main.php";s:4:"line";i:88;s:8:"function";s:9:"canAccess";s:5:"class";s:30:"common\components\AccessHelper";s:4:"type";s:2:"->";}}i:5;i:7261472;}i:42;a:6:{i:0;s:129:"SELECT `data` FROM `cache` WHERE `id` = 'reclasseringee3884871c292e38fe83a1ac97c0da3a' AND (`expire` = 0 OR `expire` >1756476803)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.109799;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:73;s:8:"function";s:3:"get";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:33;s:8:"function";s:20:"getCachedPermissions";s:5:"class";s:30:"common\components\AccessHelper";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\controllers\BaseController.php";s:4:"line";i:32;s:8:"function";s:9:"canAccess";s:5:"class";s:30:"common\components\AccessHelper";s:4:"type";s:2:"->";}}i:5;i:7356072;}i:45;a:6:{i:0;s:74:"Running action: frontend\controllers\DocumentController::actionGetVragen()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:1756476803.11104;i:4;a:0:{}i:5;i:7365568;}i:46;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `document_type`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.111368;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:17;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7402008;}i:49;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.114446;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:17;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7413704;}i:52;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_type' AND `kcu`.`TABLE_NAME` = 'document_type'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.115562;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:17;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7415016;}i:55;a:6:{i:0;s:44:"SELECT * FROM `document_type` WHERE `id`='8'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.118465;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:17;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7419232;}i:58;a:6:{i:0;s:80:"SELECT * FROM `documenttype_vraag` WHERE `document_id`=8 ORDER BY `vraag_nummer`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.124127;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7569968;}i:61;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `documenttype_vraag`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.125433;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7587320;}i:64;a:6:{i:0;s:38:"SHOW CREATE TABLE `documenttype_vraag`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.127417;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7598904;}i:67;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'documenttype_vraag' AND `kcu`.`TABLE_NAME` = 'documenttype_vraag'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.128539;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7601592;}i:70;a:6:{i:0;s:75:"SELECT * FROM `vraag` WHERE `id` IN (18, 19, 20, 21, 22, 23, 24, 25, 26, 5)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.132021;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7731752;}i:73;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `vraag`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.132938;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7746240;}i:76;a:6:{i:0;s:25:"SHOW CREATE TABLE `vraag`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.134704;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7756152;}i:79;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'vraag' AND `kcu`.`TABLE_NAME` = 'vraag'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.135233;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7758440;}i:82;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.137294;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7827064;}i:85;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.1378;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7841792;}i:88;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.138316;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7849656;}i:91;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.138943;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7859744;}i:94;a:6:{i:0;s:72:"SELECT * FROM `notification_trigger` WHERE `route`='document/get-vragen'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.139926;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:7888808;}}}";s:9:"profiling";s:29839:"a:3:{s:6:"memory";i:8109272;s:4:"time";d:0.17769312858581543;s:8:"messages";a:52:{i:10;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:1756476803.019515;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5541792;}i:11;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:1756476803.048243;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5585096;}i:12;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.04828;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5584880;}i:13;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.051596;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5597512;}i:15;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.051712;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5599712;}i:16;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.052708;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5602288;}i:18;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.053766;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5620560;}i:19;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.058289;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5623088;}i:21;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=7) AND (`status`=10)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.071089;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6089720;}i:22;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=7) AND (`status`=10)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.072409;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6091920;}i:25;a:6:{i:0;s:129:"SELECT `data` FROM `cache` WHERE `id` = 'reclassering8301292eb993d3ff19f0b1cfe988b789' AND (`expire` = 0 OR `expire` >1756476803)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.095487;i:4;a:0:{}i:5;i:6970224;}i:26;a:6:{i:0;s:129:"SELECT `data` FROM `cache` WHERE `id` = 'reclassering8301292eb993d3ff19f0b1cfe988b789' AND (`expire` = 0 OR `expire` >1756476803)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.096458;i:4;a:0:{}i:5;i:6972160;}i:29;a:6:{i:0;s:129:"SELECT `data` FROM `cache` WHERE `id` = 'reclassering00d716905c8ed414aa0103ba17815795' AND (`expire` = 0 OR `expire` >1756476803)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.097274;i:4;a:0:{}i:5;i:7044992;}i:30;a:6:{i:0;s:129:"SELECT `data` FROM `cache` WHERE `id` = 'reclassering00d716905c8ed414aa0103ba17815795' AND (`expire` = 0 OR `expire` >1756476803)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.097868;i:4;a:0:{}i:5;i:7047440;}i:40;a:6:{i:0;s:129:"SELECT `data` FROM `cache` WHERE `id` = 'reclasseringee3884871c292e38fe83a1ac97c0da3a' AND (`expire` = 0 OR `expire` >1756476803)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.10409;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:73;s:8:"function";s:3:"get";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:33;s:8:"function";s:20:"getCachedPermissions";s:5:"class";s:30:"common\components\AccessHelper";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:44:"C:\Web\Reclassering\frontend\config\main.php";s:4:"line";i:88;s:8:"function";s:9:"canAccess";s:5:"class";s:30:"common\components\AccessHelper";s:4:"type";s:2:"->";}}i:5;i:7263032;}i:41;a:6:{i:0;s:129:"SELECT `data` FROM `cache` WHERE `id` = 'reclasseringee3884871c292e38fe83a1ac97c0da3a' AND (`expire` = 0 OR `expire` >1756476803)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.105319;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:73;s:8:"function";s:3:"get";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:33;s:8:"function";s:20:"getCachedPermissions";s:5:"class";s:30:"common\components\AccessHelper";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:44:"C:\Web\Reclassering\frontend\config\main.php";s:4:"line";i:88;s:8:"function";s:9:"canAccess";s:5:"class";s:30:"common\components\AccessHelper";s:4:"type";s:2:"->";}}i:5;i:7265544;}i:43;a:6:{i:0;s:129:"SELECT `data` FROM `cache` WHERE `id` = 'reclasseringee3884871c292e38fe83a1ac97c0da3a' AND (`expire` = 0 OR `expire` >1756476803)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.109843;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:73;s:8:"function";s:3:"get";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:33;s:8:"function";s:20:"getCachedPermissions";s:5:"class";s:30:"common\components\AccessHelper";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\controllers\BaseController.php";s:4:"line";i:32;s:8:"function";s:9:"canAccess";s:5:"class";s:30:"common\components\AccessHelper";s:4:"type";s:2:"->";}}i:5;i:7357952;}i:44;a:6:{i:0;s:129:"SELECT `data` FROM `cache` WHERE `id` = 'reclasseringee3884871c292e38fe83a1ac97c0da3a' AND (`expire` = 0 OR `expire` >1756476803)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.110679;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:73;s:8:"function";s:3:"get";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:33;s:8:"function";s:20:"getCachedPermissions";s:5:"class";s:30:"common\components\AccessHelper";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\controllers\BaseController.php";s:4:"line";i:32;s:8:"function";s:9:"canAccess";s:5:"class";s:30:"common\components\AccessHelper";s:4:"type";s:2:"->";}}i:5;i:7360464;}i:47;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `document_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.111386;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:17;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7403296;}i:48;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `document_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.114381;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:17;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7412032;}i:50;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.114467;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:17;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7414992;}i:51;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.115422;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:17;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7417536;}i:53;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_type' AND `kcu`.`TABLE_NAME` = 'document_type'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.115578;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:17;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7416432;}i:54;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_type' AND `kcu`.`TABLE_NAME` = 'document_type'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.118288;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:17;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7419896;}i:56;a:6:{i:0;s:44:"SELECT * FROM `document_type` WHERE `id`='8'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.118483;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:17;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7420736;}i:57;a:6:{i:0;s:44:"SELECT * FROM `document_type` WHERE `id`='8'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.119293;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:17;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7424800;}i:59;a:6:{i:0;s:80:"SELECT * FROM `documenttype_vraag` WHERE `document_id`=8 ORDER BY `vraag_nummer`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.124195;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7571472;}i:60;a:6:{i:0;s:80:"SELECT * FROM `documenttype_vraag` WHERE `document_id`=8 ORDER BY `vraag_nummer`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.125359;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7578056;}i:62;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `documenttype_vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.125445;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7588608;}i:63;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `documenttype_vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.127381;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7597232;}i:65;a:6:{i:0;s:38:"SHOW CREATE TABLE `documenttype_vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.127428;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7601472;}i:66;a:6:{i:0;s:38:"SHOW CREATE TABLE `documenttype_vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.128201;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7604024;}i:68;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'documenttype_vraag' AND `kcu`.`TABLE_NAME` = 'documenttype_vraag'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.128589;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7603008;}i:69;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'documenttype_vraag' AND `kcu`.`TABLE_NAME` = 'documenttype_vraag'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.130685;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7606472;}i:71;a:6:{i:0;s:75:"SELECT * FROM `vraag` WHERE `id` IN (18, 19, 20, 21, 22, 23, 24, 25, 26, 5)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.132082;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7732000;}i:72;a:6:{i:0;s:75:"SELECT * FROM `vraag` WHERE `id` IN (18, 19, 20, 21, 22, 23, 24, 25, 26, 5)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.132878;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7738968;}i:74;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.132947;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7747528;}i:75;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.134674;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7754488;}i:77;a:6:{i:0;s:25:"SHOW CREATE TABLE `vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.134714;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7757440;}i:78;a:6:{i:0;s:25:"SHOW CREATE TABLE `vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.135159;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7759848;}i:80;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'vraag' AND `kcu`.`TABLE_NAME` = 'vraag'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.135244;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7759856;}i:81;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'vraag' AND `kcu`.`TABLE_NAME` = 'vraag'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.136732;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7763304;}i:83;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.137309;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7828568;}i:84;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.137666;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7831464;}i:86;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.13781;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7843296;}i:87;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.138164;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7846192;}i:89;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.138326;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7851160;}i:90;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.138654;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7854056;}i:92;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.138992;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7861248;}i:93;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.139473;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7864144;}i:95;a:6:{i:0;s:72:"SELECT * FROM `notification_trigger` WHERE `route`='document/get-vragen'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.139936;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:7889936;}i:96;a:6:{i:0;s:72:"SELECT * FROM `notification_trigger` WHERE `route`='document/get-vragen'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.140284;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:7891088;}}}";s:2:"db";s:29069:"a:1:{s:8:"messages";a:50:{i:12;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.04828;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5584880;}i:13;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.051596;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5597512;}i:15;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.051712;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5599712;}i:16;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.052708;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5602288;}i:18;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.053766;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5620560;}i:19;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.058289;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5623088;}i:21;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=7) AND (`status`=10)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.071089;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6089720;}i:22;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=7) AND (`status`=10)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.072409;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6091920;}i:25;a:6:{i:0;s:129:"SELECT `data` FROM `cache` WHERE `id` = 'reclassering8301292eb993d3ff19f0b1cfe988b789' AND (`expire` = 0 OR `expire` >1756476803)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.095487;i:4;a:0:{}i:5;i:6970224;}i:26;a:6:{i:0;s:129:"SELECT `data` FROM `cache` WHERE `id` = 'reclassering8301292eb993d3ff19f0b1cfe988b789' AND (`expire` = 0 OR `expire` >1756476803)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.096458;i:4;a:0:{}i:5;i:6972160;}i:29;a:6:{i:0;s:129:"SELECT `data` FROM `cache` WHERE `id` = 'reclassering00d716905c8ed414aa0103ba17815795' AND (`expire` = 0 OR `expire` >1756476803)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.097274;i:4;a:0:{}i:5;i:7044992;}i:30;a:6:{i:0;s:129:"SELECT `data` FROM `cache` WHERE `id` = 'reclassering00d716905c8ed414aa0103ba17815795' AND (`expire` = 0 OR `expire` >1756476803)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.097868;i:4;a:0:{}i:5;i:7047440;}i:40;a:6:{i:0;s:129:"SELECT `data` FROM `cache` WHERE `id` = 'reclasseringee3884871c292e38fe83a1ac97c0da3a' AND (`expire` = 0 OR `expire` >1756476803)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.10409;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:73;s:8:"function";s:3:"get";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:33;s:8:"function";s:20:"getCachedPermissions";s:5:"class";s:30:"common\components\AccessHelper";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:44:"C:\Web\Reclassering\frontend\config\main.php";s:4:"line";i:88;s:8:"function";s:9:"canAccess";s:5:"class";s:30:"common\components\AccessHelper";s:4:"type";s:2:"->";}}i:5;i:7263032;}i:41;a:6:{i:0;s:129:"SELECT `data` FROM `cache` WHERE `id` = 'reclasseringee3884871c292e38fe83a1ac97c0da3a' AND (`expire` = 0 OR `expire` >1756476803)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.105319;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:73;s:8:"function";s:3:"get";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:33;s:8:"function";s:20:"getCachedPermissions";s:5:"class";s:30:"common\components\AccessHelper";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:44:"C:\Web\Reclassering\frontend\config\main.php";s:4:"line";i:88;s:8:"function";s:9:"canAccess";s:5:"class";s:30:"common\components\AccessHelper";s:4:"type";s:2:"->";}}i:5;i:7265544;}i:43;a:6:{i:0;s:129:"SELECT `data` FROM `cache` WHERE `id` = 'reclasseringee3884871c292e38fe83a1ac97c0da3a' AND (`expire` = 0 OR `expire` >1756476803)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.109843;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:73;s:8:"function";s:3:"get";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:33;s:8:"function";s:20:"getCachedPermissions";s:5:"class";s:30:"common\components\AccessHelper";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\controllers\BaseController.php";s:4:"line";i:32;s:8:"function";s:9:"canAccess";s:5:"class";s:30:"common\components\AccessHelper";s:4:"type";s:2:"->";}}i:5;i:7357952;}i:44;a:6:{i:0;s:129:"SELECT `data` FROM `cache` WHERE `id` = 'reclasseringee3884871c292e38fe83a1ac97c0da3a' AND (`expire` = 0 OR `expire` >1756476803)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.110679;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:73;s:8:"function";s:3:"get";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:33;s:8:"function";s:20:"getCachedPermissions";s:5:"class";s:30:"common\components\AccessHelper";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\controllers\BaseController.php";s:4:"line";i:32;s:8:"function";s:9:"canAccess";s:5:"class";s:30:"common\components\AccessHelper";s:4:"type";s:2:"->";}}i:5;i:7360464;}i:47;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `document_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.111386;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:17;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7403296;}i:48;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `document_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.114381;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:17;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7412032;}i:50;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.114467;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:17;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7414992;}i:51;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.115422;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:17;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7417536;}i:53;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_type' AND `kcu`.`TABLE_NAME` = 'document_type'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.115578;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:17;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7416432;}i:54;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_type' AND `kcu`.`TABLE_NAME` = 'document_type'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.118288;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:17;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7419896;}i:56;a:6:{i:0;s:44:"SELECT * FROM `document_type` WHERE `id`='8'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.118483;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:17;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7420736;}i:57;a:6:{i:0;s:44:"SELECT * FROM `document_type` WHERE `id`='8'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.119293;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:17;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7424800;}i:59;a:6:{i:0;s:80:"SELECT * FROM `documenttype_vraag` WHERE `document_id`=8 ORDER BY `vraag_nummer`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.124195;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7571472;}i:60;a:6:{i:0;s:80:"SELECT * FROM `documenttype_vraag` WHERE `document_id`=8 ORDER BY `vraag_nummer`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.125359;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7578056;}i:62;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `documenttype_vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.125445;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7588608;}i:63;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `documenttype_vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.127381;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7597232;}i:65;a:6:{i:0;s:38:"SHOW CREATE TABLE `documenttype_vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.127428;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7601472;}i:66;a:6:{i:0;s:38:"SHOW CREATE TABLE `documenttype_vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.128201;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7604024;}i:68;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'documenttype_vraag' AND `kcu`.`TABLE_NAME` = 'documenttype_vraag'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.128589;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7603008;}i:69;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'documenttype_vraag' AND `kcu`.`TABLE_NAME` = 'documenttype_vraag'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.130685;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7606472;}i:71;a:6:{i:0;s:75:"SELECT * FROM `vraag` WHERE `id` IN (18, 19, 20, 21, 22, 23, 24, 25, 26, 5)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.132082;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7732000;}i:72;a:6:{i:0;s:75:"SELECT * FROM `vraag` WHERE `id` IN (18, 19, 20, 21, 22, 23, 24, 25, 26, 5)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.132878;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7738968;}i:74;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.132947;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7747528;}i:75;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.134674;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7754488;}i:77;a:6:{i:0;s:25:"SHOW CREATE TABLE `vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.134714;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7757440;}i:78;a:6:{i:0;s:25:"SHOW CREATE TABLE `vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.135159;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7759848;}i:80;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'vraag' AND `kcu`.`TABLE_NAME` = 'vraag'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.135244;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7759856;}i:81;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'vraag' AND `kcu`.`TABLE_NAME` = 'vraag'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.136732;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7763304;}i:83;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.137309;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7828568;}i:84;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.137666;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7831464;}i:86;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.13781;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7843296;}i:87;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.138164;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7846192;}i:89;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.138326;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7851160;}i:90;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.138654;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7854056;}i:92;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.138992;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7861248;}i:93;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.139473;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:39;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:338;s:8:"function";s:19:"getVragenByDocument";}}i:5;i:7864144;}i:95;a:6:{i:0;s:72:"SELECT * FROM `notification_trigger` WHERE `route`='document/get-vragen'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.139936;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:7889936;}i:96;a:6:{i:0;s:72:"SELECT * FROM `notification_trigger` WHERE `route`='document/get-vragen'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756476803.140284;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:7891088;}}}";s:5:"event";s:12509:"a:72:{i:0;a:5:{s:4:"time";d:1756476803.009535;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:1;a:5:{s:4:"time";d:1756476803.048229;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:2;a:5:{s:4:"time";d:1756476803.07376;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:3;a:5:{s:4:"time";d:1756476803.073859;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:4;a:5:{s:4:"time";d:1756476803.086852;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:5;a:5:{s:4:"time";d:1756476803.098008;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:6;a:5:{s:4:"time";d:1756476803.109564;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:7;a:5:{s:4:"time";d:1756476803.111006;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"frontend\controllers\DocumentController";}i:8;a:5:{s:4:"time";d:1756476803.111317;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:9;a:5:{s:4:"time";d:1756476803.120797;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:10;a:5:{s:4:"time";d:1756476803.122449;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:11;a:5:{s:4:"time";d:1756476803.123875;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:12;a:5:{s:4:"time";d:1756476803.125405;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:13;a:5:{s:4:"time";d:1756476803.130752;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:14;a:5:{s:4:"time";d:1756476803.130771;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:15;a:5:{s:4:"time";d:1756476803.130784;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:16;a:5:{s:4:"time";d:1756476803.130796;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:17;a:5:{s:4:"time";d:1756476803.130812;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:18;a:5:{s:4:"time";d:1756476803.130823;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:19;a:5:{s:4:"time";d:1756476803.130834;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:20;a:5:{s:4:"time";d:1756476803.130844;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:21;a:5:{s:4:"time";d:1756476803.130855;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:22;a:5:{s:4:"time";d:1756476803.131273;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:23;a:5:{s:4:"time";d:1756476803.13129;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:24;a:5:{s:4:"time";d:1756476803.131297;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:25;a:5:{s:4:"time";d:1756476803.132916;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:26;a:5:{s:4:"time";d:1756476803.136959;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:27;a:5:{s:4:"time";d:1756476803.137018;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:28;a:5:{s:4:"time";d:1756476803.137058;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:29;a:5:{s:4:"time";d:1756476803.137116;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:30;a:5:{s:4:"time";d:1756476803.137151;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:31;a:5:{s:4:"time";d:1756476803.137165;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:32;a:5:{s:4:"time";d:1756476803.137178;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:33;a:5:{s:4:"time";d:1756476803.13719;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:34;a:5:{s:4:"time";d:1756476803.137201;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:35;a:5:{s:4:"time";d:1756476803.137219;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:36;a:5:{s:4:"time";d:1756476803.137229;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:37;a:5:{s:4:"time";d:1756476803.137694;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:38;a:5:{s:4:"time";d:1756476803.137718;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:39;a:5:{s:4:"time";d:1756476803.138186;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:40;a:5:{s:4:"time";d:1756476803.138204;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:41;a:5:{s:4:"time";d:1756476803.138225;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:42;a:5:{s:4:"time";d:1756476803.138228;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:43;a:5:{s:4:"time";d:1756476803.13823;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:44;a:5:{s:4:"time";d:1756476803.138233;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:45;a:5:{s:4:"time";d:1756476803.138235;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:46;a:5:{s:4:"time";d:1756476803.138238;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:47;a:5:{s:4:"time";d:1756476803.13824;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:48;a:5:{s:4:"time";d:1756476803.138243;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:49;a:5:{s:4:"time";d:1756476803.138245;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:50;a:5:{s:4:"time";d:1756476803.138248;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:51;a:5:{s:4:"time";d:1756476803.138704;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:52;a:5:{s:4:"time";d:1756476803.13874;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:53;a:5:{s:4:"time";d:1756476803.139495;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:54;a:5:{s:4:"time";d:1756476803.139513;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:55;a:5:{s:4:"time";d:1756476803.139532;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:56;a:5:{s:4:"time";d:1756476803.139535;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:57;a:5:{s:4:"time";d:1756476803.139537;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:58;a:5:{s:4:"time";d:1756476803.139539;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:59;a:5:{s:4:"time";d:1756476803.139541;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:60;a:5:{s:4:"time";d:1756476803.139543;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:61;a:5:{s:4:"time";d:1756476803.139546;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:62;a:5:{s:4:"time";d:1756476803.139548;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:63;a:5:{s:4:"time";d:1756476803.13955;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:64;a:5:{s:4:"time";d:1756476803.139552;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:65;a:5:{s:4:"time";d:1756476803.13958;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"frontend\controllers\DocumentController";}i:66;a:5:{s:4:"time";d:1756476803.139878;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:67;a:5:{s:4:"time";d:1756476803.140299;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:68;a:5:{s:4:"time";d:1756476803.140308;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:69;a:5:{s:4:"time";d:1756476803.140312;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:70;a:5:{s:4:"time";d:1756476803.141722;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:71;a:5:{s:4:"time";d:1756476803.141766;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:91:"a:3:{s:5:"start";d:**********.969169;s:3:"end";d:1756476803.147774;s:6:"memory";i:8109272;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:1280:"a:3:{s:8:"messages";a:6:{i:31;a:6:{i:0;a:3:{s:4:"rule";s:3:"gii";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1756476803.098375;i:4;a:0:{}i:5;i:7073672;}i:32;a:6:{i:0;a:3:{s:4:"rule";s:12:"gii/<id:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1756476803.098385;i:4;a:0:{}i:5;i:7074264;}i:33;a:6:{i:0;a:3:{s:4:"rule";s:41:"gii/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1756476803.098388;i:4;a:0:{}i:5;i:7075496;}i:34;a:6:{i:0;a:3:{s:4:"rule";s:5:"debug";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1756476803.098391;i:4;a:0:{}i:5;i:7076088;}i:35;a:6:{i:0;a:3:{s:4:"rule";s:43:"debug/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1756476803.098393;i:4;a:0:{}i:5;i:7076680;}i:36;a:6:{i:0;s:55:"No matching URL rules. Using default URL parsing logic.";i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1756476803.098395;i:4;a:0:{}i:5;i:7076896;}}s:5:"route";s:19:"document/get-vragen";s:6:"action";s:58:"frontend\controllers\DocumentController::actionGetVragen()";}";s:7:"request";s:9565:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:19:{s:12:"content-type";s:0:"";s:14:"content-length";s:1:"0";s:14:"x-original-url";s:28:"/document/get-vragen?docId=8";s:14:"sec-fetch-dest";s:5:"empty";s:14:"sec-fetch-mode";s:4:"cors";s:14:"sec-fetch-site";s:11:"same-origin";s:16:"sec-ch-ua-mobile";s:2:"?0";s:9:"sec-ch-ua";s:66:""Not;A=Brand";v="99", "Microsoft Edge";v="139", "Chromium";v="139"";s:16:"x-requested-with";s:14:"XMLHttpRequest";s:12:"x-csrf-token";s:88:"gwWdt5915SSAjrwYnAlomHYlJpytHyu0YEhcEJEtaD7CYeyPxia1VLjM_mDoYFrxLhF87eZASN4leWxU-XcvBg==";s:18:"sec-ch-ua-platform";s:9:""Windows"";s:10:"user-agent";s:125:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********";s:7:"referer";s:30:"http://localhost:8005/document";s:4:"host";s:14:"localhost:8005";s:6:"cookie";s:468:"_identity-frontend=20825fca1ba33f2e9fa7551c261673418774e609a156b6153cb0373705fbe6f1a%3A2%3A%7Bi%3A0%3Bs%3A18%3A%22_identity-frontend%22%3Bi%3A1%3Bs%3A46%3A%22%5B7%2C%226126QpFT0R6UeNHmscglqzlP31CnBjEU%22%2C2592000%5D%22%3B%7D; advanced-frontend-fmz=ervhi1enqsp0ttdsohu3ko42j8; _csrf-frontend=29b65eda4e20db655928f7846630561c97757c0d136864e740993ee29b615c33a%3A2%3A%7Bi%3A0%3Bs%3A14%3A%22_csrf-frontend%22%3Bi%3A1%3Bs%3A32%3A%22Adq8YSPp8BBxti2iX4ZqK_cjE10DhZG8%22%3B%7D";s:15:"accept-language";s:23:"en-US,en;q=0.9,nl;q=0.8";s:15:"accept-encoding";s:23:"gzip, deflate, br, zstd";s:6:"accept";s:3:"*/*";s:10:"connection";s:10:"keep-alive";}s:15:"responseHeaders";a:9:{s:12:"X-Powered-By";s:9:"PHP/8.3.3";s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:35:"no-store, no-cache, must-revalidate";s:6:"Pragma";s:8:"no-cache";s:12:"Content-Type";s:31:"application/json; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"68b1b58316d5e";s:16:"X-Debug-Duration";s:3:"174";s:12:"X-Debug-Link";s:37:"/debug/default/view?tag=68b1b58316d5e";s:10:"Set-Cookie";s:313:"_identity-frontend=20825fca1ba33f2e9fa7551c261673418774e609a156b6153cb0373705fbe6f1a%3A2%3A%7Bi%3A0%3Bs%3A18%3A%22_identity-frontend%22%3Bi%3A1%3Bs%3A46%3A%22%5B7%2C%226126QpFT0R6UeNHmscglqzlP31CnBjEU%22%2C2592000%5D%22%3B%7D; expires=Sun, 28 Sep 2025 14:13:23 GMT; Max-Age=2592000; path=/; HttpOnly; SameSite=Lax";}s:5:"route";s:19:"document/get-vragen";s:6:"action";s:58:"frontend\controllers\DocumentController::actionGetVragen()";s:12:"actionParams";a:1:{s:5:"docId";s:1:"8";}s:7:"general";a:5:{s:6:"method";s:3:"GET";s:6:"isAjax";b:1;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:0:{}s:6:"SERVER";a:108:{s:13:"_FCGI_X_PIPE_";s:53:"\\.\pipe\IISFCGI-d0f00876-7677-4bb0-9969-b7eb9b63611c";s:5:"PHPRC";s:26:"C:\Program Files\PHP\v8.3\";s:21:"PHP_FCGI_MAX_REQUESTS";s:5:"10000";s:15:"ALLUSERSPROFILE";s:14:"C:\ProgramData";s:7:"APPDATA";s:56:"C:\WINDOWS\system32\config\systemprofile\AppData\Roaming";s:15:"APP_POOL_CONFIG";s:57:"C:\inetpub\temp\apppools\Reclassering\Reclassering.config";s:11:"APP_POOL_ID";s:12:"Reclassering";s:17:"ChocolateyInstall";s:25:"C:\ProgramData\chocolatey";s:18:"CommonProgramFiles";s:29:"C:\Program Files\Common Files";s:23:"CommonProgramFiles(x86)";s:35:"C:\Program Files (x86)\Common Files";s:18:"CommonProgramW6432";s:29:"C:\Program Files\Common Files";s:12:"COMPUTERNAME";s:10:"EGOV-L-046";s:7:"ComSpec";s:27:"C:\WINDOWS\system32\cmd.exe";s:10:"DriverData";s:38:"C:\Windows\System32\Drivers\DriverData";s:10:"IGCCSVC_DB";s:416:"AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAxsjL008UHEujcYXte9n1xAQAAAACAAAAAAAQZgAAAAEAACAAAACG6V8fH1MiQpt+vTHr85FUmGEpkihjwnphUZ8EBmjXIgAAAAAOgAAAAAIAACAAAAAwiEFpINOfvm27eQP7A6KJKkU7BiIp8dfO5XJb9O9V82AAAABOuhJMP+CaR040CClff82PsaeGd4XybaOdhylSvqlmGruwc3EJIvD6UYcnWqB0s7SJb8fFsrKIKR8cZ/eXSEmMUiHF6J1xrcv30HL80qkrgc0BXBLoS19xuBSzihq7gPZAAAAABjLonwgGXzz5g0gBw2ytpgnFnasAK0EsnQP6jk1iL/gtzNQgs4cOrzDs1Y9Qn4IMfeSQ9eZqer9cZQRh9WmStg==";s:12:"LOCALAPPDATA";s:54:"C:\WINDOWS\system32\config\systemprofile\AppData\Local";s:20:"NUMBER_OF_PROCESSORS";s:2:"12";s:14:"OnlineServices";s:15:"Online Services";s:2:"OS";s:10:"Windows_NT";s:4:"Path";s:582:"C:\Python313\Scripts\;C:\Python313\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Program Files\PHP\v8.3;C:\ProgramData\ComposerSetup\bin;C:\ProgramData\ComposerSetup\bin\composer.bat;C:\Program Files (x86)\HP\HP OCR\DB_Lib\;C:\Program Files\HP\Common\HPDestPlgIn\;C:\Program Files (x86)\HP\Common\HPDestPlgIn\;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Program Files\GitHub CLI\;C:\WINDOWS\system32\config\systemprofile\AppData\Local\Microsoft\WindowsApps";s:7:"PATHEXT";s:62:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW";s:12:"platformcode";s:2:"AN";s:22:"PROCESSOR_ARCHITECTURE";s:5:"AMD64";s:20:"PROCESSOR_IDENTIFIER";s:51:"Intel64 Family 6 Model 186 Stepping 3, GenuineIntel";s:15:"PROCESSOR_LEVEL";s:1:"6";s:18:"PROCESSOR_REVISION";s:4:"ba03";s:11:"ProgramData";s:14:"C:\ProgramData";s:12:"ProgramFiles";s:16:"C:\Program Files";s:17:"ProgramFiles(x86)";s:22:"C:\Program Files (x86)";s:12:"ProgramW6432";s:16:"C:\Program Files";s:12:"PSModulePath";s:93:"C:\Program Files\WindowsPowerShell\Modules;C:\WINDOWS\system32\WindowsPowerShell\v1.0\Modules";s:6:"PUBLIC";s:15:"C:\Users\<USER>\WINDOWS";s:4:"TEMP";s:15:"C:\WINDOWS\TEMP";s:3:"TMP";s:15:"C:\WINDOWS\TEMP";s:10:"USERDOMAIN";s:3:"GOV";s:8:"USERNAME";s:11:"EGOV-L-046$";s:11:"USERPROFILE";s:40:"C:\WINDOWS\system32\config\systemprofile";s:6:"windir";s:10:"C:\WINDOWS";s:17:"ZES_ENABLE_SYSMAN";s:1:"1";s:14:"ORIG_PATH_INFO";s:10:"/index.php";s:3:"URL";s:10:"/index.php";s:15:"SERVER_SOFTWARE";s:18:"Microsoft-IIS/10.0";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:18:"SERVER_PORT_SECURE";s:1:"0";s:11:"SERVER_PORT";s:4:"8005";s:11:"SERVER_NAME";s:9:"localhost";s:11:"SCRIPT_NAME";s:10:"/index.php";s:15:"SCRIPT_FILENAME";s:42:"C:\Web\Reclassering\frontend\web\index.php";s:11:"REQUEST_URI";s:28:"/document/get-vragen?docId=8";s:14:"REQUEST_METHOD";s:3:"GET";s:11:"REMOTE_USER";s:0:"";s:11:"REMOTE_PORT";s:5:"50734";s:11:"REMOTE_HOST";s:3:"::1";s:11:"REMOTE_ADDR";s:3:"::1";s:12:"QUERY_STRING";s:7:"docId=8";s:15:"PATH_TRANSLATED";s:42:"C:\Web\Reclassering\frontend\web\index.php";s:10:"LOGON_USER";s:0:"";s:10:"LOCAL_ADDR";s:3:"::1";s:18:"INSTANCE_META_PATH";s:11:"/LM/W3SVC/2";s:13:"INSTANCE_NAME";s:12:"RECLASSERING";s:11:"INSTANCE_ID";s:1:"2";s:20:"HTTPS_SERVER_SUBJECT";s:0:"";s:19:"HTTPS_SERVER_ISSUER";s:0:"";s:19:"HTTPS_SECRETKEYSIZE";s:0:"";s:13:"HTTPS_KEYSIZE";s:0:"";s:5:"HTTPS";s:3:"off";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:13:"DOCUMENT_ROOT";s:32:"C:\Web\Reclassering\frontend\web";s:12:"CONTENT_TYPE";s:0:"";s:14:"CONTENT_LENGTH";s:1:"0";s:12:"CERT_SUBJECT";s:0:"";s:17:"CERT_SERIALNUMBER";s:0:"";s:11:"CERT_ISSUER";s:0:"";s:10:"CERT_FLAGS";s:0:"";s:11:"CERT_COOKIE";s:0:"";s:9:"AUTH_USER";s:0:"";s:13:"AUTH_PASSWORD";s:0:"";s:9:"AUTH_TYPE";s:0:"";s:18:"APPL_PHYSICAL_PATH";s:33:"C:\Web\Reclassering\frontend\web\";s:12:"APPL_MD_PATH";s:16:"/LM/W3SVC/2/ROOT";s:20:"IIS_UrlRewriteModule";s:13:"7,1,1993,2351";s:13:"UNENCODED_URL";s:28:"/document/get-vragen?docId=8";s:19:"IIS_WasUrlRewritten";s:1:"1";s:19:"HTTP_X_ORIGINAL_URL";s:28:"/document/get-vragen?docId=8";s:19:"HTTP_SEC_FETCH_DEST";s:5:"empty";s:19:"HTTP_SEC_FETCH_MODE";s:4:"cors";s:19:"HTTP_SEC_FETCH_SITE";s:11:"same-origin";s:21:"HTTP_SEC_CH_UA_MOBILE";s:2:"?0";s:14:"HTTP_SEC_CH_UA";s:66:""Not;A=Brand";v="99", "Microsoft Edge";v="139", "Chromium";v="139"";s:21:"HTTP_X_REQUESTED_WITH";s:14:"XMLHttpRequest";s:17:"HTTP_X_CSRF_TOKEN";s:88:"gwWdt5915SSAjrwYnAlomHYlJpytHyu0YEhcEJEtaD7CYeyPxia1VLjM_mDoYFrxLhF87eZASN4leWxU-XcvBg==";s:23:"HTTP_SEC_CH_UA_PLATFORM";s:9:""Windows"";s:15:"HTTP_USER_AGENT";s:125:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********";s:12:"HTTP_REFERER";s:30:"http://localhost:8005/document";s:9:"HTTP_HOST";s:14:"localhost:8005";s:11:"HTTP_COOKIE";s:468:"_identity-frontend=20825fca1ba33f2e9fa7551c261673418774e609a156b6153cb0373705fbe6f1a%3A2%3A%7Bi%3A0%3Bs%3A18%3A%22_identity-frontend%22%3Bi%3A1%3Bs%3A46%3A%22%5B7%2C%226126QpFT0R6UeNHmscglqzlP31CnBjEU%22%2C2592000%5D%22%3B%7D; advanced-frontend-fmz=ervhi1enqsp0ttdsohu3ko42j8; _csrf-frontend=29b65eda4e20db655928f7846630561c97757c0d136864e740993ee29b615c33a%3A2%3A%7Bi%3A0%3Bs%3A14%3A%22_csrf-frontend%22%3Bi%3A1%3Bs%3A32%3A%22Adq8YSPp8BBxti2iX4ZqK_cjE10DhZG8%22%3B%7D";s:20:"HTTP_ACCEPT_LANGUAGE";s:23:"en-US,en;q=0.9,nl;q=0.8";s:20:"HTTP_ACCEPT_ENCODING";s:23:"gzip, deflate, br, zstd";s:11:"HTTP_ACCEPT";s:3:"*/*";s:19:"HTTP_CONTENT_LENGTH";s:1:"0";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:9:"FCGI_ROLE";s:9:"RESPONDER";s:8:"PHP_SELF";s:10:"/index.php";s:18:"REQUEST_TIME_FLOAT";d:**********.958186;s:12:"REQUEST_TIME";i:**********;}s:3:"GET";a:1:{s:5:"docId";s:1:"8";}s:4:"POST";a:0:{}s:6:"COOKIE";a:3:{s:18:"_identity-frontend";s:158:"20825fca1ba33f2e9fa7551c261673418774e609a156b6153cb0373705fbe6f1a:2:{i:0;s:18:"_identity-frontend";i:1;s:46:"[7,"6126QpFT0R6UeNHmscglqzlP31CnBjEU",2592000]";}";s:21:"advanced-frontend-fmz";s:26:"ervhi1enqsp0ttdsohu3ko42j8";s:14:"_csrf-frontend";s:140:"29b65eda4e20db655928f7846630561c97757c0d136864e740993ee29b615c33a:2:{i:0;s:14:"_csrf-frontend";i:1;s:32:"Adq8YSPp8BBxti2iX4ZqK_cjE10DhZG8";}";}s:5:"FILES";a:0:{}s:7:"SESSION";a:3:{s:7:"__flash";a:0:{}s:4:"__id";i:7;s:9:"__authKey";s:32:"6126QpFT0R6UeNHmscglqzlP31CnBjEU";}}";s:4:"user";s:1448:"a:5:{s:2:"id";i:7;s:8:"identity";a:12:{s:2:"id";s:1:"7";s:8:"username";s:12:"'medewerker'";s:8:"auth_key";s:34:"'6126QpFT0R6UeNHmscglqzlP31CnBjEU'";s:13:"password_hash";s:62:"'$2y$13$0DIFqBhjLTVgZctJptjzeOjd3WWC31ECxIYfpDBiaO7VtCzqVfuVC'";s:20:"password_reset_token";s:4:"null";s:5:"email";s:20:"'<EMAIL>'";s:6:"status";s:2:"10";s:10:"created_at";s:10:"1743699359";s:10:"updated_at";s:10:"1749726911";s:18:"verification_token";s:4:"null";s:7:"role_id";s:1:"4";s:12:"access_token";s:4:"null";}s:10:"attributes";a:12:{i:0;a:2:{s:9:"attribute";s:2:"id";s:5:"label";s:2:"Id";}i:1;a:2:{s:9:"attribute";s:8:"username";s:5:"label";s:8:"Username";}i:2;a:2:{s:9:"attribute";s:8:"auth_key";s:5:"label";s:8:"Auth Key";}i:3;a:2:{s:9:"attribute";s:13:"password_hash";s:5:"label";s:8:"Password";}i:4;a:2:{s:9:"attribute";s:20:"password_reset_token";s:5:"label";s:20:"Password Reset Token";}i:5;a:2:{s:9:"attribute";s:5:"email";s:5:"label";s:5:"Email";}i:6;a:2:{s:9:"attribute";s:6:"status";s:5:"label";s:6:"Status";}i:7;a:2:{s:9:"attribute";s:10:"created_at";s:5:"label";s:10:"Created At";}i:8;a:2:{s:9:"attribute";s:10:"updated_at";s:5:"label";s:10:"Updated At";}i:9;a:2:{s:9:"attribute";s:18:"verification_token";s:5:"label";s:18:"Verification Token";}i:10;a:2:{s:9:"attribute";s:7:"role_id";s:5:"label";s:4:"Role";}i:11;a:2:{s:9:"attribute";s:12:"access_token";s:5:"label";s:12:"Access Token";}}s:13:"rolesProvider";N;s:19:"permissionsProvider";N;}";s:5:"asset";s:6:"a:0:{}";s:7:"summary";a:13:{s:3:"tag";s:13:"68b1b58316d5e";s:3:"url";s:49:"http://localhost:8005/document/get-vragen?docId=8";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:3:"::1";s:4:"time";d:**********.958186;s:10:"statusCode";i:200;s:8:"sqlCount";i:25;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:8109272;s:14:"processingTime";d:0.17769312858581543;}s:10:"exceptions";a:0:{}}