<?php

namespace common\components;

use Yii;
use yii\base\Component;
use yii\helpers\Inflector;

class Bread<PERSON>rumbsHelper extends Component
{
    public $map = [];

    public function generate()
    {
        $route = Yii::$app->controller->route;

        if (isset($this->map[$route])) {
            $breadcrumbs = [];
            foreach ($this->map[$route] as $item) {
                $breadcrumbs[] = is_array($item) ? $item : ['label' => $item];
            }
            Yii::$app->view->params['breadcrumbs'] = $breadcrumbs;
            return;
        }

        [$controller, $action] = explode('/', $route);
        $breadcrumbs = [
            ['label' => Inflector::camel2words($controller), 'url' => ["/$controller/index"]],
            ['label' => Inflector::camel2words($action)],
        ];
        Yii::$app->view->params['breadcrumbs'] = $breadcrumbs;
    }
}
