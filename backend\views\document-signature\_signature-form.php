<?php

use yii\helpers\Html;
use yii\bootstrap5\ActiveForm;
use dominus77\sweetalert2\Asset;

/* @var $this yii\web\View */
/* @var $model common\models\DocumentSignature */
/* @var $form yii\widgets\ActiveForm */

$documentId = Yii::$app->request->get('id') ?: $model->document_id;

?>

<div class="documents-signature-form">

    <?php $form = ActiveForm::begin([
        'id' => 'signature-form',
        'action' => ['document-signature/save-signature', 'id' => $documentId],
        'method' => 'post',
        'options' => [
            'class' => 'signature-form',
            'data-pjax' => false // Prevent pjax interference
        ]
    ]); ?>

    <label class="w-100">Handtekening</label>
    <canvas id="signature-pad" width="400" height="200" style="border: 1px solid #ccc; border-radius: 8px;"></canvas><br>
    <?= Html::activeHiddenInput($model, 'signature_base64', ['id' => 'signature-input']) ?>

    <div class="form-group mt-3">
        <button type="button" id="clear" class="btn btn-light">Clear</button>
        <?= Html::submitButton('Save', [
            'class' => 'btn btn-outline-primary',
            'id' => 'save-signature-btn'
        ]) ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>

<?php
$js = <<<JS
// Initialize signature pad
let canvas = document.getElementById("signature-pad");
let signaturePad = new SignaturePad(canvas);

// Helper function to show toast notifications
function showToast(icon, title, timer = 3000) {
    Swal.fire({
        toast: true,
        position: 'top-end',
        icon: icon,
        title: title,
        showConfirmButton: false,
        timer: timer,
        timerProgressBar: true
    });
}

// Clear button functionality
document.getElementById("clear").addEventListener("click", () => {
    signaturePad.clear();
    document.getElementById('signature-input').value = '';
});

// Handle form submission for signature form
jQuery(document).off('submit', '#signature-form').on('submit', '#signature-form', function(e) {
    e.preventDefault();
    e.stopPropagation();

    if (signaturePad.isEmpty()) {
        showToast('warning', 'Please provide a signature before saving');
        return false;
    }

    // Get signature data
    let dataURL = signaturePad.toDataURL('image/png');
    document.getElementById('signature-input').value = dataURL;

    let formElement = this;
    let formData = new FormData(formElement);

    fetch(formElement.action, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: formData,
    })
    .then(response => {
        if (!response.ok) throw new Error('HTTP error ' + response.status);
        return response.json();
    })
    .then(response => {
        if (response.success) {
            jQuery('#ajaxCrudModal').modal('hide');
            jQuery('#signature-modal').modal('hide');
        setTimeout(function() {
            location.reload();
        }, 1200); // Wait for toast to show before reload
            showToast('success', response.message || 'Signature saved successfully!');
        } else {
            showToast('error', response.message || 'Error saving signature', 4000);
        }
    })
    .catch(error => {
        console.error('Fetch error:', error);
        showToast('error', 'Error processing server response', 4000);
    });

    return false;
});

// Reinitialize signature pad when modal is shown
jQuery(document).on('shown.bs.modal', '#signature-modal', function() {
    console.log('Modal shown, reinitializing signature pad');
    if (typeof SignaturePad !== 'undefined') {
        let canvas = document.getElementById("signature-pad");
        if (canvas) {
            // Destroy existing instance if it exists
            if (signaturePad) {
                signaturePad.clear();
                signaturePad.off(); // Remove event listeners if available
            }
            // Create new instance
            signaturePad = new SignaturePad(canvas);
        }
    }
});
JS;
$this->registerJs($js);
?>