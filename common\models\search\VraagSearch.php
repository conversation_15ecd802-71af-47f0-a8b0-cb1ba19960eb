<?php

namespace common\models\search;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\Vraag;

/**
 * VraagSearch represents the model behind the search form about `common\models\Vraag`.
 */
class VraagSearch extends Vraag
{
    public $globalSearch;

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'created_by', 'updated_by'], 'integer'],
            [['vraag', 'created_at', 'updated_at'], 'safe'],
            [['globalSearch'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = Vraag::find()
            ->joinWith(['createdBy', 'updatedBy']);

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $this->load($params);

        if (!$this->validate()) {
            return $dataProvider;
        }

        // Add global search condition
        if (!empty($this->globalSearch)) {
            $query->andWhere([
                'or',
                ['like', 'vraag.vraag', $this->globalSearch],
                ['like', 'user.username', $this->globalSearch], // Assuming user table for created/updated by
            ]);
        }

        $query->andFilterWhere([
            'id' => $this->id,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'created_by' => $this->created_by,
            'updated_by' => $this->updated_by,
        ]);

        $query->andFilterWhere(['like', 'vraag', $this->vraag]);

        return $dataProvider;
    }
}
