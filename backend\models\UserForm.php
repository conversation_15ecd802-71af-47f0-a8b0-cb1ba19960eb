<?php

namespace backend\models;

use Yii;
use yii\base\Model;
use common\models\User;

class UserForm extends Model
{
    public $username;
    public $email;
    public $password;
    public $status;
    public $role_id;

    private $_user;

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            ['username', 'trim'],
            ['username', 'required'],
            ['username', 'string', 'min' => 2, 'max' => 255],
            [
                'username',
                'unique',
                'targetClass' => '\common\models\User',
                'message' => 'This username has already been taken.',
                'filter' => function ($query) {
                    if (!$this->_user) {
                        return $query;
                    }
                    return $query->andWhere(['not', ['id' => $this->_user->id]]);
                }
            ],

            ['email', 'trim'],
            ['email', 'required'],
            ['email', 'email'],
            ['email', 'string', 'max' => 255],
            [
                'email',
                'unique',
                'targetClass' => '\common\models\User',
                'message' => 'This email address has already been taken.',
                'filter' => function ($query) {
                    if (!$this->_user) {
                        return $query;
                    }
                    return $query->andWhere(['not', ['id' => $this->_user->id]]);
                }
            ],

            ['password', 'required', 'when' => function ($model) {
                return $this->_user === null;
            }],
            ['password', 'string', 'min' => Yii::$app->params['user.passwordMinLength']],

            ['status', 'default', 'value' => User::STATUS_ACTIVE],
            ['status', 'in', 'range' => [User::STATUS_ACTIVE, User::STATUS_INACTIVE, User::STATUS_DELETED]],

            ['role_id', 'integer'],
            ['role_id', 'required'],
        ];
    }

    /**
     * Constructor.
     * @param User $user if provided, will update this user instead of creating new one
     * @param array $config name-value pairs that will be used to initialize the object properties
     */
    public function __construct($user = null, $config = [])
    {
        $this->_user = $user;
        if ($user !== null) {
            $this->username = $user->username;
            $this->email = $user->email;
            $this->status = $user->status;
            $this->role_id = $user->role_id;
        }
        parent::__construct($config);
    }

    /**
     * Creates or updates user.
     *
     * @return User|null the saved model or null if saving fails
     */
    public function save()
    {
        if (!$this->validate()) {
            return null;
        }

        $user = $this->_user ?? new User();
        $user->username = $this->username;
        $user->email = $this->email;
        $user->status = $this->status;
        $user->role_id = $this->role_id;

        if ($this->password) {
            $user->setPassword($this->password);
        }

        if ($this->_user === null) {
            $user->generateAuthKey();
        }

        return $user->save() ? $user : null;
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'username' => 'Username',
            'email' => 'Email',
            'password' => 'Password',
            'status' => 'Status',
            'role_id' => 'Role',
        ];
    }

    /**
     * @return bool whether this instance is a new record
     */
    public function getIsNewRecord()
    {
        return $this->_user === null;
    }

    /**
     * @return int|null the user ID if this is an existing record, null otherwise
     */
    public function getUserId()
    {
        return $this->_user ? $this->_user->id : null;
    }
}
