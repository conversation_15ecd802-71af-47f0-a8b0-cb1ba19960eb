<?php

namespace backend\controllers;

use common\components\services\SyncDataService;
use common\components\services\GedetineerdeService;
use Yii;
use common\models\Gedetineerde;
use common\models\search\GedetineerdeSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use \yii\web\Response;
use yii\helpers\Html;

/**
 * GedetineerdeController implements the CRUD actions for Gedetineerde model.
 */
class GedetineerdeController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['post'],
                    'bulkdelete' => ['post'],
                ],
            ],
        ];
    }

    /**
     * Lists all Gedetineerde models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new GedetineerdeSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }


    /**
     * Displays a single Gedetineerde model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        $request = Yii::$app->request;
        $isApiRecord = $request->get('source') === 'api';
        $gedetService = new GedetineerdeService();

        $model = null;

        if ($isApiRecord) {
            $model = $gedetService->getOneGedetineerde($id);
        } else {
            $model = $this->findModel($id);
        }

        if ($request->isAjax) {
            Yii::$app->response->format = Response::FORMAT_JSON;
            return [
                'title' => "Afspraak #" . $id,
                'content' => $this->renderAjax('view', [
                    'model' => $model,
                    'isApiRecord' => $isApiRecord
                ]),
                'footer' => Html::button(Yii::t('yii2-ajaxcrud', 'Close'), ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal']) .
                    $isApiRecord ? null : Html::a(Yii::t('yii2-ajaxcrud', 'Update'), ['update', 'id' => $id], ['class' => 'btn btn-primary', 'role' => 'modal-remote'])
            ];
        } else {
            return $this->render('view', [
                'model' => $this->findModel($id),
                'isApiRecord' => $isApiRecord

            ]);
        }
    }

    /**
     * Creates a new Gedetineerde model.
     * For ajax request will return json object
     * and for non-ajax request if creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $request = Yii::$app->request;
        $model = new Gedetineerde();

        if ($request->isAjax) {
            /*
            *   Process for ajax request
            */
            Yii::$app->response->format = Response::FORMAT_JSON;
            if ($request->isGet) {
                return [
                    'title' => Yii::t('yii2-ajaxcrud', 'Create New') . " Gedetineerde",
                    'content' => $this->renderAjax('create', [
                        'model' => $model,
                    ]),
                    'footer' => Html::button(Yii::t('yii2-ajaxcrud', 'Close'), ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal']) .
                        Html::button(Yii::t('yii2-ajaxcrud', 'Create'), ['class' => 'btn btn-primary', 'type' => 'submit'])
                ];
            } else if ($model->load($request->post()) && $model->save()) {
                return [
                    'forceReload' => '#crud-datatable-pjax',
                    'title' => Yii::t('yii2-ajaxcrud', 'Create New') . " Gedetineerde",
                    'content' => '<span class="text-success">' . Yii::t('yii2-ajaxcrud', 'Create') . ' Gedetineerde ' . Yii::t('yii2-ajaxcrud', 'Success') . '</span>',
                    'footer' =>  Html::button(Yii::t('yii2-ajaxcrud', 'Close'), ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal']) .
                        Html::a(Yii::t('yii2-ajaxcrud', 'Create More'), ['create'], ['class' => 'btn btn-primary', 'role' => 'modal-remote'])
                ];
            } else {
                return [
                    'title' => Yii::t('yii2-ajaxcrud', 'Create New') . " Gedetineerde",
                    'content' => $this->renderAjax('create', [
                        'model' => $model,
                    ]),
                    'footer' => Html::button(Yii::t('yii2-ajaxcrud', 'Close'), ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal']) .
                        Html::button(Yii::t('yii2-ajaxcrud', 'Save'), ['class' => 'btn btn-primary', 'type' => 'submit'])
                ];
            }
        } else {
            /*
            *   Process for non-ajax request
            */
            if ($model->load($request->post()) && $model->save()) {
                return $this->redirect(['view', 'id' => $model->id]);
            } else {
                return $this->render('create', [
                    'model' => $model,
                ]);
            }
        }
    }

    /**
     * Updates an existing Gedetineerde model.
     * For ajax request will return json object
     * and for non-ajax request if update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $request = Yii::$app->request;
        $model = $this->findModel($id);

        if ($request->isAjax) {
            /*
            *   Process for ajax request
            */
            Yii::$app->response->format = Response::FORMAT_JSON;
            if ($request->isGet) {
                return [
                    'title' => Yii::t('yii2-ajaxcrud', 'Update') . " Gedetineerde #" . $id,
                    'content' => $this->renderAjax('update', [
                        'model' => $model,
                    ]),
                    'footer' => Html::button(Yii::t('yii2-ajaxcrud', 'Close'), ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal']) .
                        Html::button(Yii::t('yii2-ajaxcrud', 'Save'), ['class' => 'btn btn-primary', 'type' => 'submit'])
                ];
            } else if ($model->load($request->post()) && $model->save()) {
                return [
                    'forceReload' => '#crud-datatable-pjax',
                    'title' => "Gedetineerde #" . $id,
                    'content' => $this->renderAjax('view', [
                        'model' => $model,
                    ]),
                    'footer' => Html::button(Yii::t('yii2-ajaxcrud', 'Close'), ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal']) .
                        Html::a(Yii::t('yii2-ajaxcrud', 'Update'), ['update', 'id' => $id], ['class' => 'btn btn-primary', 'role' => 'modal-remote'])
                ];
            } else {
                return [
                    'title' => Yii::t('yii2-ajaxcrud', 'Update') . " Gedetineerde #" . $id,
                    'content' => $this->renderAjax('update', [
                        'model' => $model,
                    ]),
                    'footer' => Html::button(Yii::t('yii2-ajaxcrud', 'Close'), ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal']) .
                        Html::button(Yii::t('yii2-ajaxcrud', 'Save'), ['class' => 'btn btn-primary', 'type' => 'submit'])
                ];
            }
        } else {
            /*
            *   Process for non-ajax request
            */
            if ($model->load($request->post()) && $model->save()) {
                return $this->redirect(['view', 'id' => $model->id]);
            } else {
                return $this->render('update', [
                    'model' => $model,
                ]);
            }
        }
    }

    /**
     * Delete an existing Gedetineerde model.
     * For ajax request will return json object
     * and for non-ajax request if deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $request = Yii::$app->request;
        $this->findModel($id)->delete();

        if ($request->isAjax) {
            /*
            *   Process for ajax request
            */
            Yii::$app->response->format = Response::FORMAT_JSON;
            return ['forceClose' => true, 'forceReload' => '#crud-datatable-pjax'];
        } else {
            /*
            *   Process for non-ajax request
            */
            return $this->redirect(['index']);
        }
    }

    /**
     * Delete multiple existing Gedetineerde model.
     * For ajax request will return json object
     * and for non-ajax request if deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionBulkdelete()
    {
        $request = Yii::$app->request;
        $pks = explode(',', $request->post('pks')); // Array or selected records primary keys
        foreach ($pks as $pk) {
            $model = $this->findModel($pk);
            $model->delete();
        }

        if ($request->isAjax) {
            /*
            *   Process for ajax request
            */
            Yii::$app->response->format = Response::FORMAT_JSON;
            return ['forceClose' => true, 'forceReload' => '#crud-datatable-pjax'];
        } else {
            /*
            *   Process for non-ajax request
            */
            return $this->redirect(['index']);
        }
    }

    /**
     * Finds the Gedetineerde model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return Gedetineerde the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = Gedetineerde::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    public function actionImport()
    {
        $searchModel = new GedetineerdeSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        $apiData = [];
        $search = Yii::$app->request->get('search');

        $syncService = new SyncDataService();
        $service = new GedetineerdeService();

        if ($search) {
            // If there's a search query, use the service's search function
            $apiData = $service->search($search)['data'];
        } else {
            // If no search, get data as before
            $syncData = $syncService->getData()['data'];
            if (empty($syncData)) {
                $apiData = $service->getGedetineerde()['data'];
            } else {
                $apiData = $syncData;
            }
        }

        return $this->render('import', [
            'gedetineerdeData' => $apiData,
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }


    /**
     * Debug action to check API data structure
     */
    public function actionDebugApi()
    {
        $syncService = new SyncDataService();
        $gedetineerdeService = new GedetineerdeService();

        echo "<h3>Sync Data:</h3>";
        $syncData = $syncService->getData();
        echo "<pre>" . json_encode($syncData, JSON_PRETTY_PRINT) . "</pre>";

        echo "<h3>Gedetineerde Data:</h3>";
        $gedetineerdeData = $gedetineerdeService->getGedetineerde();
        echo "<pre>" . json_encode($gedetineerdeData, JSON_PRETTY_PRINT) . "</pre>";

        if (!empty($syncData['data'])) {
            $firstRecord = reset($syncData['data']);
            echo "<h3>First Sync Record Structure:</h3>";
            echo "<pre>" . json_encode($firstRecord, JSON_PRETTY_PRINT) . "</pre>";

            if (isset($firstRecord['persoonid']) || isset($firstRecord['pk'])) {
                $id = $firstRecord['persoonid'] ?? $firstRecord['pk'] ?? null;
                if ($id) {
                    echo "<h3>Single Record from API (ID: {$id}):</h3>";
                    $singleRecord = $gedetineerdeService->getOneGedetineerde($id);
                    echo "<pre>" . json_encode($singleRecord, JSON_PRETTY_PRINT) . "</pre>";
                }
            }
        }

        exit;
    }

    /**
     * View recent logs for debugging
     */
    public function actionViewLogs()
    {
        $logFile = Yii::getAlias('@app/runtime/logs/app.log');

        if (file_exists($logFile)) {
            $lines = file($logFile);
            $recentLines = array_slice($lines, -100); // Get last 100 lines

            echo "<h3>Recent Log Entries (last 100 lines):</h3>";
            echo "<pre style='background: #f5f5f5; padding: 10px; max-height: 500px; overflow-y: scroll;'>";
            echo htmlspecialchars(implode('', $recentLines));
            echo "</pre>";
        } else {
            echo "Log file not found at: " . $logFile;
        }

        exit;
    }

    public function actionImportSelected()
    {
        $request = Yii::$app->request;
        $pks = explode(',', $request->post('pks')); // Array or selected records primary keys

        $gedetService = new GedetineerdeService();
        $model = new Gedetineerde();

        $inserted = [];
        foreach ($pks as $pk) {
            // Get single record from gedetineerde API
            $gedetineerde = $gedetService->getOneGedetineerde($pk);

            // Check if gedetineerder already exit in db
            $exists = Gedetineerde::findOne(['source_id' => $pk]);

            // If exists, delete it
            if ($exists) {
                $exists->delete();
            }

            // Assign API data to model attributes
            $mapping = [
                'source_id' => 'persoonid',
                'regnr' => 'regnr',
                'naam' => 'naam',
                'voornaam' => 'voornaam',
                'idnr' => 'idnr',
                'verzekeringskaartnr' => 'verzekeringskaartnr',
                'geboortedatum' => 'geboortedatum'
            ];

            // Map API data to model attributes
            $model = new Gedetineerde();
            foreach ($mapping as $modelAttr => $apiAttr) {
                $model->$modelAttr = $gedetineerde[$apiAttr] ?? null;
            }

            if ($model->save()) {
                $inserted[] = $model->attributes;

                // Update the sync status for this record
                $syncService = new SyncDataService();
                $syncUpdateSuccess = $syncService->updateOneRecord($pk);
                if (!$syncUpdateSuccess) {
                    Yii::warning("Failed to update sync status for record with pk: {$pk}", __METHOD__);
                }
            }
        }

        if ($request->isAjax) {
            /*
            *   Process for ajax request
            */
            Yii::$app->response->format = Response::FORMAT_JSON;
            return ['forceClose' => true, 'forceReload' => '#model-datatable'];
        } else {
            /*
            *   Process for non-ajax request
            */
            return $this->redirect(['index']);
        }
    }

    public function findRecordInApi($recordId)
    {
        Yii::info("Looking for API record with ID: {$recordId}", __METHOD__);

        // First try to find in SyncDataService
        $syncService = new SyncDataService();
        $syncResponse = $syncService->getData();
        $syncData = $syncResponse['data'] ?? [];

        if (!empty($syncData)) {
            Yii::info("Searching in " . count($syncData) . " sync records", __METHOD__);

            foreach ($syncData as $record) {
                // Check all possible ID fields
                if ((isset($record['persoonid']) && $record['persoonid'] == $recordId) ||
                    (isset($record['id']) && $record['id'] == $recordId) ||
                    (isset($record['pk']) && $record['pk'] == $recordId)
                ) {
                    Yii::info("Found record in sync data: " . json_encode($record), __METHOD__);
                    return $record;
                }
            }
        }

        // If not found in sync data, try GedetineerdeService
        Yii::info("Record not found in sync data, trying GedetineerdeService", __METHOD__);
        $service = new GedetineerdeService();
        $result = $service->getOneGedetineerde($recordId);

        if ($result && isset($result['data'])) {
            Yii::info("Found record in GedetineerdeService: " . json_encode($result['data']), __METHOD__);
            return $result['data'];
        }

        Yii::warning("API record with ID {$recordId} not found in any service", __METHOD__);
        throw new NotFoundHttpException('The requested record does not exist in the API.');
    }
}
