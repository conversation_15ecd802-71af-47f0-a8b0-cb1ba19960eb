<?php
Yii::set<PERSON><PERSON><PERSON>('@common', dirname(__DIR__));
Yii::set<PERSON><PERSON><PERSON>('@frontend', dirname(dirname(__DIR__)) . '/frontend');
Yii::set<PERSON><PERSON><PERSON>('@backend', dirname(dirname(__DIR__)) . '/backend');
Yii::set<PERSON><PERSON><PERSON>('@console', dirname(dirname(__DIR__)) . '/console');

require_once __DIR__ . '/../helpers/functions.php';
require_once __DIR__ . '/../helpers/workflowHelper.php';
require_once __DIR__ . '/../components/UserIdentity.php';
