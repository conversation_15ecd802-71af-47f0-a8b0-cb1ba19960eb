<?php

namespace common\models\search;

use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\DocumentUpload;
use Yii;

class DocumentUploadSearch extends DocumentUpload
{
    public $search_query;
    public $date_from;
    public $date_to;

    public function rules()
    {
        return [
            [['search_query', 'file_type'], 'string'],
            [['date_from', 'date_to'], 'date', 'format' => 'php:Y-m-d'],
        ];
    }

    public function scenarios()
    {
        return Model::scenarios();
    }

    public function search($params)
    {
        $query = DocumentUpload::find();

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'pagination' => [
                'pageSize' => 20,
            ],
            'sort' => [
                'defaultOrder' => [
                    'created_at' => SORT_DESC,
                ],
            ],
        ]);

        $this->load($params);

        if (!$this->validate()) {
            Yii::error("Search validation failed: " . json_encode($this->errors), __METHOD__);
            return $dataProvider;
        }

        $query->andFilterWhere(['like', 'original_filename', $this->search_query])
            ->andFilterWhere(['file_type' => $this->file_type])
            ->andFilterWhere(['>=', 'created_at', $this->date_from ? strtotime($this->date_from . ' 00:00:00') : null])
            ->andFilterWhere(['<=', 'created_at', $this->date_to ? strtotime($this->date_to . ' 23:59:59') : null]);

        return $dataProvider;
    }
}
