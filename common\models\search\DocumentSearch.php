<?php

namespace common\models\search;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\Document;

/**
 * DocumentSearch represents the model behind the search form about `common\models\Document`.
 */
class DocumentSearch extends Document
{
    public $globalSearch;
    public $documentType;
    public $createdByUsername;
    public $updatedByUsername;
    public $filename; // New field added

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'documenttype_id', 'created_by', 'updated_by'], 'integer'],
            [['status', 'created_at', 'updated_at', 'globalSearch', 'documentType', 'createdByUsername', 'updatedByUsername', 'filename'], 'safe'], // Include filename
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = Document::find()
            ->alias('r')
            ->joinWith([
                'documentType d',  // Changed from 'documentType' to 'documenttype'
                'createdBy cb',
                'updatedBy ub',
                'documentAntwoords ra',
                'documentFeedbacks rf'
            ]);

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => [
                'defaultOrder' => [
                    'id' => SORT_DESC
                ],
                'attributes' => [
                    'id',
                    'status',
                    'created_at',
                    'updated_at',
                    'documentType' => [
                        'asc' => ['d.type' => SORT_ASC],
                        'desc' => ['d.type' => SORT_DESC],
                    ],
                    'createdByUsername' => [
                        'asc' => ['cb.username' => SORT_ASC],
                        'desc' => ['cb.username' => SORT_DESC],
                    ],
                    'updatedByUsername' => [
                        'asc' => ['ub.username' => SORT_ASC],
                        'desc' => ['ub.username' => SORT_DESC],
                    ],
                    'filename' => [ // Add sorting for filename
                        'asc' => ['r.filename' => SORT_ASC],
                        'desc' => ['r.filename' => SORT_DESC],
                    ],
                ],
            ],
        ]);

        $this->load($params);

        if (!$this->validate()) {
            return $dataProvider;
        }

        // Add global search condition
        if (!empty($this->globalSearch)) {
            $query->andWhere([
                'or',
                ['like', 'r.id', $this->globalSearch],
                ['like', 'r.status', $this->globalSearch],
                ['like', 'd.type', $this->globalSearch],
                ['like', 'cb.username', $this->globalSearch],
                ['like', 'ub.username', $this->globalSearch],
                ['like', 'ra.antwoord', $this->globalSearch],
                ['like', 'rf.comment', $this->globalSearch],
                ['like', 'r.filename', $this->globalSearch],  // Changed from r.document_type to r.filename
            ]);
        }

        // Grid filtering conditions
        $query->andFilterWhere([
            'r.id' => $this->id,
            'r.documenttype_id' => $this->documenttype_id,
            'r.created_by' => $this->created_by,
            'r.updated_by' => $this->updated_by,
            'r.created_at' => $this->created_at,
            'r.updated_at' => $this->updated_at,
        ]);

        $query->andFilterWhere(['like', 'r.status', $this->status])
            ->andFilterWhere(['like', 'd.type', $this->documentType])  // Changed from documentNaamNaam
            ->andFilterWhere(['like', 'cb.username', $this->createdByUsername])
            ->andFilterWhere(['like', 'ub.username', $this->updatedByUsername])
            ->andFilterWhere(['like', 'r.filename', $this->filename]); // Add filtering for filename

        // Remove duplicates that might occur due to joins
        $query->groupBy('r.id');

        return $dataProvider;
    }
}
