<?php

use yii\helpers\Url;
use yii\helpers\Html;
use yii\bootstrap5\Modal;
use kartik\grid\GridView;
use yii2ajaxcrud\ajaxcrud\CrudAsset;
use yii2ajaxcrud\ajaxcrud\BulkButtonWidget;

/* @var $this yii\web\View */
/* @var $gedetineerdeData array */

$this->title = 'Import Gedetineerde';
// $this->params['breadcrumbs'][] = ['label' => 'Gedetineerde', 'url' => ['index']];
// $this->params['breadcrumbs'][] = $this->title;

CrudAsset::register($this);

$ApiDataProvider = new \yii\data\ArrayDataProvider([
    'allModels' => $gedetineerdeData,
    'pagination' => [
        'pageSize' => 20,
    ],
]);

?>
<div class="gedetineerde-import">
    <div class="mb-2" style="max-width: 1250px;">
        <h3>Import de gedetineerde data van het KPA systeem naar het FMZ systeem.</h3>
        <p class="text-secondary">Lorem ipsum dolor sit amet consectetur adipisicing elit. Maxime nisi sequi eos pariatur maiores cupiditate molestias aliquid illum eius? Rerum reprehenderit eius similique assumenda laboriosam, ex dolor totam sapiente molestiae.</p>
    </div>
    <div class="row">
        <div id="ajaxCrudDatatableApi" class="col-md-6">
            <?= GridView::widget([
                'id' => 'api-datatable',
                'dataProvider' => $ApiDataProvider,
                'pjax' => true,
                'columns' => require(__DIR__ . '/_api-columns.php'),
                'toolbar' => [
                    [
                        'content' =>
                        Html::a(
                            '<i class="fa fa-redo"></i>',
                            [''],
                            ['data-pjax' => 1, 'class' => 'btn btn-outline-success', 'title' => Yii::t('yii2-ajaxcrud', 'Reset Grid')]
                        ) .
                            '{toggleData}'
                    ],
                ],
                'striped' => false,
                'hover' => true,
                'condensed' => true,
                'responsive' => true,
                'panel' => [
                    'type' => 'default',
                    'heading' => '<strong>Gedetineerden van KPA</strong>',
                    'before' =>
                    '<div class="d-flex align-items-center gap-2">' .
                        Html::beginForm(['import'], 'get', ['data-pjax' => 1, 'class' => 'd-flex']) .
                        '<div class="input-group" style="width: auto;">' .
                        Html::input('search', 'search', Yii::$app->request->get('search'), [
                            'class' => 'form-control',
                            'style' => 'width: 300px;',
                            'placeholder' => 'Zoek op naam, voornaam of id nummer...',
                            'aria-label' => 'Search',
                            'aria-describedby' => 'search-addon'
                        ]) .
                        '<button class="btn btn-outline-primary" type="submit" id="search-addon">
                            <i class="fa fa-search"></i>
                        </button>' .
                        '</div>' .
                        Html::endForm() .
                        '</div>',
                    'after' => BulkButtonWidget::widget([
                        'buttons' => Html::a(
                            '<i data-lucide="import" style="width:16px; height:16px"></i>&nbsp; Importeer',
                            ['import-selected'], // You need to create this action in your controller
                            [
                                'class' => 'btn btn-outline-info ms-2 btn-sm',
                                'role' => 'modal-remote-bulk',
                                'data-confirm' => false,
                                'data-method' => false,
                                'data-request-method' => 'post',
                                'data-confirm-title' => 'Importeer naar systeem',
                                'data-confirm-message' => 'Wilt u de geselecteerde gedetineerden importeren?'
                            ]
                        ),
                    ]) . '<div class="clearfix"></div>',
                ]
            ]) ?>
        </div>
        <div id="ajaxCrudDatatableModel" class="col-md-6">
            <?= GridView::widget([
                'id' => 'model-datatable',
                'dataProvider' => $dataProvider,
                'filterModel' => $searchModel,
                'pjax' => true,
                'columns' => require(__DIR__ . '/_columns.php'),
                'toolbar' => [
                    [
                        'content' =>
                        Html::a(
                            '<i class="fa fa-redo"></i>',
                            [''],
                            ['data-pjax' => 1, 'class' => 'btn btn-outline-success', 'title' => Yii::t('yii2-ajaxcrud', 'Reset Grid')]
                        ) .
                            '{toggleData}' .
                            '{export}'
                    ],
                ],
                'striped' => false,
                'hover' => true,
                'condensed' => true,
                'responsive' => true,
                'panel' => [
                    'type' => 'default',
                    'heading' => '<strong/>Gedetineerden in Systeem</strong>',
                    'before' => '',
                    'after' => BulkButtonWidget::widget([
                        'buttons' => Html::a(
                            '<i class="fa fa-trash"></i>&nbsp; ' . Yii::t('yii2-ajaxcrud', 'Delete All'),
                            ["bulkdelete"],
                            [
                                'class' => 'btn btn-danger btn-xs',
                                'role' => 'modal-remote-bulk',
                                'data-confirm' => false,
                                'data-method' => false, // for overide yii data api
                                'data-request-method' => 'post',
                                'data-confirm-title' => Yii::t('yii2-ajaxcrud', 'Delete'),
                                'data-confirm-message' => Yii::t('yii2-ajaxcrud', 'Delete Confirm')
                            ]
                        ),
                    ]) .
                        '<div class="clearfix"></div>',
                ]
            ]) ?>
        </div>
    </div>

</div>
<?php Modal::begin([
    "id" => "ajaxCrudModal",
    "footer" => "", // always need it for jquery plugin
    "clientOptions" => [
        "tabindex" => false,
        "backdrop" => "static",
        "keyboard" => false,
    ],
    "options" => [
        "tabindex" => false
    ]
]) ?>
<?php Modal::end(); ?>

<script>
    // Debug script to log form submissions
    $(document).ready(function() {
        // Log when checkboxes are selected
        $(document).on('change', 'input[type="checkbox"]', function() {
            console.log('Checkbox changed:', this.value, this.checked);
        });

        // Log when import button is clicked
        $(document).on('click', 'a[href*="import-selected"]', function(e) {
            var selectedValues = [];
            $('input[type="checkbox"]:checked').each(function() {
                if (this.value !== 'on') { // Skip the "select all" checkbox
                    selectedValues.push(this.value);
                }
            });
            console.log('Import button clicked with selected values:', selectedValues);
            console.log('Selected values as string:', selectedValues.join(','));
        });
    });
</script>