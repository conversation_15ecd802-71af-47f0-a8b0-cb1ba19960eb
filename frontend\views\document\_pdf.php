<?php
/* @var $model common\models\Rapport */
/* @var $documentData array */

use yii\helpers\Html;

$inputStyle = "
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 8px 12px;
    min-height: 24px;
    margin: 4px 0;
    width: 100%;
";

$labelStyle = "
    display: block;
    font-weight: bold;
    margin-bottom: 4px;
    color: #495057;
";

$questionContainerStyle = "
    margin-bottom: 20px;
";
?>

<div class="document-pdf">
    <?php if (empty($documentData)): ?>
        <div style="text-align: center; padding: 20px; background-color: #fff3cd; border: 1px solid #ffeeba; border-radius: 4px; color: #856404;">
            <h2 style="margin-bottom: 10px;">No Report Data Available</h2>
            <p>The requested report data could not be found or is empty.</p>
        </div>
    <?php else: ?>
        <!-- Singature Alert -->
        <?php if (!empty($documentData['signatures'])): ?>
            <div style="padding: 15px; background-color:rgb(241, 209, 209); border: 1px solidrgb(243, 180, 180); border-radius: 4px; color:rgb(216, 20, 20); margin-bottom:40px">
                <p style="margin: 0;">Rapport ondertekend door:</p>
                <?php foreach ($documentData['signatures'] as $signature): ?>
                    <p style="margin: 0;"><?= Html::encode($signature['user']['username'] ?? 'Unknown User') ?></p>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>

        <!-- Header Section -->
        <div style="margin-bottom: 30px; border-bottom: 2px solid #dee2e6; padding-bottom: 20px;">
            <h1 style="color: #2c3e50; font-size: 24px; margin-bottom: 18px; font-weight:bold">
                <?= Html::encode($documentData['filename'] ?? 'Untitled Report') ?>
            </h1>
            <h3 style="color: #2c3e50; font-size: 18px; margin-bottom: 20px; font-weight: medium;">
                <?= Html::encode($documentData['document']['type'] ?? 'No Document Name') ?>
            </h3>

            <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                <tr>
                    <td style="width: 33%; padding: 8px;">
                        <span style="color: #6c757d; font-size: 12px;">Status</span><br>
                        <strong style="color: red;"><?= Html::encode($documentData['status'] ?? 'Unknown') ?></strong>
                    </td>
                    <td style="width: 33%; padding: 8px;">
                        <span style="color: #6c757d; font-size: 12px;">Created By</span><br>
                        <strong><?= Html::encode(isset($documentData['created_by']['username']) ? $documentData['created_by']['username'] : 'Unknown User') ?></strong>
                    </td>
                    <td style="width: 33%; padding: 8px;">
                        <span style="color: #6c757d; font-size: 12px;">Created At</span><br>
                        <strong><?= isset($documentData['created_at']) ? Yii::$app->formatter->asDatetime($documentData['created_at']) : 'Unknown Date' ?></strong>
                    </td>
                </tr>
            </table>
        </div>

        <!-- Questions and Answers Section -->
        <?php if (!empty($documentData['questions_and_answers'])): ?>
            <div class="document-answers">
                <?php foreach ($documentData['questions_and_answers'] as $qa): ?>
                    <?php if (isset($qa['question']) && isset($qa['answer'])): ?>
                        <div style="<?= $questionContainerStyle ?>">
                            <!-- Question Label -->
                            <label style="<?= $labelStyle ?>">
                                <?= Html::encode($qa['question']['number'] ?? '?') ?>.
                                <?= Html::encode($qa['question']['text'] ?? 'Question text not available') ?>
                            </label>

                            <!-- Answer Input-style Box -->
                            <div style="<?= $inputStyle ?>">
                                <?= Html::encode($qa['answer']['text'] ?? 'No answer provided') ?>
                            </div>
                        </div>
                    <?php endif; ?>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <div style="padding: 15px; background-color: #e2e3e5; border: 1px solid #d6d8db; border-radius: 4px; color: #383d41;">
                <p style="margin: 0;">No questions and answers available for this report.</p>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</div>