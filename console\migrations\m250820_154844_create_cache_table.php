<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%cache}}`.
 */
class m250820_154844_create_cache_table extends Migration
{
    public function safeUp()
    {
        $this->createTable('cache', [
            'id' => $this->string(128)->notNull(),
            'expire' => $this->integer(),
            'data' => $this->binary(),
            'PRIMARY KEY(id)',
        ]);
    }

    public function safeDown()
    {
        $this->dropTable('cache');
    }
}
