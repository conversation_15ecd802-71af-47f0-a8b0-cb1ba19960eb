a:14:{s:6:"config";s:15911:"a:5:{s:10:"phpVersion";s:5:"8.3.3";s:10:"yiiVersion";s:6:"2.0.53";s:11:"application";a:8:{s:3:"yii";s:6:"2.0.53";s:4:"name";s:3:"FMZ";s:7:"version";s:3:"1.0";s:8:"language";s:5:"en-US";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:5:"8.3.3";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:71:{s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/faker";s:49:"C:\Web\Reclassering\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.7.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:47:"C:\Web\Reclassering\vendor/yiisoft/yii2-gii/src";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:57:"C:\Web\Reclassering\vendor/yiisoft/yii2-symfonymailer/src";}}s:29:"hail812/yii2-adminlte-widgets";a:3:{s:4:"name";s:29:"hail812/yii2-adminlte-widgets";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:25:"@hail812/adminlte/widgets";s:60:"C:\Web\Reclassering\vendor/hail812/yii2-adminlte-widgets/src";}}s:23:"yiisoft/yii2-bootstrap4";a:3:{s:4:"name";s:23:"yiisoft/yii2-bootstrap4";s:7:"version";s:8:"2.0.12.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap4";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-bootstrap4/src";}}s:22:"hail812/yii2-adminlte3";a:3:{s:4:"name";s:22:"hail812/yii2-adminlte3";s:7:"version";s:7:"1.1.9.0";s:5:"alias";a:1:{s:18:"@hail812/adminlte3";s:53:"C:\Web\Reclassering\vendor/hail812/yii2-adminlte3/src";}}s:18:"kartik-v/yii2-mpdf";a:3:{s:4:"name";s:18:"kartik-v/yii2-mpdf";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:12:"@kartik/mpdf";s:49:"C:\Web\Reclassering\vendor/kartik-v/yii2-mpdf/src";}}s:25:"kartik-v/yii2-krajee-base";a:3:{s:4:"name";s:25:"kartik-v/yii2-krajee-base";s:7:"version";s:7:"3.0.5.0";s:5:"alias";a:1:{s:12:"@kartik/base";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-krajee-base/src";}}s:20:"kartik-v/yii2-dialog";a:3:{s:4:"name";s:20:"kartik-v/yii2-dialog";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:14:"@kartik/dialog";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-dialog/src";}}s:23:"kartik-v/yii2-popover-x";a:3:{s:4:"name";s:23:"kartik-v/yii2-popover-x";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:15:"@kartik/popover";s:54:"C:\Web\Reclassering\vendor/kartik-v/yii2-popover-x/src";}}s:21:"kartik-v/yii2-builder";a:3:{s:4:"name";s:21:"kartik-v/yii2-builder";s:7:"version";s:7:"1.6.9.0";s:5:"alias";a:1:{s:15:"@kartik/builder";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-builder/src";}}s:22:"kartik-v/yii2-editable";a:3:{s:4:"name";s:22:"kartik-v/yii2-editable";s:7:"version";s:7:"1.8.0.0";s:5:"alias";a:1:{s:16:"@kartik/editable";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-editable/src";}}s:18:"kartik-v/yii2-grid";a:3:{s:4:"name";s:18:"kartik-v/yii2-grid";s:7:"version";s:7:"3.5.3.0";s:5:"alias";a:1:{s:12:"@kartik/grid";s:49:"C:\Web\Reclassering\vendor/kartik-v/yii2-grid/src";}}s:21:"kartik-v/yii2-helpers";a:3:{s:4:"name";s:21:"kartik-v/yii2-helpers";s:7:"version";s:7:"1.3.9.0";s:5:"alias";a:1:{s:15:"@kartik/helpers";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-helpers/src";}}s:24:"kartik-v/yii2-checkbox-x";a:3:{s:4:"name";s:24:"kartik-v/yii2-checkbox-x";s:7:"version";s:7:"1.0.7.0";s:5:"alias";a:1:{s:16:"@kartik/checkbox";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-checkbox-x/src";}}s:26:"kartik-v/yii2-context-menu";a:3:{s:4:"name";s:26:"kartik-v/yii2-context-menu";s:7:"version";s:7:"1.2.4.0";s:5:"alias";a:1:{s:13:"@kartik/cmenu";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-context-menu/src";}}s:25:"kartik-v/yii2-datecontrol";a:3:{s:4:"name";s:25:"kartik-v/yii2-datecontrol";s:7:"version";s:7:"1.9.9.0";s:5:"alias";a:1:{s:19:"@kartik/datecontrol";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-datecontrol/src";}}s:22:"kartik-v/yii2-sortable";a:3:{s:4:"name";s:22:"kartik-v/yii2-sortable";s:7:"version";s:7:"1.2.2.0";s:5:"alias";a:1:{s:16:"@kartik/sortable";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-sortable/src";}}s:25:"kartik-v/yii2-field-range";a:3:{s:4:"name";s:25:"kartik-v/yii2-field-range";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:13:"@kartik/field";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-field-range/src";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:8:"2.0.16.0";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-httpclient/src";}}s:25:"kartik-v/yii2-detail-view";a:3:{s:4:"name";s:25:"kartik-v/yii2-detail-view";s:7:"version";s:7:"1.8.7.0";s:5:"alias";a:1:{s:14:"@kartik/detail";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-detail-view/src";}}s:24:"kartik-v/yii2-date-range";a:3:{s:4:"name";s:24:"kartik-v/yii2-date-range";s:7:"version";s:7:"1.7.3.0";s:5:"alias";a:1:{s:17:"@kartik/daterange";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-date-range/src";}}s:22:"kartik-v/yii2-dynagrid";a:3:{s:4:"name";s:22:"kartik-v/yii2-dynagrid";s:7:"version";s:7:"1.5.5.0";s:5:"alias";a:1:{s:16:"@kartik/dynagrid";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-dynagrid/src";}}s:16:"yiisoft/yii2-jui";a:3:{s:4:"name";s:16:"yiisoft/yii2-jui";s:7:"version";s:7:"2.0.7.0";s:5:"alias";a:1:{s:8:"@yii/jui";s:43:"C:\Web\Reclassering\vendor/yiisoft/yii2-jui";}}s:27:"kartik-v/yii2-label-inplace";a:3:{s:4:"name";s:27:"kartik-v/yii2-label-inplace";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/label";s:58:"C:\Web\Reclassering\vendor/kartik-v/yii2-label-inplace/src";}}s:19:"kartik-v/yii2-icons";a:3:{s:4:"name";s:19:"kartik-v/yii2-icons";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/icons";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-icons/src";}}s:19:"kartik-v/yii2-money";a:3:{s:4:"name";s:19:"kartik-v/yii2-money";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/money";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-money/src";}}s:20:"kartik-v/yii2-ipinfo";a:3:{s:4:"name";s:20:"kartik-v/yii2-ipinfo";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:14:"@kartik/ipinfo";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-ipinfo/src";}}s:22:"kartik-v/yii2-markdown";a:3:{s:4:"name";s:22:"kartik-v/yii2-markdown";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/markdown";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-markdown/src";}}s:24:"kartik-v/yii2-dropdown-x";a:3:{s:4:"name";s:24:"kartik-v/yii2-dropdown-x";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/dropdown";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-dropdown-x/src";}}s:19:"kartik-v/yii2-nav-x";a:3:{s:4:"name";s:19:"kartik-v/yii2-nav-x";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:11:"@kartik/nav";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-nav-x/src";}}s:22:"kartik-v/yii2-password";a:3:{s:4:"name";s:22:"kartik-v/yii2-password";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/password";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-password/src";}}s:20:"kartik-v/yii2-slider";a:3:{s:4:"name";s:20:"kartik-v/yii2-slider";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:14:"@kartik/slider";s:47:"C:\Web\Reclassering\vendor/kartik-v/yii2-slider";}}s:28:"kartik-v/yii2-sortable-input";a:3:{s:4:"name";s:28:"kartik-v/yii2-sortable-input";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:17:"@kartik/sortinput";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-sortable-input/src";}}s:20:"kartik-v/yii2-tabs-x";a:3:{s:4:"name";s:20:"kartik-v/yii2-tabs-x";s:7:"version";s:7:"1.2.9.0";s:5:"alias";a:1:{s:12:"@kartik/tabs";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-tabs-x/src";}}s:30:"kartik-v/yii2-widget-typeahead";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-typeahead";s:7:"version";s:7:"1.0.4.0";s:5:"alias";a:1:{s:17:"@kartik/typeahead";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-typeahead/src";}}s:20:"kartik-v/yii2-social";a:3:{s:4:"name";s:20:"kartik-v/yii2-social";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:14:"@kartik/social";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-social/src";}}s:30:"kartik-v/yii2-widget-touchspin";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-touchspin";s:7:"version";s:7:"1.2.4.0";s:5:"alias";a:1:{s:17:"@kartik/touchspin";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-touchspin/src";}}s:32:"kartik-v/yii2-widget-switchinput";a:3:{s:4:"name";s:32:"kartik-v/yii2-widget-switchinput";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:19:"@kartik/switchinput";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-switchinput";}}s:24:"kartik-v/yii2-validators";a:4:{s:4:"name";s:24:"kartik-v/yii2-validators";s:7:"version";s:7:"1.0.3.0";s:5:"alias";a:1:{s:18:"@kartik/validators";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-validators/src";}s:9:"bootstrap";s:27:"kartik\validators\Bootstrap";}s:28:"kartik-v/yii2-widget-spinner";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-spinner";s:7:"version";s:7:"1.0.1.0";s:5:"alias";a:1:{s:15:"@kartik/spinner";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-spinner/src";}}s:28:"kartik-v/yii2-widget-sidenav";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-sidenav";s:7:"version";s:7:"1.0.1.0";s:5:"alias";a:1:{s:15:"@kartik/sidenav";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-sidenav";}}s:27:"kartik-v/yii2-widget-rating";a:3:{s:4:"name";s:27:"kartik-v/yii2-widget-rating";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:14:"@kartik/rating";s:58:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-rating/src";}}s:31:"kartik-v/yii2-widget-rangeinput";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-rangeinput";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:13:"@kartik/range";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-rangeinput/src";}}s:26:"kartik-v/yii2-widget-growl";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-growl";s:7:"version";s:7:"1.1.2.0";s:5:"alias";a:1:{s:13:"@kartik/growl";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-growl/src";}}s:28:"kartik-v/yii2-widget-depdrop";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-depdrop";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:15:"@kartik/depdrop";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-depdrop/src";}}s:31:"kartik-v/yii2-widget-colorinput";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-colorinput";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:13:"@kartik/color";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-colorinput/src";}}s:35:"kartik-v/yii2-widget-datetimepicker";a:3:{s:4:"name";s:35:"kartik-v/yii2-widget-datetimepicker";s:7:"version";s:7:"1.5.1.0";s:5:"alias";a:1:{s:16:"@kartik/datetime";s:66:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-datetimepicker/src";}}s:31:"kartik-v/yii2-widget-datepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-datepicker";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:12:"@kartik/date";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-datepicker/src";}}s:26:"kartik-v/yii2-widget-alert";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-alert";s:7:"version";s:7:"1.1.5.0";s:5:"alias";a:1:{s:13:"@kartik/alert";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-alert/src";}}s:26:"kartik-v/yii2-widget-affix";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-affix";s:7:"version";s:7:"1.0.0.0";s:5:"alias";a:1:{s:13:"@kartik/affix";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-affix";}}s:21:"kartik-v/yii2-widgets";a:3:{s:4:"name";s:21:"kartik-v/yii2-widgets";s:7:"version";s:7:"3.4.1.0";s:5:"alias";a:1:{s:15:"@kartik/widgets";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-widgets/src";}}s:33:"kartik-v/yii2-bootstrap5-dropdown";a:3:{s:4:"name";s:33:"kartik-v/yii2-bootstrap5-dropdown";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:19:"@kartik/bs5dropdown";s:64:"C:\Web\Reclassering\vendor/kartik-v/yii2-bootstrap5-dropdown/src";}}s:26:"biladina/yii2-ajaxcrud-bs4";a:4:{s:4:"name";s:26:"biladina/yii2-ajaxcrud-bs4";s:7:"version";s:7:"3.0.0.0";s:5:"alias";a:1:{s:22:"@yii2ajaxcrud/ajaxcrud";s:57:"C:\Web\Reclassering\vendor/biladina/yii2-ajaxcrud-bs4/src";}s:9:"bootstrap";s:31:"yii2ajaxcrud\ajaxcrud\Bootstrap";}s:28:"kartik-v/yii2-widget-select2";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-select2";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@kartik/select2";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-select2/src";}}s:31:"kartik-v/yii2-widget-activeform";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-activeform";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/form";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-activeform/src";}}s:23:"raoul2000/yii2-workflow";a:3:{s:4:"name";s:23:"raoul2000/yii2-workflow";s:7:"version";s:7:"1.2.0.0";s:5:"alias";a:1:{s:19:"@raoul2000/workflow";s:54:"C:\Web\Reclassering\vendor/raoul2000/yii2-workflow/src";}}s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:8:"2.0.50.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:20:"kartik-v/yii2-export";a:3:{s:4:"name";s:20:"kartik-v/yii2-export";s:7:"version";s:7:"1.4.3.0";s:5:"alias";a:1:{s:14:"@kartik/export";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-export/src";}}s:28:"raoul2000/yii2-workflow-view";a:3:{s:4:"name";s:28:"raoul2000/yii2-workflow-view";s:7:"version";s:7:"0.0.2.0";s:5:"alias";a:1:{s:24:"@raoul2000/workflow/view";s:59:"C:\Web\Reclassering\vendor/raoul2000/yii2-workflow-view/src";}}s:32:"cornernote/yii2-workflow-manager";a:3:{s:4:"name";s:32:"cornernote/yii2-workflow-manager";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:28:"@cornernote/workflow/manager";s:63:"C:\Web\Reclassering\vendor/cornernote/yii2-workflow-manager/src";}}s:28:"2amigos/yii2-ckeditor-widget";a:3:{s:4:"name";s:28:"2amigos/yii2-ckeditor-widget";s:7:"version";s:7:"2.1.0.0";s:5:"alias";a:1:{s:19:"@dosamigos/ckeditor";s:59:"C:\Web\Reclassering\vendor/2amigos/yii2-ckeditor-widget/src";}}s:17:"yiisoft/yii2-twig";a:3:{s:4:"name";s:17:"yiisoft/yii2-twig";s:7:"version";s:7:"2.5.1.0";s:5:"alias";a:1:{s:9:"@yii/twig";s:48:"C:\Web\Reclassering\vendor/yiisoft/yii2-twig/src";}}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.27.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:49:"C:\Web\Reclassering\vendor/yiisoft/yii2-debug/src";}}s:27:"2amigos/yii2-chartjs-widget";a:3:{s:4:"name";s:27:"2amigos/yii2-chartjs-widget";s:7:"version";s:7:"2.1.3.0";s:5:"alias";a:1:{s:18:"@dosamigos/chartjs";s:58:"C:\Web\Reclassering\vendor/2amigos/yii2-chartjs-widget/src";}}s:19:"bedezign/yii2-audit";a:4:{s:4:"name";s:19:"bedezign/yii2-audit";s:7:"version";s:7:"1.2.7.0";s:5:"alias";a:1:{s:20:"@bedezign/yii2/audit";s:50:"C:\Web\Reclassering\vendor/bedezign/yii2-audit/src";}s:9:"bootstrap";s:29:"bedezign\yii2\audit\Bootstrap";}s:30:"kartik-v/yii2-widget-fileinput";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-fileinput";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/file";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-fileinput/src";}}s:24:"rmrevin/yii2-fontawesome";a:3:{s:4:"name";s:24:"rmrevin/yii2-fontawesome";s:7:"version";s:8:"2.10.3.0";s:5:"alias";a:1:{s:24:"@rmrevin/yii/fontawesome";s:51:"C:\Web\Reclassering\vendor/rmrevin/yii2-fontawesome";}}s:24:"edofre/yii2-fullcalendar";a:3:{s:4:"name";s:24:"edofre/yii2-fullcalendar";s:7:"version";s:8:"1.0.11.0";s:5:"alias";a:1:{s:20:"@edofre/fullcalendar";s:55:"C:\Web\Reclassering\vendor/edofre/yii2-fullcalendar/src";}}s:31:"kartik-v/yii2-widget-timepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-timepicker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/time";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-timepicker/src";}}s:34:"miloschuman/yii2-highcharts-widget";a:3:{s:4:"name";s:34:"miloschuman/yii2-highcharts-widget";s:7:"version";s:8:"11.0.0.0";s:5:"alias";a:1:{s:23:"@miloschuman/highcharts";s:65:"C:\Web\Reclassering\vendor/miloschuman/yii2-highcharts-widget/src";}}}}";s:3:"log";s:41802:"a:1:{s:8:"messages";a:69:{i:0;a:6:{i:0;s:55:"Bootstrap with kartik\validators\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.133627;i:4;a:0:{}i:5;i:2898672;}i:1;a:6:{i:0;s:59:"Bootstrap with yii2ajaxcrud\ajaxcrud\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.135089;i:4;a:0:{}i:5;i:3003896;}i:2;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.135097;i:4;a:0:{}i:5;i:3004192;}i:3;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.135417;i:4;a:0:{}i:5;i:3034192;}i:4;a:6:{i:0;s:57:"Bootstrap with bedezign\yii2\audit\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.135689;i:4;a:0:{}i:5;i:3061664;}i:5;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.13673;i:4;a:0:{}i:5;i:3216696;}i:6;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.13674;i:4;a:0:{}i:5;i:3217336;}i:7;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:**********.141657;i:4;a:0:{}i:5;i:4214952;}i:8;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.151535;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5539048;}i:9;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.151644;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5541328;}i:14;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.165805;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5599144;}i:17;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.167079;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5619864;}i:20;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.18061;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6089680;}i:23;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.199485;i:4;a:0:{}i:5;i:6782200;}i:24;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.202706;i:4;a:0:{}i:5;i:6962296;}i:25;a:6:{i:0;s:21:"Loading module: audit";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.202749;i:4;a:0:{}i:5;i:6962936;}i:26;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.205202;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7032856;}i:29;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.208688;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7044488;}i:32;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.20981;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7044272;}i:35;a:6:{i:0;s:40:"Bootstrap with bedezign\yii2\audit\Audit";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.222923;i:4;a:0:{}i:5;i:7224704;}i:37;a:6:{i:0;s:31:"Route requested: 'document/pdf'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:**********.223263;i:4;a:0:{}i:5;i:7223632;}i:38;a:6:{i:0;s:26:"Route to run: document/pdf";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:**********.228061;i:4;a:0:{}i:5;i:7435400;}i:39;a:6:{i:0;s:158:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('document/pdf', '1', '::1', 0, 'GET', '2025-08-29 13:20:08')";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.238444;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7705792;}i:42;a:6:{i:0;s:67:"Running action: backend\controllers\DocumentController::actionPdf()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:**********.243839;i:4;a:0:{}i:5;i:7717224;}i:43;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `document`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.244228;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:396;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7764672;}i:46;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.247105;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:396;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7779472;}i:49;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document' AND `kcu`.`TABLE_NAME` = 'document'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.248523;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:396;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7783136;}i:52;a:6:{i:0;s:40:"SELECT * FROM `document` WHERE `id`='18'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.250297;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:396;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7787984;}i:55;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `sw_status`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.256253;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8443672;}i:58;a:6:{i:0;s:29:"SHOW CREATE TABLE `sw_status`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.258396;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8453016;}i:61;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_status' AND `kcu`.`TABLE_NAME` = 'sw_status'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.259467;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8457336;}i:64;a:6:{i:0;s:91:"SELECT * FROM `sw_status` WHERE (`workflow_id`='document-workflow') AND (`id`='medewerker')";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.261146;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8461208;}i:67;a:6:{i:0;s:100:"SELECT * FROM `sw_metadata` WHERE (`workflow_id`='document-workflow') AND (`status_id`='medewerker')";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.262651;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8534672;}i:70;a:6:{i:0;s:57:"SELECT * FROM `document_signature` WHERE `document_id`=18";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.267591;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:399;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8581928;}i:73;a:6:{i:0;s:40:"SELECT * FROM `document` WHERE `id`='18'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.268637;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8585768;}i:76;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.270774;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8726008;}i:79;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `document_type`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.271842;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8741544;}i:82;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.274142;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8754368;}i:85;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_type' AND `kcu`.`TABLE_NAME` = 'document_type'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.275174;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8756808;}i:88;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.277642;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8763376;}i:91;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.278616;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8775064;}i:94;a:6:{i:0;s:56:"SELECT * FROM `document_antwoord` WHERE `document_id`=18";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.279672;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8786208;}i:97;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `document_antwoord`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.280647;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8804848;}i:100;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_antwoord`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.2829;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8817688;}i:103;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_antwoord' AND `kcu`.`TABLE_NAME` = 'document_antwoord'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.283854;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8820112;}i:106;a:6:{i:0;s:75:"SELECT * FROM `vraag` WHERE `id` IN (18, 19, 20, 21, 22, 23, 24, 25, 26, 5)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.286761;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8917584;}i:109;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `vraag`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.287922;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8933840;}i:112;a:6:{i:0;s:25:"SHOW CREATE TABLE `vraag`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.289828;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8944880;}i:115;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'vraag' AND `kcu`.`TABLE_NAME` = 'vraag'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.290645;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8948296;}i:118;a:6:{i:0;s:56:"SELECT * FROM `document_feedback` WHERE `document_id`=18";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.292532;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9012976;}i:121;a:6:{i:0;s:57:"SELECT * FROM `document_signature` WHERE `document_id`=18";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.293295;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9019024;}i:124;a:6:{i:0;s:80:"SELECT * FROM `documenttype_vraag` WHERE `document_id`=8 ORDER BY `vraag_nummer`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.294275;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:129;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9034280;}i:127;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `documenttype_vraag`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.29508;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:129;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9052760;}i:130;a:6:{i:0;s:38:"SHOW CREATE TABLE `documenttype_vraag`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.296649;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:129;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9071104;}i:133;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'documenttype_vraag' AND `kcu`.`TABLE_NAME` = 'documenttype_vraag'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.297122;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:129;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9073640;}i:136;a:6:{i:0;s:75:"SELECT * FROM `vraag` WHERE `id` IN (18, 19, 20, 21, 22, 23, 24, 25, 26, 5)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.298502;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:129;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9144880;}i:139;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.29916;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9222208;}i:142;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.299597;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9235160;}i:145;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.300369;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9247896;}i:148;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.301075;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9266264;}i:151;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.301528;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9279000;}i:154;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.301955;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9291736;}i:157;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.3024;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9304472;}i:160;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.302833;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9317208;}i:163;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.303586;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9329944;}i:166;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.304324;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9342840;}i:169;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.32788;i:4;a:2:{i:0;a:5:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:212;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9427816;}i:172;a:6:{i:0;s:65:"SELECT * FROM `notification_trigger` WHERE `route`='document/pdf'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756470013.250207;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:26032184;}i:175;a:6:{i:0;s:90:"UPDATE `audit_entry` SET `duration`=5.1295499801636, `memory_max`=29060000 WHERE `id`=3777";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1756470013.252227;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:26035016;}}}";s:9:"profiling";s:78952:"a:3:{s:6:"memory";i:29060000;s:4:"time";d:5.142040967941284;s:8:"messages";a:108:{i:10;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.151679;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5542136;}i:11;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.162741;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5585440;}i:12;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.162769;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5585224;}i:13;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.165433;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5597856;}i:15;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.165822;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5600056;}i:16;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.166546;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5602632;}i:18;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.167096;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5620904;}i:19;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.168724;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5623432;}i:21;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.180681;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6090064;}i:22;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.181781;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6092432;}i:27;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.205263;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7033768;}i:28;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.208571;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7043200;}i:30;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.208734;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7045400;}i:31;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.209583;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7047312;}i:33;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.209837;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7045952;}i:34;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.212019;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7047808;}i:40;a:6:{i:0;s:158:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('document/pdf', '1', '::1', 0, 'GET', '2025-08-29 13:20:08')";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.238513;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7707152;}i:41;a:6:{i:0;s:158:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('document/pdf', '1', '::1', 0, 'GET', '2025-08-29 13:20:08')";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.243219;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7708944;}i:44;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `document`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.244247;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:396;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7765960;}i:45;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `document`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.246838;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:396;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7777808;}i:47;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.24716;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:396;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7780760;}i:48;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.248136;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:396;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7783944;}i:50;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document' AND `kcu`.`TABLE_NAME` = 'document'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.248538;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:396;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7784552;}i:51;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document' AND `kcu`.`TABLE_NAME` = 'document'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.250223;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:396;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7789080;}i:53;a:6:{i:0;s:40:"SELECT * FROM `document` WHERE `id`='18'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.250306;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:396;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7789488;}i:54;a:6:{i:0;s:40:"SELECT * FROM `document` WHERE `id`='18'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.250721;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:396;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7792080;}i:56;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `sw_status`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.256309;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8445336;}i:57;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `sw_status`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.258352;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8450976;}i:59;a:6:{i:0;s:29:"SHOW CREATE TABLE `sw_status`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.258411;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8454680;}i:60;a:6:{i:0;s:29:"SHOW CREATE TABLE `sw_status`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.25935;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8457088;}i:62;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_status' AND `kcu`.`TABLE_NAME` = 'sw_status'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.259482;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8459128;}i:63;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_status' AND `kcu`.`TABLE_NAME` = 'sw_status'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.261025;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8461736;}i:65;a:6:{i:0;s:91:"SELECT * FROM `sw_status` WHERE (`workflow_id`='document-workflow') AND (`id`='medewerker')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.26116;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8464264;}i:66;a:6:{i:0;s:91:"SELECT * FROM `sw_status` WHERE (`workflow_id`='document-workflow') AND (`id`='medewerker')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.261641;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8466552;}i:68;a:6:{i:0;s:100:"SELECT * FROM `sw_metadata` WHERE (`workflow_id`='document-workflow') AND (`status_id`='medewerker')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.262667;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8536448;}i:69;a:6:{i:0;s:100:"SELECT * FROM `sw_metadata` WHERE (`workflow_id`='document-workflow') AND (`status_id`='medewerker')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.263449;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8538320;}i:71;a:6:{i:0;s:57:"SELECT * FROM `document_signature` WHERE `document_id`=18";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.26765;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:399;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8583056;}i:72;a:6:{i:0;s:57:"SELECT * FROM `document_signature` WHERE `document_id`=18";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.268518;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:399;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8584312;}i:74;a:6:{i:0;s:40:"SELECT * FROM `document` WHERE `id`='18'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.26865;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8587648;}i:75;a:6:{i:0;s:40:"SELECT * FROM `document` WHERE `id`='18'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.269352;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8590536;}i:77;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.270791;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8727888;}i:78;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.271556;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8732336;}i:80;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `document_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.271891;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8743208;}i:81;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `document_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.274081;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8752320;}i:83;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.274163;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8756032;}i:84;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.275003;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8758952;}i:86;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_type' AND `kcu`.`TABLE_NAME` = 'document_type'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.275195;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8758600;}i:87;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_type' AND `kcu`.`TABLE_NAME` = 'document_type'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.277426;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8762440;}i:89;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.277662;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8765256;}i:90;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.278424;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8768144;}i:92;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.278634;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8776944;}i:93;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.279429;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8779832;}i:95;a:6:{i:0;s:56:"SELECT * FROM `document_antwoord` WHERE `document_id`=18";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.279694;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8788088;}i:96;a:6:{i:0;s:56:"SELECT * FROM `document_antwoord` WHERE `document_id`=18";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.280532;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8795496;}i:98;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `document_antwoord`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.280666;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8806512;}i:99;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `document_antwoord`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.282846;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8815640;}i:101;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_antwoord`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.282917;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8819352;}i:102;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_antwoord`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.28372;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8822408;}i:104;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_antwoord' AND `kcu`.`TABLE_NAME` = 'document_antwoord'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.283869;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8821904;}i:105;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_antwoord' AND `kcu`.`TABLE_NAME` = 'document_antwoord'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.285915;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8826280;}i:107;a:6:{i:0;s:75:"SELECT * FROM `vraag` WHERE `id` IN (18, 19, 20, 21, 22, 23, 24, 25, 26, 5)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.286781;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8918208;}i:108;a:6:{i:0;s:75:"SELECT * FROM `vraag` WHERE `id` IN (18, 19, 20, 21, 22, 23, 24, 25, 26, 5)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.287633;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8925552;}i:110;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.287972;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8934864;}i:111;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.28979;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8942200;}i:113;a:6:{i:0;s:25:"SHOW CREATE TABLE `vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.289842;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8946544;}i:114;a:6:{i:0;s:25:"SHOW CREATE TABLE `vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.290558;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8949328;}i:116;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'vraag' AND `kcu`.`TABLE_NAME` = 'vraag'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.290657;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8949448;}i:117;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'vraag' AND `kcu`.`TABLE_NAME` = 'vraag'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.292218;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8953272;}i:119;a:6:{i:0;s:56:"SELECT * FROM `document_feedback` WHERE `document_id`=18";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.292542;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9014856;}i:120;a:6:{i:0;s:56:"SELECT * FROM `document_feedback` WHERE `document_id`=18";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.293235;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9016760;}i:122;a:6:{i:0;s:57:"SELECT * FROM `document_signature` WHERE `document_id`=18";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.293304;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9020904;}i:123;a:6:{i:0;s:57:"SELECT * FROM `document_signature` WHERE `document_id`=18";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.293849;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9022872;}i:125;a:6:{i:0;s:80:"SELECT * FROM `documenttype_vraag` WHERE `document_id`=8 ORDER BY `vraag_nummer`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.294285;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:129;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9036160;}i:126;a:6:{i:0;s:80:"SELECT * FROM `documenttype_vraag` WHERE `document_id`=8 ORDER BY `vraag_nummer`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.294854;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:129;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9043120;}i:128;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `documenttype_vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.295128;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:129;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9054424;}i:129;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `documenttype_vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.296617;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:129;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9069056;}i:131;a:6:{i:0;s:38:"SHOW CREATE TABLE `documenttype_vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.296659;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:129;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9072768;}i:132;a:6:{i:0;s:38:"SHOW CREATE TABLE `documenttype_vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.297043;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:129;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9075696;}i:134;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'documenttype_vraag' AND `kcu`.`TABLE_NAME` = 'documenttype_vraag'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.297131;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:129;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9075432;}i:135;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'documenttype_vraag' AND `kcu`.`TABLE_NAME` = 'documenttype_vraag'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.298241;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:129;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9079272;}i:137;a:6:{i:0;s:75:"SELECT * FROM `vraag` WHERE `id` IN (18, 19, 20, 21, 22, 23, 24, 25, 26, 5)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.298515;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:129;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9145504;}i:138;a:6:{i:0;s:75:"SELECT * FROM `vraag` WHERE `id` IN (18, 19, 20, 21, 22, 23, 24, 25, 26, 5)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.298841;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:129;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9152848;}i:140;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.29917;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9224088;}i:141;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.299477;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9226976;}i:143;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.299605;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9237040;}i:144;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.299883;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9239928;}i:146;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.300412;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9249776;}i:147;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.300961;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9252664;}i:149;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.301085;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9268144;}i:150;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.301417;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9271032;}i:152;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.301538;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9280880;}i:153;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.301843;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9283768;}i:155;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.301966;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9293616;}i:156;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.302278;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9296504;}i:158;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.302409;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9306352;}i:159;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.302715;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9309240;}i:161;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.302842;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9319088;}i:162;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.303113;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9321976;}i:164;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.303631;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9331824;}i:165;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.304218;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9334712;}i:167;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.304333;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9344720;}i:168;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.30468;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9347608;}i:170;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.327951;i:4;a:2:{i:0;a:5:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:212;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9429000;}i:171;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.329124;i:4;a:2:{i:0;a:5:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:212;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9433024;}i:173;a:6:{i:0;s:65:"SELECT * FROM `notification_trigger` WHERE `route`='document/pdf'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756470013.250279;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:26033312;}i:174;a:6:{i:0;s:65:"SELECT * FROM `notification_trigger` WHERE `route`='document/pdf'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756470013.251931;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:26034448;}i:176;a:6:{i:0;s:90:"UPDATE `audit_entry` SET `duration`=5.1295499801636, `memory_max`=29060000 WHERE `id`=3777";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1756470013.25225;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:26036352;}i:177;a:6:{i:0;s:90:"UPDATE `audit_entry` SET `duration`=5.1295499801636, `memory_max`=29060000 WHERE `id`=3777";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1756470013.256711;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:26037752;}}}";s:2:"db";s:78183:"a:1:{s:8:"messages";a:106:{i:12;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.162769;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5585224;}i:13;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.165433;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5597856;}i:15;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.165822;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5600056;}i:16;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.166546;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5602632;}i:18;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.167096;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5620904;}i:19;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.168724;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5623432;}i:21;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.180681;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6090064;}i:22;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.181781;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6092432;}i:27;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.205263;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7033768;}i:28;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.208571;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7043200;}i:30;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.208734;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7045400;}i:31;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.209583;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7047312;}i:33;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.209837;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7045952;}i:34;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.212019;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7047808;}i:40;a:6:{i:0;s:158:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('document/pdf', '1', '::1', 0, 'GET', '2025-08-29 13:20:08')";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.238513;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7707152;}i:41;a:6:{i:0;s:158:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('document/pdf', '1', '::1', 0, 'GET', '2025-08-29 13:20:08')";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.243219;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7708944;}i:44;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `document`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.244247;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:396;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7765960;}i:45;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `document`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.246838;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:396;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7777808;}i:47;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.24716;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:396;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7780760;}i:48;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.248136;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:396;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7783944;}i:50;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document' AND `kcu`.`TABLE_NAME` = 'document'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.248538;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:396;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7784552;}i:51;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document' AND `kcu`.`TABLE_NAME` = 'document'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.250223;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:396;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7789080;}i:53;a:6:{i:0;s:40:"SELECT * FROM `document` WHERE `id`='18'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.250306;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:396;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7789488;}i:54;a:6:{i:0;s:40:"SELECT * FROM `document` WHERE `id`='18'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.250721;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:396;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7792080;}i:56;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `sw_status`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.256309;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8445336;}i:57;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `sw_status`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.258352;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8450976;}i:59;a:6:{i:0;s:29:"SHOW CREATE TABLE `sw_status`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.258411;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8454680;}i:60;a:6:{i:0;s:29:"SHOW CREATE TABLE `sw_status`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.25935;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8457088;}i:62;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_status' AND `kcu`.`TABLE_NAME` = 'sw_status'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.259482;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8459128;}i:63;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_status' AND `kcu`.`TABLE_NAME` = 'sw_status'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.261025;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8461736;}i:65;a:6:{i:0;s:91:"SELECT * FROM `sw_status` WHERE (`workflow_id`='document-workflow') AND (`id`='medewerker')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.26116;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8464264;}i:66;a:6:{i:0;s:91:"SELECT * FROM `sw_status` WHERE (`workflow_id`='document-workflow') AND (`id`='medewerker')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.261641;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8466552;}i:68;a:6:{i:0;s:100:"SELECT * FROM `sw_metadata` WHERE (`workflow_id`='document-workflow') AND (`status_id`='medewerker')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.262667;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8536448;}i:69;a:6:{i:0;s:100:"SELECT * FROM `sw_metadata` WHERE (`workflow_id`='document-workflow') AND (`status_id`='medewerker')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.263449;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8538320;}i:71;a:6:{i:0;s:57:"SELECT * FROM `document_signature` WHERE `document_id`=18";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.26765;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:399;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8583056;}i:72;a:6:{i:0;s:57:"SELECT * FROM `document_signature` WHERE `document_id`=18";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.268518;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:399;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8584312;}i:74;a:6:{i:0;s:40:"SELECT * FROM `document` WHERE `id`='18'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.26865;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8587648;}i:75;a:6:{i:0;s:40:"SELECT * FROM `document` WHERE `id`='18'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.269352;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8590536;}i:77;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.270791;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8727888;}i:78;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.271556;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8732336;}i:80;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `document_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.271891;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8743208;}i:81;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `document_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.274081;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8752320;}i:83;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.274163;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8756032;}i:84;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.275003;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8758952;}i:86;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_type' AND `kcu`.`TABLE_NAME` = 'document_type'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.275195;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8758600;}i:87;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_type' AND `kcu`.`TABLE_NAME` = 'document_type'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.277426;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8762440;}i:89;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.277662;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8765256;}i:90;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.278424;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8768144;}i:92;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.278634;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8776944;}i:93;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.279429;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8779832;}i:95;a:6:{i:0;s:56:"SELECT * FROM `document_antwoord` WHERE `document_id`=18";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.279694;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8788088;}i:96;a:6:{i:0;s:56:"SELECT * FROM `document_antwoord` WHERE `document_id`=18";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.280532;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8795496;}i:98;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `document_antwoord`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.280666;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8806512;}i:99;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `document_antwoord`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.282846;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8815640;}i:101;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_antwoord`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.282917;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8819352;}i:102;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_antwoord`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.28372;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8822408;}i:104;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_antwoord' AND `kcu`.`TABLE_NAME` = 'document_antwoord'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.283869;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8821904;}i:105;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_antwoord' AND `kcu`.`TABLE_NAME` = 'document_antwoord'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.285915;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8826280;}i:107;a:6:{i:0;s:75:"SELECT * FROM `vraag` WHERE `id` IN (18, 19, 20, 21, 22, 23, 24, 25, 26, 5)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.286781;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8918208;}i:108;a:6:{i:0;s:75:"SELECT * FROM `vraag` WHERE `id` IN (18, 19, 20, 21, 22, 23, 24, 25, 26, 5)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.287633;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8925552;}i:110;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.287972;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8934864;}i:111;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.28979;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8942200;}i:113;a:6:{i:0;s:25:"SHOW CREATE TABLE `vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.289842;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8946544;}i:114;a:6:{i:0;s:25:"SHOW CREATE TABLE `vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.290558;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8949328;}i:116;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'vraag' AND `kcu`.`TABLE_NAME` = 'vraag'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.290657;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8949448;}i:117;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'vraag' AND `kcu`.`TABLE_NAME` = 'vraag'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.292218;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:8953272;}i:119;a:6:{i:0;s:56:"SELECT * FROM `document_feedback` WHERE `document_id`=18";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.292542;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9014856;}i:120;a:6:{i:0;s:56:"SELECT * FROM `document_feedback` WHERE `document_id`=18";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.293235;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9016760;}i:122;a:6:{i:0;s:57:"SELECT * FROM `document_signature` WHERE `document_id`=18";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.293304;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9020904;}i:123;a:6:{i:0;s:57:"SELECT * FROM `document_signature` WHERE `document_id`=18";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.293849;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:114;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9022872;}i:125;a:6:{i:0;s:80:"SELECT * FROM `documenttype_vraag` WHERE `document_id`=8 ORDER BY `vraag_nummer`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.294285;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:129;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9036160;}i:126;a:6:{i:0;s:80:"SELECT * FROM `documenttype_vraag` WHERE `document_id`=8 ORDER BY `vraag_nummer`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.294854;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:129;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9043120;}i:128;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `documenttype_vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.295128;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:129;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9054424;}i:129;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `documenttype_vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.296617;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:129;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9069056;}i:131;a:6:{i:0;s:38:"SHOW CREATE TABLE `documenttype_vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.296659;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:129;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9072768;}i:132;a:6:{i:0;s:38:"SHOW CREATE TABLE `documenttype_vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.297043;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:129;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9075696;}i:134;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'documenttype_vraag' AND `kcu`.`TABLE_NAME` = 'documenttype_vraag'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.297131;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:129;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9075432;}i:135;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'documenttype_vraag' AND `kcu`.`TABLE_NAME` = 'documenttype_vraag'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.298241;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:129;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9079272;}i:137;a:6:{i:0;s:75:"SELECT * FROM `vraag` WHERE `id` IN (18, 19, 20, 21, 22, 23, 24, 25, 26, 5)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.298515;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:129;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9145504;}i:138;a:6:{i:0;s:75:"SELECT * FROM `vraag` WHERE `id` IN (18, 19, 20, 21, 22, 23, 24, 25, 26, 5)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.298841;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:129;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9152848;}i:140;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.29917;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9224088;}i:141;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.299477;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9226976;}i:143;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.299605;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9237040;}i:144;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.299883;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9239928;}i:146;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.300412;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9249776;}i:147;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.300961;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9252664;}i:149;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.301085;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9268144;}i:150;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.301417;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9271032;}i:152;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.301538;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9280880;}i:153;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.301843;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9283768;}i:155;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.301966;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9293616;}i:156;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.302278;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9296504;}i:158;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.302409;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9306352;}i:159;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.302715;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9309240;}i:161;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.302842;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9319088;}i:162;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.303113;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9321976;}i:164;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.303631;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9331824;}i:165;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.304218;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9334712;}i:167;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.304333;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9344720;}i:168;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.30468;i:4;a:3:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:155;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:191;s:8:"function";s:11:"getDocument";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9347608;}i:170;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.327951;i:4;a:2:{i:0;a:5:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:212;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9429000;}i:171;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.329124;i:4;a:2:{i:0;a:5:{s:4:"file";s:46:"C:\Web\Reclassering\common\models\Document.php";s:4:"line";i:212;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:408;s:8:"function";s:16:"renderWithLayout";s:5:"class";s:22:"common\models\Document";s:4:"type";s:2:"->";}}i:5;i:9433024;}i:173;a:6:{i:0;s:65:"SELECT * FROM `notification_trigger` WHERE `route`='document/pdf'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756470013.250279;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:26033312;}i:174;a:6:{i:0;s:65:"SELECT * FROM `notification_trigger` WHERE `route`='document/pdf'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756470013.251931;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:26034448;}i:176;a:6:{i:0;s:90:"UPDATE `audit_entry` SET `duration`=5.1295499801636, `memory_max`=29060000 WHERE `id`=3777";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1756470013.25225;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:26036352;}i:177;a:6:{i:0;s:90:"UPDATE `audit_entry` SET `duration`=5.1295499801636, `memory_max`=29060000 WHERE `id`=3777";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1756470013.256711;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:26037752;}}}";s:5:"event";s:28006:"a:161:{i:0;a:5:{s:4:"time";d:**********.146328;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:1;a:5:{s:4:"time";d:**********.162732;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:2;a:5:{s:4:"time";d:**********.18263;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:3;a:5:{s:4:"time";d:**********.182679;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:4;a:5:{s:4:"time";d:**********.193359;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:5;a:5:{s:4:"time";d:**********.223156;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:6;a:5:{s:4:"time";d:**********.232694;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:7;a:5:{s:4:"time";d:**********.232737;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:8;a:5:{s:4:"time";d:**********.237017;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:9;a:5:{s:4:"time";d:**********.243528;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:10;a:5:{s:4:"time";d:**********.243541;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:11;a:5:{s:4:"time";d:**********.243816;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:38:"backend\controllers\DocumentController";}i:12;a:5:{s:4:"time";d:**********.244195;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:13;a:5:{s:4:"time";d:**********.254922;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:14;a:5:{s:4:"time";d:**********.256132;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:15;a:5:{s:4:"time";d:**********.261658;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"cornernote\workflow\manager\models\Status";}i:16;a:5:{s:4:"time";d:**********.26168;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"cornernote\workflow\manager\models\Status";}i:17;a:5:{s:4:"time";d:**********.261948;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:18;a:5:{s:4:"time";d:**********.265932;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:19;a:5:{s:4:"time";d:**********.26728;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:20;a:5:{s:4:"time";d:**********.26857;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:21;a:5:{s:4:"time";d:**********.269438;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:22;a:5:{s:4:"time";d:**********.269868;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:23;a:5:{s:4:"time";d:**********.270159;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:24;a:5:{s:4:"time";d:**********.270177;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:25;a:5:{s:4:"time";d:**********.270184;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:26;a:5:{s:4:"time";d:**********.270426;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:27;a:5:{s:4:"time";d:**********.270689;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:28;a:5:{s:4:"time";d:**********.270702;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:29;a:5:{s:4:"time";d:**********.271743;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:30;a:5:{s:4:"time";d:**********.277515;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:31;a:5:{s:4:"time";d:**********.278483;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:32;a:5:{s:4:"time";d:**********.278525;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:33;a:5:{s:4:"time";d:**********.279509;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:34;a:5:{s:4:"time";d:**********.279561;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:35;a:5:{s:4:"time";d:**********.280609;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:36;a:5:{s:4:"time";d:**********.286001;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:37;a:5:{s:4:"time";d:**********.286028;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:38;a:5:{s:4:"time";d:**********.286053;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:39;a:5:{s:4:"time";d:**********.286071;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:40;a:5:{s:4:"time";d:**********.286088;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:41;a:5:{s:4:"time";d:**********.286106;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:42;a:5:{s:4:"time";d:**********.286122;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:43;a:5:{s:4:"time";d:**********.286138;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:44;a:5:{s:4:"time";d:**********.286154;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:45;a:5:{s:4:"time";d:**********.286657;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:46;a:5:{s:4:"time";d:**********.287827;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:47;a:5:{s:4:"time";d:**********.292278;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:48;a:5:{s:4:"time";d:**********.292299;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:49;a:5:{s:4:"time";d:**********.292313;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:50;a:5:{s:4:"time";d:**********.292325;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:51;a:5:{s:4:"time";d:**********.292337;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:52;a:5:{s:4:"time";d:**********.292348;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:53;a:5:{s:4:"time";d:**********.292359;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:54;a:5:{s:4:"time";d:**********.29237;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:55;a:5:{s:4:"time";d:**********.292381;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:56;a:5:{s:4:"time";d:**********.292389;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:57;a:5:{s:4:"time";d:**********.292391;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:58;a:5:{s:4:"time";d:**********.292394;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:59;a:5:{s:4:"time";d:**********.292396;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:60;a:5:{s:4:"time";d:**********.292398;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:61;a:5:{s:4:"time";d:**********.292401;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:62;a:5:{s:4:"time";d:**********.292403;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:63;a:5:{s:4:"time";d:**********.292405;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:64;a:5:{s:4:"time";d:**********.292408;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:65;a:5:{s:4:"time";d:**********.29241;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:66;a:5:{s:4:"time";d:**********.292444;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:67;a:5:{s:4:"time";d:**********.292446;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:68;a:5:{s:4:"time";d:**********.292449;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:69;a:5:{s:4:"time";d:**********.292451;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:70;a:5:{s:4:"time";d:**********.292454;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:71;a:5:{s:4:"time";d:**********.292457;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:72;a:5:{s:4:"time";d:**********.29246;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:73;a:5:{s:4:"time";d:**********.292462;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:74;a:5:{s:4:"time";d:**********.292464;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:75;a:5:{s:4:"time";d:**********.292467;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:76;a:5:{s:4:"time";d:**********.293894;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:77;a:5:{s:4:"time";d:**********.29423;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:78;a:5:{s:4:"time";d:**********.294986;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:79;a:5:{s:4:"time";d:**********.298312;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:80;a:5:{s:4:"time";d:**********.298331;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:81;a:5:{s:4:"time";d:**********.298344;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:82;a:5:{s:4:"time";d:**********.298356;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:83;a:5:{s:4:"time";d:**********.298367;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:84;a:5:{s:4:"time";d:**********.298377;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:85;a:5:{s:4:"time";d:**********.298389;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:86;a:5:{s:4:"time";d:**********.298398;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:87;a:5:{s:4:"time";d:**********.298409;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:88;a:5:{s:4:"time";d:**********.298427;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:89;a:5:{s:4:"time";d:**********.298899;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:90;a:5:{s:4:"time";d:**********.298926;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:91;a:5:{s:4:"time";d:**********.298941;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:92;a:5:{s:4:"time";d:**********.298953;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:93;a:5:{s:4:"time";d:**********.298963;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:94;a:5:{s:4:"time";d:**********.298973;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:95;a:5:{s:4:"time";d:**********.298983;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:96;a:5:{s:4:"time";d:**********.298994;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:97;a:5:{s:4:"time";d:**********.299003;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:98;a:5:{s:4:"time";d:**********.299013;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:99;a:5:{s:4:"time";d:**********.29902;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:100;a:5:{s:4:"time";d:**********.299022;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:101;a:5:{s:4:"time";d:**********.299024;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:102;a:5:{s:4:"time";d:**********.299027;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:103;a:5:{s:4:"time";d:**********.299029;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:104;a:5:{s:4:"time";d:**********.299031;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:105;a:5:{s:4:"time";d:**********.299033;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:106;a:5:{s:4:"time";d:**********.299035;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:107;a:5:{s:4:"time";d:**********.299037;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:108;a:5:{s:4:"time";d:**********.299039;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:109;a:5:{s:4:"time";d:**********.299064;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:110;a:5:{s:4:"time";d:**********.299066;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:111;a:5:{s:4:"time";d:**********.299068;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:112;a:5:{s:4:"time";d:**********.299071;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:113;a:5:{s:4:"time";d:**********.299073;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:114;a:5:{s:4:"time";d:**********.299075;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:115;a:5:{s:4:"time";d:**********.299077;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:116;a:5:{s:4:"time";d:**********.299079;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:117;a:5:{s:4:"time";d:**********.299081;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:118;a:5:{s:4:"time";d:**********.299083;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:119;a:5:{s:4:"time";d:**********.29911;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:120;a:5:{s:4:"time";d:**********.299506;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:121;a:5:{s:4:"time";d:**********.299528;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:122;a:5:{s:4:"time";d:**********.299549;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:123;a:5:{s:4:"time";d:**********.299998;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:124;a:5:{s:4:"time";d:**********.300091;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:125;a:5:{s:4:"time";d:**********.300182;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:126;a:5:{s:4:"time";d:**********.30099;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:127;a:5:{s:4:"time";d:**********.301011;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:128;a:5:{s:4:"time";d:**********.30103;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:129;a:5:{s:4:"time";d:**********.301444;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:130;a:5:{s:4:"time";d:**********.301465;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:131;a:5:{s:4:"time";d:**********.301484;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:132;a:5:{s:4:"time";d:**********.301871;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:133;a:5:{s:4:"time";d:**********.301891;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:134;a:5:{s:4:"time";d:**********.30191;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:135;a:5:{s:4:"time";d:**********.302311;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:136;a:5:{s:4:"time";d:**********.302333;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:137;a:5:{s:4:"time";d:**********.302354;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:138;a:5:{s:4:"time";d:**********.302744;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:139;a:5:{s:4:"time";d:**********.302766;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:140;a:5:{s:4:"time";d:**********.302788;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:141;a:5:{s:4:"time";d:**********.303177;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:142;a:5:{s:4:"time";d:**********.303278;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:143;a:5:{s:4:"time";d:**********.303378;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:144;a:5:{s:4:"time";d:**********.304244;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:145;a:5:{s:4:"time";d:**********.304263;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:146;a:5:{s:4:"time";d:**********.304282;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:147;a:5:{s:4:"time";d:**********.304721;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:148;a:5:{s:4:"time";d:**********.30474;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:149;a:5:{s:4:"time";d:**********.327606;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:150;a:5:{s:4:"time";d:**********.329255;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:151;a:5:{s:4:"time";d:**********.329317;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:152;a:5:{s:4:"time";d:1756470013.248598;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:38:"backend\controllers\DocumentController";}i:153;a:5:{s:4:"time";d:1756470013.249912;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:154;a:5:{s:4:"time";d:1756470013.251988;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:155;a:5:{s:4:"time";d:1756470013.252084;s:4:"name";s:12:"beforeUpdate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:156;a:5:{s:4:"time";d:1756470013.256752;s:4:"name";s:11:"afterUpdate";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:157;a:5:{s:4:"time";d:1756470013.25676;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:158;a:5:{s:4:"time";d:1756470013.256768;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:159;a:5:{s:4:"time";d:1756470013.257216;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:160;a:5:{s:4:"time";d:1756470013.257735;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:91:"a:3:{s:5:"start";d:**********.12248;s:3:"end";d:1756470013.266566;s:6:"memory";i:29060000;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:310:"a:3:{s:8:"messages";a:1:{i:36;a:6:{i:0;s:56:"Pretty URL not enabled. Using default URL parsing logic.";i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.223201;i:4;a:0:{}i:5;i:7223736;}}s:5:"route";s:12:"document/pdf";s:6:"action";s:51:"backend\controllers\DocumentController::actionPdf()";}";s:7:"request";s:11110:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:18:{s:12:"content-type";s:0:"";s:14:"content-length";s:1:"0";s:14:"sec-fetch-dest";s:6:"iframe";s:14:"sec-fetch-user";s:2:"?1";s:14:"sec-fetch-mode";s:8:"navigate";s:14:"sec-fetch-site";s:11:"same-origin";s:25:"upgrade-insecure-requests";s:1:"1";s:18:"sec-ch-ua-platform";s:9:""Windows"";s:16:"sec-ch-ua-mobile";s:2:"?0";s:9:"sec-ch-ua";s:65:""Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"";s:10:"user-agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:7:"referer";s:53:"http://localhost:8005/backoffice/index.php?r=document";s:4:"host";s:14:"localhost:8005";s:6:"cookie";s:957:"_identity-frontend=ea504c4adbb00d93bcc48cc313183654e5c3f219dbab61d7d91fbc33aaa0c4a5a%3A2%3A%7Bi%3A0%3Bs%3A18%3A%22_identity-frontend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; sidebar-collapse=false; _identity-backend=39d1ba9859efd2bc37503bb1ffdf6627160ca396ce3e4675a5f71e3c133c960ba%3A2%3A%7Bi%3A0%3Bs%3A17%3A%22_identity-backend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; advanced-backend-fmz=kg5944qo99nlrpbhm7q5konqli; _csrf-backend=917abb8469aa4d698f81356ac859964e5a8cac9582dcc38b00411aefe9ca5df9a%3A2%3A%7Bi%3A0%3Bs%3A13%3A%22_csrf-backend%22%3Bi%3A1%3Bs%3A32%3A%22TwezWto5toyW6sW07LndJjdZNt6UE7DV%22%3B%7D; advanced-frontend-fmz=moh7rs0lensqa7tdlnifptpb87; _csrf-frontend=dd73e6ec16db6a655690a449fe4e4fa789f76c9c2c95dfa96614b330565202a0a%3A2%3A%7Bi%3A0%3Bs%3A14%3A%22_csrf-frontend%22%3Bi%3A1%3Bs%3A32%3A%22ca_8DpXaaaTHYgxFa4WS3IK6vVsvuynG%22%3B%7D";s:15:"accept-language";s:14:"en-US,en;q=0.9";s:15:"accept-encoding";s:23:"gzip, deflate, br, zstd";s:6:"accept";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:10:"connection";s:10:"keep-alive";}s:15:"responseHeaders";a:12:{s:12:"X-Powered-By";s:9:"PHP/8.3.3";s:12:"Content-Type";s:15:"application/pdf";s:25:"Content-Transfer-Encoding";s:6:"binary";s:13:"Cache-Control";s:34:"public, must-revalidate, max-age=0";s:6:"Pragma";s:6:"public";s:7:"Expires";s:29:"Sat, 26 Jul 1997 05:00:00 GMT";s:13:"Last-Modified";s:29:"Fri, 29 Aug 2025 12:20:13 GMT";s:19:"Content-Disposition";s:19:"inline; filename=""";s:11:"X-Debug-Tag";s:13:"68b19af83112c";s:16:"X-Debug-Duration";s:5:"5,136";s:12:"X-Debug-Link";s:64:"/backoffice/index.php?r=debug%2Fdefault%2Fview&tag=68b19af83112c";s:10:"Set-Cookie";s:311:"_identity-backend=39d1ba9859efd2bc37503bb1ffdf6627160ca396ce3e4675a5f71e3c133c960ba%3A2%3A%7Bi%3A0%3Bs%3A17%3A%22_identity-backend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; expires=Sun, 28 Sep 2025 12:20:08 GMT; Max-Age=2591995; path=/; HttpOnly; SameSite=Lax";}s:5:"route";s:12:"document/pdf";s:6:"action";s:51:"backend\controllers\DocumentController::actionPdf()";s:12:"actionParams";a:1:{s:2:"id";s:2:"18";}s:7:"general";a:5:{s:6:"method";s:3:"GET";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:0:{}s:6:"SERVER";a:104:{s:13:"_FCGI_X_PIPE_";s:53:"\\.\pipe\IISFCGI-ad81a991-7012-4e01-9c81-464ba6da51d1";s:5:"PHPRC";s:26:"C:\Program Files\PHP\v8.3\";s:21:"PHP_FCGI_MAX_REQUESTS";s:5:"10000";s:15:"ALLUSERSPROFILE";s:14:"C:\ProgramData";s:7:"APPDATA";s:56:"C:\WINDOWS\system32\config\systemprofile\AppData\Roaming";s:15:"APP_POOL_CONFIG";s:57:"C:\inetpub\temp\apppools\Reclassering\Reclassering.config";s:11:"APP_POOL_ID";s:12:"Reclassering";s:17:"ChocolateyInstall";s:25:"C:\ProgramData\chocolatey";s:18:"CommonProgramFiles";s:29:"C:\Program Files\Common Files";s:23:"CommonProgramFiles(x86)";s:35:"C:\Program Files (x86)\Common Files";s:18:"CommonProgramW6432";s:29:"C:\Program Files\Common Files";s:12:"COMPUTERNAME";s:10:"EGOV-L-046";s:7:"ComSpec";s:27:"C:\WINDOWS\system32\cmd.exe";s:10:"DriverData";s:38:"C:\Windows\System32\Drivers\DriverData";s:10:"IGCCSVC_DB";s:416:"AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAxsjL008UHEujcYXte9n1xAQAAAACAAAAAAAQZgAAAAEAACAAAACG6V8fH1MiQpt+vTHr85FUmGEpkihjwnphUZ8EBmjXIgAAAAAOgAAAAAIAACAAAAAwiEFpINOfvm27eQP7A6KJKkU7BiIp8dfO5XJb9O9V82AAAABOuhJMP+CaR040CClff82PsaeGd4XybaOdhylSvqlmGruwc3EJIvD6UYcnWqB0s7SJb8fFsrKIKR8cZ/eXSEmMUiHF6J1xrcv30HL80qkrgc0BXBLoS19xuBSzihq7gPZAAAAABjLonwgGXzz5g0gBw2ytpgnFnasAK0EsnQP6jk1iL/gtzNQgs4cOrzDs1Y9Qn4IMfeSQ9eZqer9cZQRh9WmStg==";s:12:"LOCALAPPDATA";s:54:"C:\WINDOWS\system32\config\systemprofile\AppData\Local";s:20:"NUMBER_OF_PROCESSORS";s:2:"12";s:14:"OnlineServices";s:15:"Online Services";s:2:"OS";s:10:"Windows_NT";s:4:"Path";s:582:"C:\Python313\Scripts\;C:\Python313\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Program Files\PHP\v8.3;C:\ProgramData\ComposerSetup\bin;C:\ProgramData\ComposerSetup\bin\composer.bat;C:\Program Files (x86)\HP\HP OCR\DB_Lib\;C:\Program Files\HP\Common\HPDestPlgIn\;C:\Program Files (x86)\HP\Common\HPDestPlgIn\;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Program Files\GitHub CLI\;C:\WINDOWS\system32\config\systemprofile\AppData\Local\Microsoft\WindowsApps";s:7:"PATHEXT";s:62:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW";s:12:"platformcode";s:2:"AN";s:22:"PROCESSOR_ARCHITECTURE";s:5:"AMD64";s:20:"PROCESSOR_IDENTIFIER";s:51:"Intel64 Family 6 Model 186 Stepping 3, GenuineIntel";s:15:"PROCESSOR_LEVEL";s:1:"6";s:18:"PROCESSOR_REVISION";s:4:"ba03";s:11:"ProgramData";s:14:"C:\ProgramData";s:12:"ProgramFiles";s:16:"C:\Program Files";s:17:"ProgramFiles(x86)";s:22:"C:\Program Files (x86)";s:12:"ProgramW6432";s:16:"C:\Program Files";s:12:"PSModulePath";s:93:"C:\Program Files\WindowsPowerShell\Modules;C:\WINDOWS\system32\WindowsPowerShell\v1.0\Modules";s:6:"PUBLIC";s:15:"C:\Users\<USER>\WINDOWS";s:4:"TEMP";s:15:"C:\WINDOWS\TEMP";s:3:"TMP";s:15:"C:\WINDOWS\TEMP";s:10:"USERDOMAIN";s:3:"GOV";s:8:"USERNAME";s:11:"EGOV-L-046$";s:11:"USERPROFILE";s:40:"C:\WINDOWS\system32\config\systemprofile";s:6:"windir";s:10:"C:\WINDOWS";s:17:"ZES_ENABLE_SYSMAN";s:1:"1";s:14:"ORIG_PATH_INFO";s:21:"/backoffice/index.php";s:3:"URL";s:21:"/backoffice/index.php";s:15:"SERVER_SOFTWARE";s:18:"Microsoft-IIS/10.0";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:18:"SERVER_PORT_SECURE";s:1:"0";s:11:"SERVER_PORT";s:4:"8005";s:11:"SERVER_NAME";s:9:"localhost";s:11:"SCRIPT_NAME";s:21:"/backoffice/index.php";s:15:"SCRIPT_FILENAME";s:41:"C:\Web\Reclassering\backend\web\index.php";s:11:"REQUEST_URI";s:44:"/backoffice/index.php?r=document%2Fpdf&id=18";s:14:"REQUEST_METHOD";s:3:"GET";s:11:"REMOTE_USER";s:0:"";s:11:"REMOTE_PORT";s:5:"56339";s:11:"REMOTE_HOST";s:3:"::1";s:11:"REMOTE_ADDR";s:3:"::1";s:12:"QUERY_STRING";s:22:"r=document%2Fpdf&id=18";s:15:"PATH_TRANSLATED";s:41:"C:\Web\Reclassering\backend\web\index.php";s:10:"LOGON_USER";s:0:"";s:10:"LOCAL_ADDR";s:3:"::1";s:18:"INSTANCE_META_PATH";s:11:"/LM/W3SVC/2";s:13:"INSTANCE_NAME";s:12:"RECLASSERING";s:11:"INSTANCE_ID";s:1:"2";s:20:"HTTPS_SERVER_SUBJECT";s:0:"";s:19:"HTTPS_SERVER_ISSUER";s:0:"";s:19:"HTTPS_SECRETKEYSIZE";s:0:"";s:13:"HTTPS_KEYSIZE";s:0:"";s:5:"HTTPS";s:3:"off";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:13:"DOCUMENT_ROOT";s:32:"C:\Web\Reclassering\frontend\web";s:12:"CONTENT_TYPE";s:0:"";s:14:"CONTENT_LENGTH";s:1:"0";s:12:"CERT_SUBJECT";s:0:"";s:17:"CERT_SERIALNUMBER";s:0:"";s:11:"CERT_ISSUER";s:0:"";s:10:"CERT_FLAGS";s:0:"";s:11:"CERT_COOKIE";s:0:"";s:9:"AUTH_USER";s:0:"";s:13:"AUTH_PASSWORD";s:0:"";s:9:"AUTH_TYPE";s:0:"";s:18:"APPL_PHYSICAL_PATH";s:32:"C:\Web\Reclassering\backend\web\";s:12:"APPL_MD_PATH";s:27:"/LM/W3SVC/2/ROOT/backoffice";s:20:"IIS_UrlRewriteModule";s:13:"7,1,1993,2351";s:19:"HTTP_SEC_FETCH_DEST";s:6:"iframe";s:19:"HTTP_SEC_FETCH_USER";s:2:"?1";s:19:"HTTP_SEC_FETCH_MODE";s:8:"navigate";s:19:"HTTP_SEC_FETCH_SITE";s:11:"same-origin";s:30:"HTTP_UPGRADE_INSECURE_REQUESTS";s:1:"1";s:23:"HTTP_SEC_CH_UA_PLATFORM";s:9:""Windows"";s:21:"HTTP_SEC_CH_UA_MOBILE";s:2:"?0";s:14:"HTTP_SEC_CH_UA";s:65:""Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"";s:15:"HTTP_USER_AGENT";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:12:"HTTP_REFERER";s:53:"http://localhost:8005/backoffice/index.php?r=document";s:9:"HTTP_HOST";s:14:"localhost:8005";s:11:"HTTP_COOKIE";s:957:"_identity-frontend=ea504c4adbb00d93bcc48cc313183654e5c3f219dbab61d7d91fbc33aaa0c4a5a%3A2%3A%7Bi%3A0%3Bs%3A18%3A%22_identity-frontend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; sidebar-collapse=false; _identity-backend=39d1ba9859efd2bc37503bb1ffdf6627160ca396ce3e4675a5f71e3c133c960ba%3A2%3A%7Bi%3A0%3Bs%3A17%3A%22_identity-backend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; advanced-backend-fmz=kg5944qo99nlrpbhm7q5konqli; _csrf-backend=917abb8469aa4d698f81356ac859964e5a8cac9582dcc38b00411aefe9ca5df9a%3A2%3A%7Bi%3A0%3Bs%3A13%3A%22_csrf-backend%22%3Bi%3A1%3Bs%3A32%3A%22TwezWto5toyW6sW07LndJjdZNt6UE7DV%22%3B%7D; advanced-frontend-fmz=moh7rs0lensqa7tdlnifptpb87; _csrf-frontend=dd73e6ec16db6a655690a449fe4e4fa789f76c9c2c95dfa96614b330565202a0a%3A2%3A%7Bi%3A0%3Bs%3A14%3A%22_csrf-frontend%22%3Bi%3A1%3Bs%3A32%3A%22ca_8DpXaaaTHYgxFa4WS3IK6vVsvuynG%22%3B%7D";s:20:"HTTP_ACCEPT_LANGUAGE";s:14:"en-US,en;q=0.9";s:20:"HTTP_ACCEPT_ENCODING";s:23:"gzip, deflate, br, zstd";s:11:"HTTP_ACCEPT";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:9:"FCGI_ROLE";s:9:"RESPONDER";s:8:"PHP_SELF";s:21:"/backoffice/index.php";s:18:"REQUEST_TIME_FLOAT";d:**********.113252;s:12:"REQUEST_TIME";i:**********;}s:3:"GET";a:2:{s:1:"r";s:12:"document/pdf";s:2:"id";s:2:"18";}s:4:"POST";a:0:{}s:6:"COOKIE";a:7:{s:18:"_identity-frontend";s:158:"ea504c4adbb00d93bcc48cc313183654e5c3f219dbab61d7d91fbc33aaa0c4a5a:2:{i:0;s:18:"_identity-frontend";i:1;s:46:"[1,"R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW",2592000]";}";s:16:"sidebar-collapse";s:5:"false";s:17:"_identity-backend";s:157:"39d1ba9859efd2bc37503bb1ffdf6627160ca396ce3e4675a5f71e3c133c960ba:2:{i:0;s:17:"_identity-backend";i:1;s:46:"[1,"R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW",2592000]";}";s:20:"advanced-backend-fmz";s:26:"kg5944qo99nlrpbhm7q5konqli";s:13:"_csrf-backend";s:139:"917abb8469aa4d698f81356ac859964e5a8cac9582dcc38b00411aefe9ca5df9a:2:{i:0;s:13:"_csrf-backend";i:1;s:32:"TwezWto5toyW6sW07LndJjdZNt6UE7DV";}";s:21:"advanced-frontend-fmz";s:26:"moh7rs0lensqa7tdlnifptpb87";s:14:"_csrf-frontend";s:140:"dd73e6ec16db6a655690a449fe4e4fa789f76c9c2c95dfa96614b330565202a0a:2:{i:0;s:14:"_csrf-frontend";i:1;s:32:"ca_8DpXaaaTHYgxFa4WS3IK6vVsvuynG";}";}s:5:"FILES";a:0:{}s:7:"SESSION";a:3:{s:7:"__flash";a:0:{}s:4:"__id";i:1;s:9:"__authKey";s:32:"R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW";}}";s:4:"user";s:1549:"a:5:{s:2:"id";i:1;s:8:"identity";a:12:{s:2:"id";s:1:"1";s:8:"username";s:11:"'SuperUser'";s:8:"auth_key";s:34:"'R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW'";s:13:"password_hash";s:62:"'$2y$13$uUSLwNI3so2bzilzzZdKJ.C1qmfyTdLRRT1XVP8jYO1c38iE6dfxS'";s:20:"password_reset_token";s:4:"null";s:5:"email";s:17:"'<EMAIL>'";s:6:"status";s:2:"10";s:10:"created_at";s:10:"1741788198";s:10:"updated_at";s:10:"1749661338";s:18:"verification_token";s:45:"'oZxacBEp5N7LZAqXc3s1haihOCLK8dLU_1741788198'";s:7:"role_id";s:1:"1";s:12:"access_token";s:66:"'CjYetJZp6PI5vAvKuqm6TDSC_2kYfe_LtOkrjMVtEo4IseTg3J-r-Gp3gMgH-pr7'";}s:10:"attributes";a:12:{i:0;a:2:{s:9:"attribute";s:2:"id";s:5:"label";s:2:"Id";}i:1;a:2:{s:9:"attribute";s:8:"username";s:5:"label";s:8:"Username";}i:2;a:2:{s:9:"attribute";s:8:"auth_key";s:5:"label";s:8:"Auth Key";}i:3;a:2:{s:9:"attribute";s:13:"password_hash";s:5:"label";s:8:"Password";}i:4;a:2:{s:9:"attribute";s:20:"password_reset_token";s:5:"label";s:20:"Password Reset Token";}i:5;a:2:{s:9:"attribute";s:5:"email";s:5:"label";s:5:"Email";}i:6;a:2:{s:9:"attribute";s:6:"status";s:5:"label";s:6:"Status";}i:7;a:2:{s:9:"attribute";s:10:"created_at";s:5:"label";s:10:"Created At";}i:8;a:2:{s:9:"attribute";s:10:"updated_at";s:5:"label";s:10:"Updated At";}i:9;a:2:{s:9:"attribute";s:18:"verification_token";s:5:"label";s:18:"Verification Token";}i:10;a:2:{s:9:"attribute";s:7:"role_id";s:5:"label";s:4:"Role";}i:11;a:2:{s:9:"attribute";s:12:"access_token";s:5:"label";s:12:"Access Token";}}s:13:"rolesProvider";N;s:19:"permissionsProvider";N;}";s:5:"asset";s:6:"a:0:{}";s:7:"summary";a:13:{s:3:"tag";s:13:"68b19af83112c";s:3:"url";s:65:"http://localhost:8005/backoffice/index.php?r=document%2Fpdf&id=18";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:3:"::1";s:4:"time";d:**********.113252;s:10:"statusCode";i:200;s:8:"sqlCount";i:53;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:29060000;s:14:"processingTime";d:5.142040967941284;}s:10:"exceptions";a:0:{}}