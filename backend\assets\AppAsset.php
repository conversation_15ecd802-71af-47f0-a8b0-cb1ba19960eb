<?php

namespace backend\assets;

use yii\web\AssetBundle;

/**
 * Main backend application asset bundle.
 */
class AppAsset extends AssetBundle
{
    public $basePath = '@webroot';
    public $baseUrl = '@web';
    public $css = [
        'css/site.css',
    ];
    public $js = [
        "https://unpkg.com/lucide@latest/dist/umd/lucide.js",
        'js/main.js',
        'https://cdn.jsdelivr.net/npm/sweetalert2@11.7.1/dist/sweetalert2.all.min.js',
    ];
    public $depends = [
        'yii\web\YiiAsset',
        'yii\bootstrap5\BootstrapAsset',
        'yii\web\JqueryAsset',
        'yii\jui\JuiAsset',
        'kartik\sortable\SortableAsset',
        'hail812\adminlte3\assets\AdminLteAsset',
        'hail812\adminlte3\assets\PluginAsset',
        // 'bower-asset\sweetalert2\SweetAlert2Asset',
    ];
}
