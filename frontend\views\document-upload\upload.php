<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;
use kartik\file\FileInput;

$this->title = 'Upload Documents';
?>

<h1><?= Html::encode($this->title) ?></h1>

<?php $form = ActiveForm::begin([
    'options' => ['enctype' => 'multipart/form-data']
]); ?>

<?= $form->field($model, 'upload_file[]')->widget(FileInput::class, [
    'options' => ['multiple' => true, 'accept' => '.pdf,.png,.jpg,.jpeg'],
    'pluginOptions' => [
        'previewFileType' => 'any',
        'showUpload' => false,
        'browseLabel' => 'Select Documents',
        'removeLabel' => '',
        'showCaption' => true,
        'showRemove' => true,
        'showClose' => false,
        'initialPreviewAsData' => true,
    ],
    'pluginEvents' => [
        // Called when files are selected
        'filebatchselected' => 'function(event, files) {
            let container = $("#custom-names-container");
            container.empty();

            for (let i = 0; i < files.length; i++) {
                const name = files[i].name.replace(/\.[^/.]+$/, "");
                const id = files[i].lastModified + "_" + files[i].size;

                container.append(
                    `<div class="form-group custom-name-input" data-id="${id}">
                        <label>Custom name for <strong>${files[i].name}</strong></label>
                        <input type="text" name="custom_names[]" class="form-control" value="${name}">
                    </div>`
                );
            }

            // Store metadata for tracking
            window.selectedFiles = files.map(f => ({
                id: f.lastModified + "_" + f.size,
                name: f.name
            }));
        }',

        // Called when a file is removed via [x]
        'fileremoved' => 'function(event, id, index) {
            const removedFile = window.selectedFiles[index];
            if (removedFile && removedFile.id) {
                $(`.custom-name-input[data-id="${removedFile.id}"]`).remove();
                window.selectedFiles.splice(index, 1); // update array
            }
        }',

        // Called when "Remove" button is pressed (clears all)
        'fileclear' => 'function() {
            $("#custom-names-container").empty();
            window.selectedFiles = [];
        }'
    ]
]) ?>

<div id="custom-names-container" class="mb-4"></div>

<div class="form-group">
    <?= Html::submitButton('Upload', ['class' => 'btn btn-primary']) ?>
</div>

<?php ActiveForm::end(); ?>