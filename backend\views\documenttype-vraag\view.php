<?php

use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\DocumentnaamVraag */
?>
<div class="documentnaam-vraag-view">

    <?= DetailView::widget([
        'model' => $model,
        'attributes' => [
            'id',
            [
                'attribute' => 'document_id',
                'value' => function ($model) {
                    return $model->document->naam;
                }
            ],
            [
                'attribute' => 'vraag_id',
                'value' => function ($model) {
                    return $model->vraag->vraag;
                }
            ],
            'vraag_nummer',
            'created_at',
            'updated_at',
            [
                'attribute' => 'created_by',
                'value' => function ($model) {
                    return $model->createdBy->username;
                }
            ],
            [
                'attribute' => 'created_by',
                'value' => function ($model) {
                    return $model->createdBy->username;
                }
            ],
        ],
    ]) ?>

</div>