<?php
if (!function_exists("isAdminOrSuperAdmin")) {
    function isAdminOrSuperAdmin()
    {
        if (Yii::$app->user->isGuest) {
            return false;
        }

        $user = Yii::$app->user->identity;
        return $user->role_id && in_array($user->role_id, [1, 2]); // 1 = superUser, 2 = admin
    }
}

if (!function_exists("isHoofdOrOnderhoofd")) {
    function isHoofdOrOnderhoofd()
    {
        if (Yii::$app->user->isGuest) {
            return false;
        }

        $user = Yii::$app->user->identity;
        return $user->role_id && in_array($user->role_id, [1, 2, 3]); // 1 = superUser, 2 = admin, 2 = hoofd, 4 = hoofd
    }
}

if (!function_exists("isMedewerker")) {
    function isMedewerker()
    {
        if (Yii::$app->user->isGuest) {
            return false;
        }

        $user = Yii::$app->user->identity;
        return $user->role_id === 4;
    }
}
