<?php

use yii\db\Migration;

/**
 * Sets up example notification configuration for document/create route
 */
class m250715_000000_setup_document_create_notification extends Migration
{
    public function safeUp()
    {
        // Insert example notification for document creation
        $this->insert('{{%notification}}', [
            'key' => 'document_created',
            'title' => 'New Document Created',
            'message_template' => 'A new document "{document_name}" has been created by {username}.',
            'enabled' => 1,
            'send_email' => 0, // Set to 1 to enable email notifications
            'created_at' => new \yii\db\Expression('CURRENT_TIMESTAMP'),
        ]);

        // Get the notification ID
        $notificationId = $this->db->getLastInsertID();

        // Insert notification trigger for document/create route
        $this->insert('{{%notification_trigger}}', [
            'route' => 'document/create',
            'notification_key' => 'document_created',
            'trigger_type' => 'create',
            'model_class' => 'common\\models\\Document',
            'model_id_param' => 'id',
            'fields' => json_encode([
                'document_name' => 'filename',
                'document_type' => 'documenttype.name',
                'created_by_user' => 'createdBy.username'
            ]),
            'link_template' => '/document/view?id={id}',
        ]);

        // You can uncomment and modify these lines to assign the notification to specific roles
        // Make sure the role IDs exist in your role table
        /*
        // Example: Assign to admin role (assuming role ID 1 is admin)
        $this->insert('{{%notification_role}}', [
            'notification_id' => $notificationId,
            'role_id' => 1, // Replace with actual admin role ID
        ]);

        // Example: Assign to manager role (assuming role ID 2 is manager)
        $this->insert('{{%notification_role}}', [
            'notification_id' => $notificationId,
            'role_id' => 2, // Replace with actual manager role ID
        ]);
        */

        echo "Document creation notification setup completed.\n";
        echo "To enable email notifications, update the notification record to set send_email = 1.\n";
        echo "Don't forget to assign roles to the notification in the notification_role table.\n";
    }

    public function safeDown()
    {
        // Remove notification trigger
        $this->delete('{{%notification_trigger}}', ['route' => 'document/create']);
        
        // Remove notification roles (if any were created)
        $notificationId = $this->db->createCommand(
            'SELECT id FROM {{%notification}} WHERE key = :key'
        )->bindValue(':key', 'document_created')->queryScalar();
        
        if ($notificationId) {
            $this->delete('{{%notification_role}}', ['notification_id' => $notificationId]);
        }
        
        // Remove notification
        $this->delete('{{%notification}}', ['key' => 'document_created']);
        
        echo "Document creation notification setup removed.\n";
    }
}
