<?php

use common\components\CreateNewButton;
use common\widgets\RecordSearchWidget;
use yii\helpers\Url;
use yii\helpers\Html;
use yii\bootstrap5\Modal;
use kartik\grid\GridView;
use yii2ajaxcrud\ajaxcrud\CrudAsset;
use yii2ajaxcrud\ajaxcrud\BulkButtonWidget;

/* @var $this yii\web\View */
/* @var $searchModel common\models\search\VraagSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Vraag';
$this->params['breadcrumbs'][] = $this->title;

CrudAsset::register($this);

?>
<div class="vraag-index">
    <div id="ajaxCrudDatatable">
        <?= GridView::widget([
            'id' => 'crud-datatable',
            'dataProvider' => $dataProvider,
            // 'filterModel' => $searchModel,
            'pjax' => true,
            'columns' => require(__DIR__ . '/_columns.php'),
            'toolbar' => [
                [
                    'content' =>
                    CreateNewButton::widget(['options' => ['class' => 'ms-1 btn btn-outline-primary rounded-2']]) .
                        Html::button(
                            '<i data-lucide="file-up" style="width: 18px; height: 18px;"></i> Import',
                            [
                                'class' => 'ms-1 btn btn-outline-success rounded-2',
                                'data-bs-toggle' => 'modal',
                                'data-bs-target' => '#importModal'
                            ]
                        )
                    // '{export}'

                ],
            ],
            'striped' => false,
            'hover' => true,
            'condensed' => true,
            'responsive' => true,
            'panel' => [
                'type' => 'default',
                'heading' => '<i class="fa fa-list"></i> <b>' . $this->title . '</b>',
                'before' => RecordSearchWidget::widget([
                    'id' => 'vraag-search',
                    'placeholder' => 'Search...',
                    'width' => 'w-75',
                    'paramName' => 'globalSearch'  // This will be converted to FunctionalitySearch[globalSearch]
                ]),
                'after' => BulkButtonWidget::widget([
                    'buttons' => Html::a(
                        '<i class="fa fa-trash"></i>&nbsp; ' . Yii::t('yii2-ajaxcrud', 'Delete All'),
                        ["bulkdelete"],
                        [
                            'class' => 'btn btn-danger btn-xs',
                            'role' => 'modal-remote-bulk',
                            'data-confirm' => false,
                            'data-method' => false, // for overide yii data api
                            'data-request-method' => 'post',
                            'data-confirm-title' => Yii::t('yii2-ajaxcrud', 'Delete'),
                            'data-confirm-message' => Yii::t('yii2-ajaxcrud', 'Delete Confirm')
                        ]
                    ),
                ]) .
                    '<div class="clearfix"></div>',
            ]
        ]) ?>
    </div>
</div>
<?php Modal::begin([
    "id" => "ajaxCrudModal",
    "footer" => "", // always need it for jquery plugin
    "clientOptions" => [
        "tabindex" => false,
        "backdrop" => "static",
        "keyboard" => false,
    ],
    "options" => [
        "tabindex" => false
    ]
]) ?>
<?php Modal::end(); ?>

<?php
// Import Modal
Modal::begin([
    'title' => 'Import Vragen',
    'id' => 'importModal',
    'size' => Modal::SIZE_DEFAULT,
    'options' => ['tabindex' => false],
]);
?>
<form id="import-form" action="<?= Url::to(['vraag/import']) ?>" method="post" enctype="multipart/form-data">
    <?= Html::hiddenInput(Yii::$app->request->csrfParam, Yii::$app->request->getCsrfToken()) ?>
    <div class="mb-3">
        <label for="import-file" class="form-label">Select XLSX or CSV file</label>
        <input type="file" class="form-control" id="import-file" name="import-file" accept=".xlsx,.csv" required>
    </div>
    <button type="submit" class="btn btn-success">Upload</button>
</form>
<?php Modal::end(); ?>