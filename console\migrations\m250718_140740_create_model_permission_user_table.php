<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%model_permission_user}}`.
 */
class m250718_140740_create_model_permission_user_table extends Migration
{
    public function safeUp()
    {
        $table = '{{%user_permission}}';
        if ($this->db->schema->getTableSchema($table, true) !== null) {
            $this->dropTable($table);
        }

        $this->createTable('{{%user_permission}}', [
            'id' => $this->primaryKey(),
            'user_id' => $this->integer()->notNull(),
            'route' => $this->string(255)->notNull(), // e.g., 'document-upload/view', 'posts/update'
            'can_access' => $this->boolean()->notNull()->defaultValue(true), // true = allow, false = deny
            'created_at' => $this->dateTime()->defaultExpression('CURRENT_TIMESTAMP'),
            'updated_at' => $this->dateTime()->defaultExpression('CURRENT_TIMESTAMP')->append('ON UPDATE CURRENT_TIMESTAMP'),
        ]);

        $this->addForeignKey('fk_user_permission_user', '{{%user_permission}}', 'user_id', '{{%user}}', 'id', 'CASCADE', 'CASCADE');


        $this->createIndex('idx-user_permission-user-route', '{{%user_permission}}', ['user_id', 'route'], true);
    }

    public function safeDown()
    {
        $this->dropTable('{{%model_permission_user}}');
    }
}
