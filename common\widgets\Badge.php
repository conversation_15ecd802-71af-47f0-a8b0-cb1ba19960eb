<?php

namespace common\widgets;

use yii\base\Widget;
use yii\helpers\Html;

class Badge extends Widget
{
    public $label;
    public $url = null;
    public $options = [];
    public $encode = true;

    public function run()
    {
        $badgeOptions = array_merge(['class' => 'badge bg-primary'], $this->options);
        $content = $this->encode ? Html::encode($this->label) : $this->label;

        if ($this->url) {
            return Html::a($content, $this->url, $badgeOptions);
        }
        return Html::tag('span', $content, $badgeOptions);
    }
}
