<?php

namespace backend\controllers;

use Yii;
use common\models\UserPermission;
use common\models\search\UserPermissionSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use \yii\web\Response;
use yii\helpers\Html;

/**
 * UserPermissionController implements the CRUD actions for UserPermission model.
 */
class UserPermissionController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['post'],
                    'bulkdelete' => ['post'],
                ],
            ],
        ];
    }

    /**
     * Lists all UserPermission models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new UserPermissionSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }


    /**
     * Displays a single UserPermission model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        $request = Yii::$app->request;
        if ($request->isAjax) {
            Yii::$app->response->format = Response::FORMAT_JSON;
            return [
                'title' => "UserPermission #" . $id,
                'content' => $this->renderAjax('view', [
                    'model' => $this->findModel($id),
                ]),
                'footer' => Html::button(Yii::t('yii2-ajaxcrud', 'Close'), ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal']) .
                    Html::a(Yii::t('yii2-ajaxcrud', 'Update'), ['update', 'id' => $id], ['class' => 'btn btn-primary', 'role' => 'modal-remote'])
            ];
        } else {
            return $this->render('view', [
                'model' => $this->findModel($id),
            ]);
        }
    }

    /**
     * Creates a new UserPermission model.
     * For ajax request will return json object
     * and for non-ajax request if creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $request = Yii::$app->request;
        $model = new UserPermission();

        if ($request->isAjax) {
            Yii::$app->response->format = Response::FORMAT_JSON;
            if ($request->isGet) {
                return [
                    'title' => Yii::t('yii2-ajaxcrud', 'Create New') . " UserPermission",
                    'content' => $this->renderAjax('create', [
                        'model' => $model,
                    ]),
                    'footer' => Html::button(Yii::t('yii2-ajaxcrud', 'Close'), ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal']) .
                        Html::button(Yii::t('yii2-ajaxcrud', 'Create'), ['class' => 'btn btn-primary', 'type' => 'submit'])
                ];
            } else if ($request->isPost) {
                $postData = $request->post();

                // Handle multiple routes
                if (isset($postData['UserPermission']['route']) && is_array($postData['UserPermission']['route'])) {
                    $routes = $postData['UserPermission']['route'];
                    $userId = $postData['UserPermission']['user_id'];
                    $canAccess = $postData['UserPermission']['can_access'] ?? 1;

                    $transaction = Yii::$app->db->beginTransaction();
                    try {
                        $savedCount = 0;
                        foreach ($routes as $route) {
                            // Check if permission already exists
                            $existing = UserPermission::find()
                                ->where(['user_id' => $userId, 'route' => $route])
                                ->one();

                            if (!$existing) {
                                $permission = new UserPermission();
                                $permission->user_id = $userId;
                                $permission->route = $route;
                                $permission->can_access = $canAccess;

                                if ($permission->save()) {
                                    $savedCount++;
                                }
                            }
                        }

                        $transaction->commit();

                        return [
                            'forceReload' => '#crud-datatable-pjax',
                            'title' => Yii::t('yii2-ajaxcrud', 'Create New') . " UserPermission",
                            'content' => '<span class="text-success">Created ' . $savedCount . ' user permissions successfully</span>',
                            'footer' => Html::button(Yii::t('yii2-ajaxcrud', 'Close'), ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal']) .
                                Html::a(Yii::t('yii2-ajaxcrud', 'Create More'), ['create'], ['class' => 'btn btn-primary', 'role' => 'modal-remote'])
                        ];
                    } catch (\Exception $e) {
                        $transaction->rollBack();
                        return [
                            'title' => Yii::t('yii2-ajaxcrud', 'Create New') . " UserPermission",
                            'content' => $this->renderAjax('create', ['model' => $model]),
                            'footer' => Html::button(Yii::t('yii2-ajaxcrud', 'Close'), ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal']) .
                                Html::button(Yii::t('yii2-ajaxcrud', 'Save'), ['class' => 'btn btn-primary', 'type' => 'submit'])
                        ];
                    }
                } else if ($model->load($postData) && $model->save()) {
                    return [
                        'forceReload' => '#crud-datatable-pjax',
                        'title' => Yii::t('yii2-ajaxcrud', 'Create New') . " UserPermission",
                        'content' => '<span class="text-success">' . Yii::t('yii2-ajaxcrud', 'Create') . ' UserPermission ' . Yii::t('yii2-ajaxcrud', 'Success') . '</span>',
                        'footer' => Html::button(Yii::t('yii2-ajaxcrud', 'Close'), ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal']) .
                            Html::a(Yii::t('yii2-ajaxcrud', 'Create More'), ['create'], ['class' => 'btn btn-primary', 'role' => 'modal-remote'])
                    ];
                } else {
                    return [
                        'title' => Yii::t('yii2-ajaxcrud', 'Create New') . " UserPermission",
                        'content' => $this->renderAjax('create', ['model' => $model]),
                        'footer' => Html::button(Yii::t('yii2-ajaxcrud', 'Close'), ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal']) .
                            Html::button(Yii::t('yii2-ajaxcrud', 'Save'), ['class' => 'btn btn-primary', 'type' => 'submit'])
                    ];
                }
            }
        } else {
            // Handle non-AJAX request
            if ($model->load($request->post()) && $model->save()) {
                return $this->redirect(['view', 'id' => $model->id]);
            } else {
                return $this->render('create', ['model' => $model]);
            }
        }
    }

    /**
     * Updates an existing UserPermission model.
     * For ajax request will return json object
     * and for non-ajax request if update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $request = Yii::$app->request;
        $model = $this->findModel($id);

        if ($request->isAjax) {
            Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;

            if ($request->isGet) {
                $model->route = [$model->route];
                return [
                    'title' => Yii::t('yii2-ajaxcrud', 'Update') . " UserPermission #" . $id,
                    'content' => $this->renderAjax('update', [
                        'model' => $model,
                    ]),
                    'footer' => Html::button(Yii::t('yii2-ajaxcrud', 'Close'), ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal']) .
                        Html::button(Yii::t('yii2-ajaxcrud', 'Save'), ['class' => 'btn btn-primary', 'type' => 'submit'])
                ];
            }

            if ($model->load($request->post())) {
                $selectedRoutes = $request->post('UserPermission')['route'] ?? [];

                // Fetch all existing route records for this user
                $existingPermissions = UserPermission::find()
                    ->where(['user_id' => $model->user_id])
                    ->indexBy('route')
                    ->all();

                $updatedRoutes = [];

                foreach ($selectedRoutes as $route) {
                    $updatedRoutes[] = $route;

                    if (isset($existingPermissions[$route])) {
                        // Already exists, just ensure can_access is true
                        $existing = $existingPermissions[$route];
                        if ($existing->can_access == 0) {
                            $existing->can_access = 1;
                            $existing->save(); // Don't skip validation/hooks - triggers cache refresh
                        }
                    } else {
                        // New route, create it
                        $new = new UserPermission([
                            'user_id' => $model->user_id,
                            'route' => $route,
                            'can_access' => 1,
                        ]);
                        $new->save(); // Don't skip validation/hooks - triggers cache refresh
                    }
                }

                // Now disable permissions that were unselected
                foreach ($existingPermissions as $route => $permModel) {
                    if (!in_array($route, $updatedRoutes)) {
                        if ($permModel->can_access == 1) {
                            $permModel->can_access = 0;
                            $permModel->save(); // Don't skip validation/hooks - triggers cache refresh
                        }
                    }
                }

                return [
                    'forceReload' => '#crud-datatable-pjax',
                    'title' => Yii::t('yii2-ajaxcrud', 'UserPermission') . " #{$model->id}",
                    'content' => '<span class="text-success">' . Yii::t('yii2-ajaxcrud', 'Update successful') . '</span>',
                    'footer' => Html::button(Yii::t('yii2-ajaxcrud', 'Close'), [
                        'class' => 'btn btn-default pull-left',
                        'data-bs-dismiss' => 'modal'
                    ]) .
                        Html::a(Yii::t('yii2-ajaxcrud', 'Edit'), ['update', 'id' => $model->id], [
                            'class' => 'btn btn-primary',
                            'role' => 'modal-remote'
                        ])
                ];
            }


            return [
                'title' => Yii::t('yii2-ajaxcrud', 'Update') . " UserPermission #" . $id,
                'content' => $this->renderAjax('update', [
                    'model' => $model,
                ]),
                'footer' => Html::button(Yii::t('yii2-ajaxcrud', 'Close'), ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal']) .
                    Html::button(Yii::t('yii2-ajaxcrud', 'Save'), ['class' => 'btn btn-primary', 'type' => 'submit'])
            ];
        }

        // fallback for non-AJAX
        return $this->redirect(['index']);
    }



    /**
     * Delete an existing UserPermission model.
     * For ajax request will return json object
     * and for non-ajax request if deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $request = Yii::$app->request;
        $this->findModel($id)->delete();

        if ($request->isAjax) {
            /*
            *   Process for ajax request
            */
            Yii::$app->response->format = Response::FORMAT_JSON;
            return ['forceClose' => true, 'forceReload' => '#crud-datatable-pjax'];
        } else {
            /*
            *   Process for non-ajax request
            */
            return $this->redirect(['index']);
        }
    }

    /**
     * Delete multiple existing UserPermission model.
     * For ajax request will return json object
     * and for non-ajax request if deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionBulkdelete()
    {
        $request = Yii::$app->request;
        $pks = explode(',', $request->post('pks')); // Array or selected records primary keys
        foreach ($pks as $pk) {
            $model = $this->findModel($pk);
            $model->delete();
        }

        if ($request->isAjax) {
            /*
            *   Process for ajax request
            */
            Yii::$app->response->format = Response::FORMAT_JSON;
            return ['forceClose' => true, 'forceReload' => '#crud-datatable-pjax'];
        } else {
            /*
            *   Process for non-ajax request
            */
            return $this->redirect(['index']);
        }
    }

    /**
     * Finds the UserPermission model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return UserPermission the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = UserPermission::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    public function actionGetRoutes()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        $search = Yii::$app->request->get('q', '');
        $results = Yii::$app->routeService->getRoutesForSelect2($search);

        return ['results' => $results];
    }
}
