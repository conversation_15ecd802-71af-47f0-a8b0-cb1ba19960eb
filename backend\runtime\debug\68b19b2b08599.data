a:14:{s:6:"config";s:15911:"a:5:{s:10:"phpVersion";s:5:"8.3.3";s:10:"yiiVersion";s:6:"2.0.53";s:11:"application";a:8:{s:3:"yii";s:6:"2.0.53";s:4:"name";s:3:"FMZ";s:7:"version";s:3:"1.0";s:8:"language";s:5:"en-US";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:5:"8.3.3";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:71:{s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/faker";s:49:"C:\Web\Reclassering\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.7.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:47:"C:\Web\Reclassering\vendor/yiisoft/yii2-gii/src";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:57:"C:\Web\Reclassering\vendor/yiisoft/yii2-symfonymailer/src";}}s:29:"hail812/yii2-adminlte-widgets";a:3:{s:4:"name";s:29:"hail812/yii2-adminlte-widgets";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:25:"@hail812/adminlte/widgets";s:60:"C:\Web\Reclassering\vendor/hail812/yii2-adminlte-widgets/src";}}s:23:"yiisoft/yii2-bootstrap4";a:3:{s:4:"name";s:23:"yiisoft/yii2-bootstrap4";s:7:"version";s:8:"2.0.12.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap4";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-bootstrap4/src";}}s:22:"hail812/yii2-adminlte3";a:3:{s:4:"name";s:22:"hail812/yii2-adminlte3";s:7:"version";s:7:"1.1.9.0";s:5:"alias";a:1:{s:18:"@hail812/adminlte3";s:53:"C:\Web\Reclassering\vendor/hail812/yii2-adminlte3/src";}}s:18:"kartik-v/yii2-mpdf";a:3:{s:4:"name";s:18:"kartik-v/yii2-mpdf";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:12:"@kartik/mpdf";s:49:"C:\Web\Reclassering\vendor/kartik-v/yii2-mpdf/src";}}s:25:"kartik-v/yii2-krajee-base";a:3:{s:4:"name";s:25:"kartik-v/yii2-krajee-base";s:7:"version";s:7:"3.0.5.0";s:5:"alias";a:1:{s:12:"@kartik/base";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-krajee-base/src";}}s:20:"kartik-v/yii2-dialog";a:3:{s:4:"name";s:20:"kartik-v/yii2-dialog";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:14:"@kartik/dialog";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-dialog/src";}}s:23:"kartik-v/yii2-popover-x";a:3:{s:4:"name";s:23:"kartik-v/yii2-popover-x";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:15:"@kartik/popover";s:54:"C:\Web\Reclassering\vendor/kartik-v/yii2-popover-x/src";}}s:21:"kartik-v/yii2-builder";a:3:{s:4:"name";s:21:"kartik-v/yii2-builder";s:7:"version";s:7:"1.6.9.0";s:5:"alias";a:1:{s:15:"@kartik/builder";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-builder/src";}}s:22:"kartik-v/yii2-editable";a:3:{s:4:"name";s:22:"kartik-v/yii2-editable";s:7:"version";s:7:"1.8.0.0";s:5:"alias";a:1:{s:16:"@kartik/editable";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-editable/src";}}s:18:"kartik-v/yii2-grid";a:3:{s:4:"name";s:18:"kartik-v/yii2-grid";s:7:"version";s:7:"3.5.3.0";s:5:"alias";a:1:{s:12:"@kartik/grid";s:49:"C:\Web\Reclassering\vendor/kartik-v/yii2-grid/src";}}s:21:"kartik-v/yii2-helpers";a:3:{s:4:"name";s:21:"kartik-v/yii2-helpers";s:7:"version";s:7:"1.3.9.0";s:5:"alias";a:1:{s:15:"@kartik/helpers";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-helpers/src";}}s:24:"kartik-v/yii2-checkbox-x";a:3:{s:4:"name";s:24:"kartik-v/yii2-checkbox-x";s:7:"version";s:7:"1.0.7.0";s:5:"alias";a:1:{s:16:"@kartik/checkbox";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-checkbox-x/src";}}s:26:"kartik-v/yii2-context-menu";a:3:{s:4:"name";s:26:"kartik-v/yii2-context-menu";s:7:"version";s:7:"1.2.4.0";s:5:"alias";a:1:{s:13:"@kartik/cmenu";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-context-menu/src";}}s:25:"kartik-v/yii2-datecontrol";a:3:{s:4:"name";s:25:"kartik-v/yii2-datecontrol";s:7:"version";s:7:"1.9.9.0";s:5:"alias";a:1:{s:19:"@kartik/datecontrol";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-datecontrol/src";}}s:22:"kartik-v/yii2-sortable";a:3:{s:4:"name";s:22:"kartik-v/yii2-sortable";s:7:"version";s:7:"1.2.2.0";s:5:"alias";a:1:{s:16:"@kartik/sortable";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-sortable/src";}}s:25:"kartik-v/yii2-field-range";a:3:{s:4:"name";s:25:"kartik-v/yii2-field-range";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:13:"@kartik/field";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-field-range/src";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:8:"2.0.16.0";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-httpclient/src";}}s:25:"kartik-v/yii2-detail-view";a:3:{s:4:"name";s:25:"kartik-v/yii2-detail-view";s:7:"version";s:7:"1.8.7.0";s:5:"alias";a:1:{s:14:"@kartik/detail";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-detail-view/src";}}s:24:"kartik-v/yii2-date-range";a:3:{s:4:"name";s:24:"kartik-v/yii2-date-range";s:7:"version";s:7:"1.7.3.0";s:5:"alias";a:1:{s:17:"@kartik/daterange";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-date-range/src";}}s:22:"kartik-v/yii2-dynagrid";a:3:{s:4:"name";s:22:"kartik-v/yii2-dynagrid";s:7:"version";s:7:"1.5.5.0";s:5:"alias";a:1:{s:16:"@kartik/dynagrid";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-dynagrid/src";}}s:16:"yiisoft/yii2-jui";a:3:{s:4:"name";s:16:"yiisoft/yii2-jui";s:7:"version";s:7:"2.0.7.0";s:5:"alias";a:1:{s:8:"@yii/jui";s:43:"C:\Web\Reclassering\vendor/yiisoft/yii2-jui";}}s:27:"kartik-v/yii2-label-inplace";a:3:{s:4:"name";s:27:"kartik-v/yii2-label-inplace";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/label";s:58:"C:\Web\Reclassering\vendor/kartik-v/yii2-label-inplace/src";}}s:19:"kartik-v/yii2-icons";a:3:{s:4:"name";s:19:"kartik-v/yii2-icons";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/icons";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-icons/src";}}s:19:"kartik-v/yii2-money";a:3:{s:4:"name";s:19:"kartik-v/yii2-money";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/money";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-money/src";}}s:20:"kartik-v/yii2-ipinfo";a:3:{s:4:"name";s:20:"kartik-v/yii2-ipinfo";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:14:"@kartik/ipinfo";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-ipinfo/src";}}s:22:"kartik-v/yii2-markdown";a:3:{s:4:"name";s:22:"kartik-v/yii2-markdown";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/markdown";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-markdown/src";}}s:24:"kartik-v/yii2-dropdown-x";a:3:{s:4:"name";s:24:"kartik-v/yii2-dropdown-x";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/dropdown";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-dropdown-x/src";}}s:19:"kartik-v/yii2-nav-x";a:3:{s:4:"name";s:19:"kartik-v/yii2-nav-x";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:11:"@kartik/nav";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-nav-x/src";}}s:22:"kartik-v/yii2-password";a:3:{s:4:"name";s:22:"kartik-v/yii2-password";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/password";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-password/src";}}s:20:"kartik-v/yii2-slider";a:3:{s:4:"name";s:20:"kartik-v/yii2-slider";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:14:"@kartik/slider";s:47:"C:\Web\Reclassering\vendor/kartik-v/yii2-slider";}}s:28:"kartik-v/yii2-sortable-input";a:3:{s:4:"name";s:28:"kartik-v/yii2-sortable-input";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:17:"@kartik/sortinput";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-sortable-input/src";}}s:20:"kartik-v/yii2-tabs-x";a:3:{s:4:"name";s:20:"kartik-v/yii2-tabs-x";s:7:"version";s:7:"1.2.9.0";s:5:"alias";a:1:{s:12:"@kartik/tabs";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-tabs-x/src";}}s:30:"kartik-v/yii2-widget-typeahead";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-typeahead";s:7:"version";s:7:"1.0.4.0";s:5:"alias";a:1:{s:17:"@kartik/typeahead";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-typeahead/src";}}s:20:"kartik-v/yii2-social";a:3:{s:4:"name";s:20:"kartik-v/yii2-social";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:14:"@kartik/social";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-social/src";}}s:30:"kartik-v/yii2-widget-touchspin";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-touchspin";s:7:"version";s:7:"1.2.4.0";s:5:"alias";a:1:{s:17:"@kartik/touchspin";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-touchspin/src";}}s:32:"kartik-v/yii2-widget-switchinput";a:3:{s:4:"name";s:32:"kartik-v/yii2-widget-switchinput";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:19:"@kartik/switchinput";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-switchinput";}}s:24:"kartik-v/yii2-validators";a:4:{s:4:"name";s:24:"kartik-v/yii2-validators";s:7:"version";s:7:"1.0.3.0";s:5:"alias";a:1:{s:18:"@kartik/validators";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-validators/src";}s:9:"bootstrap";s:27:"kartik\validators\Bootstrap";}s:28:"kartik-v/yii2-widget-spinner";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-spinner";s:7:"version";s:7:"1.0.1.0";s:5:"alias";a:1:{s:15:"@kartik/spinner";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-spinner/src";}}s:28:"kartik-v/yii2-widget-sidenav";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-sidenav";s:7:"version";s:7:"1.0.1.0";s:5:"alias";a:1:{s:15:"@kartik/sidenav";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-sidenav";}}s:27:"kartik-v/yii2-widget-rating";a:3:{s:4:"name";s:27:"kartik-v/yii2-widget-rating";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:14:"@kartik/rating";s:58:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-rating/src";}}s:31:"kartik-v/yii2-widget-rangeinput";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-rangeinput";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:13:"@kartik/range";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-rangeinput/src";}}s:26:"kartik-v/yii2-widget-growl";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-growl";s:7:"version";s:7:"1.1.2.0";s:5:"alias";a:1:{s:13:"@kartik/growl";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-growl/src";}}s:28:"kartik-v/yii2-widget-depdrop";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-depdrop";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:15:"@kartik/depdrop";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-depdrop/src";}}s:31:"kartik-v/yii2-widget-colorinput";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-colorinput";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:13:"@kartik/color";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-colorinput/src";}}s:35:"kartik-v/yii2-widget-datetimepicker";a:3:{s:4:"name";s:35:"kartik-v/yii2-widget-datetimepicker";s:7:"version";s:7:"1.5.1.0";s:5:"alias";a:1:{s:16:"@kartik/datetime";s:66:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-datetimepicker/src";}}s:31:"kartik-v/yii2-widget-datepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-datepicker";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:12:"@kartik/date";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-datepicker/src";}}s:26:"kartik-v/yii2-widget-alert";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-alert";s:7:"version";s:7:"1.1.5.0";s:5:"alias";a:1:{s:13:"@kartik/alert";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-alert/src";}}s:26:"kartik-v/yii2-widget-affix";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-affix";s:7:"version";s:7:"1.0.0.0";s:5:"alias";a:1:{s:13:"@kartik/affix";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-affix";}}s:21:"kartik-v/yii2-widgets";a:3:{s:4:"name";s:21:"kartik-v/yii2-widgets";s:7:"version";s:7:"3.4.1.0";s:5:"alias";a:1:{s:15:"@kartik/widgets";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-widgets/src";}}s:33:"kartik-v/yii2-bootstrap5-dropdown";a:3:{s:4:"name";s:33:"kartik-v/yii2-bootstrap5-dropdown";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:19:"@kartik/bs5dropdown";s:64:"C:\Web\Reclassering\vendor/kartik-v/yii2-bootstrap5-dropdown/src";}}s:26:"biladina/yii2-ajaxcrud-bs4";a:4:{s:4:"name";s:26:"biladina/yii2-ajaxcrud-bs4";s:7:"version";s:7:"3.0.0.0";s:5:"alias";a:1:{s:22:"@yii2ajaxcrud/ajaxcrud";s:57:"C:\Web\Reclassering\vendor/biladina/yii2-ajaxcrud-bs4/src";}s:9:"bootstrap";s:31:"yii2ajaxcrud\ajaxcrud\Bootstrap";}s:28:"kartik-v/yii2-widget-select2";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-select2";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@kartik/select2";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-select2/src";}}s:31:"kartik-v/yii2-widget-activeform";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-activeform";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/form";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-activeform/src";}}s:23:"raoul2000/yii2-workflow";a:3:{s:4:"name";s:23:"raoul2000/yii2-workflow";s:7:"version";s:7:"1.2.0.0";s:5:"alias";a:1:{s:19:"@raoul2000/workflow";s:54:"C:\Web\Reclassering\vendor/raoul2000/yii2-workflow/src";}}s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:8:"2.0.50.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:20:"kartik-v/yii2-export";a:3:{s:4:"name";s:20:"kartik-v/yii2-export";s:7:"version";s:7:"1.4.3.0";s:5:"alias";a:1:{s:14:"@kartik/export";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-export/src";}}s:28:"raoul2000/yii2-workflow-view";a:3:{s:4:"name";s:28:"raoul2000/yii2-workflow-view";s:7:"version";s:7:"0.0.2.0";s:5:"alias";a:1:{s:24:"@raoul2000/workflow/view";s:59:"C:\Web\Reclassering\vendor/raoul2000/yii2-workflow-view/src";}}s:32:"cornernote/yii2-workflow-manager";a:3:{s:4:"name";s:32:"cornernote/yii2-workflow-manager";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:28:"@cornernote/workflow/manager";s:63:"C:\Web\Reclassering\vendor/cornernote/yii2-workflow-manager/src";}}s:28:"2amigos/yii2-ckeditor-widget";a:3:{s:4:"name";s:28:"2amigos/yii2-ckeditor-widget";s:7:"version";s:7:"2.1.0.0";s:5:"alias";a:1:{s:19:"@dosamigos/ckeditor";s:59:"C:\Web\Reclassering\vendor/2amigos/yii2-ckeditor-widget/src";}}s:17:"yiisoft/yii2-twig";a:3:{s:4:"name";s:17:"yiisoft/yii2-twig";s:7:"version";s:7:"2.5.1.0";s:5:"alias";a:1:{s:9:"@yii/twig";s:48:"C:\Web\Reclassering\vendor/yiisoft/yii2-twig/src";}}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.27.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:49:"C:\Web\Reclassering\vendor/yiisoft/yii2-debug/src";}}s:27:"2amigos/yii2-chartjs-widget";a:3:{s:4:"name";s:27:"2amigos/yii2-chartjs-widget";s:7:"version";s:7:"2.1.3.0";s:5:"alias";a:1:{s:18:"@dosamigos/chartjs";s:58:"C:\Web\Reclassering\vendor/2amigos/yii2-chartjs-widget/src";}}s:19:"bedezign/yii2-audit";a:4:{s:4:"name";s:19:"bedezign/yii2-audit";s:7:"version";s:7:"1.2.7.0";s:5:"alias";a:1:{s:20:"@bedezign/yii2/audit";s:50:"C:\Web\Reclassering\vendor/bedezign/yii2-audit/src";}s:9:"bootstrap";s:29:"bedezign\yii2\audit\Bootstrap";}s:30:"kartik-v/yii2-widget-fileinput";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-fileinput";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/file";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-fileinput/src";}}s:24:"rmrevin/yii2-fontawesome";a:3:{s:4:"name";s:24:"rmrevin/yii2-fontawesome";s:7:"version";s:8:"2.10.3.0";s:5:"alias";a:1:{s:24:"@rmrevin/yii/fontawesome";s:51:"C:\Web\Reclassering\vendor/rmrevin/yii2-fontawesome";}}s:24:"edofre/yii2-fullcalendar";a:3:{s:4:"name";s:24:"edofre/yii2-fullcalendar";s:7:"version";s:8:"1.0.11.0";s:5:"alias";a:1:{s:20:"@edofre/fullcalendar";s:55:"C:\Web\Reclassering\vendor/edofre/yii2-fullcalendar/src";}}s:31:"kartik-v/yii2-widget-timepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-timepicker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/time";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-timepicker/src";}}s:34:"miloschuman/yii2-highcharts-widget";a:3:{s:4:"name";s:34:"miloschuman/yii2-highcharts-widget";s:7:"version";s:8:"11.0.0.0";s:5:"alias";a:1:{s:23:"@miloschuman/highcharts";s:65:"C:\Web\Reclassering\vendor/miloschuman/yii2-highcharts-widget/src";}}}}";s:3:"log";s:14914:"a:1:{s:8:"messages";a:33:{i:0;a:6:{i:0;s:55:"Bootstrap with kartik\validators\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.988251;i:4;a:0:{}i:5;i:2915896;}i:1;a:6:{i:0;s:59:"Bootstrap with yii2ajaxcrud\ajaxcrud\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.988908;i:4;a:0:{}i:5;i:3021120;}i:2;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.988914;i:4;a:0:{}i:5;i:3021416;}i:3;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.989185;i:4;a:0:{}i:5;i:3051416;}i:4;a:6:{i:0;s:57:"Bootstrap with bedezign\yii2\audit\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.989424;i:4;a:0:{}i:5;i:3078888;}i:5;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.990247;i:4;a:0:{}i:5;i:3233920;}i:6;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.990255;i:4;a:0:{}i:5;i:3234560;}i:7;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:**********.995346;i:4;a:0:{}i:5;i:4232176;}i:8;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.003146;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5556272;}i:9;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:1756470059.003171;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5558552;}i:14;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.015365;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5616368;}i:17;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.016687;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5637088;}i:20;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.020664;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6106904;}i:23;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1756470059.03269;i:4;a:0:{}i:5;i:6799424;}i:24;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1756470059.035927;i:4;a:0:{}i:5;i:6979520;}i:25;a:6:{i:0;s:21:"Loading module: audit";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1756470059.035965;i:4;a:0:{}i:5;i:6980160;}i:26;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.038238;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7050080;}i:29;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.040618;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7061712;}i:32;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.041554;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7061496;}i:35;a:6:{i:0;s:40:"Bootstrap with bedezign\yii2\audit\Audit";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1756470059.054179;i:4;a:0:{}i:5;i:7241928;}i:37;a:6:{i:0;s:39:"Route requested: 'document-type/update'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:1756470059.05431;i:4;a:0:{}i:5;i:7240864;}i:38;a:6:{i:0;s:34:"Route to run: document-type/update";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:1756470059.057997;i:4;a:0:{}i:5;i:7410056;}i:39;a:6:{i:0;s:167:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('document-type/update', '1', '::1', 0, 'POST', '2025-08-29 13:20:59')";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1756470059.067969;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7681200;}i:42;a:6:{i:0;s:74:"Running action: backend\controllers\DocumentTypeController::actionUpdate()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:1756470059.073727;i:4;a:0:{}i:5;i:7691184;}i:43;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `document_type`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.074042;i:4;a:2:{i:0;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:259;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:143;s:8:"function";s:9:"findModel";s:5:"class";s:42:"backend\controllers\DocumentTypeController";s:4:"type";s:2:"->";}}i:5;i:7726688;}i:46;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.076343;i:4;a:2:{i:0;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:259;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:143;s:8:"function";s:9:"findModel";s:5:"class";s:42:"backend\controllers\DocumentTypeController";s:4:"type";s:2:"->";}}i:5;i:7738384;}i:49;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_type' AND `kcu`.`TABLE_NAME` = 'document_type'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.077371;i:4;a:2:{i:0;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:259;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:143;s:8:"function";s:9:"findModel";s:5:"class";s:42:"backend\controllers\DocumentTypeController";s:4:"type";s:2:"->";}}i:5;i:7739696;}i:52;a:6:{i:0;s:44:"SELECT * FROM `document_type` WHERE `id`='8'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.079106;i:4;a:2:{i:0;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:259;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:143;s:8:"function";s:9:"findModel";s:5:"class";s:42:"backend\controllers\DocumentTypeController";s:4:"type";s:2:"->";}}i:5;i:7743480;}i:55;a:6:{i:0;s:67:"SELECT EXISTS(SELECT * FROM `user` WHERE `user`.`id`=1) AS `result`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.084913;i:4;a:1:{i:0;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:184;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7984640;}i:58;a:6:{i:0;s:67:"SELECT EXISTS(SELECT * FROM `user` WHERE `user`.`id`=1) AS `result`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.086792;i:4;a:1:{i:0;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:184;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7987960;}i:61;a:6:{i:0;s:2234:"UPDATE `document_type` SET `handmatig_invoer`=1, `layout_template`='<h1 style=\"text-align: center;\"><b>Ministerie van Justitie en Politie</b></h1>\r\n\r\n<h3 style=\"text-align: center;\">Onderdirectoraat Forensische Zorg</h3>\r\n\r\n<h3 style=\"text-align: center;\">Afdeling: Forensisch Maatschappelijke Zorg</h3>\r\n\r\n<h3 style=\"text-align: center;\">Tibitistraat 4 Paramaribo</h3>\r\n\r\n<h3 style=\"text-align: center;\">Tel: 427197 toestel 2720 of 2711</h3>\r\n\r\n<hr />\r\n<p><strong>Paramaribo, {{ currentDate }}</strong></p>\r\n\r\n<p> </p>\r\n\r\n<p><strong>Ons kenmerk: F.M.Z No ..... / 20</strong></p>\r\n\r\n<p> </p>\r\n\r\n<h4><b>VOORLICHTINGSRAPPORT UITGEBRACHT AAN DE HEER/MEVROUW RECHTER IN HET .......... KANTON BETREFFENDE:</b></h4>\r\n\r\n<hr />\r\n<p> </p>\r\n\r\n<table align=\"left\" border=\"0\" cellpadding=\"1\" cellspacing=\"0\" style=\"width: 500px;\">\r\n	<tbody>\r\n		<tr>\r\n			<td>\r\n			<p>NAAM:</p>\r\n			</td>\r\n			<td>\r\n			<p> {{ data.questions_and_answers[0].answer.text }}</p>\r\n			</td>\r\n		</tr>\r\n		<tr>\r\n			<td>\r\n			<p>VOORNAAM:</p>\r\n			</td>\r\n			<td>\r\n			<p> {{ data.questions_and_answers[1].answer.text }}</p>\r\n			</td>\r\n		</tr>\r\n		<tr>\r\n			<td>\r\n			<p>GEB.DD. / PLAATS:</p>\r\n			</td>\r\n			<td>\r\n			<p> {{ data.questions_and_answers[2].answer.text }}</p>\r\n			</td>\r\n		</tr>\r\n	</tbody>\r\n</table>\r\n\r\n<p> </p>\r\n\r\n<p>{% for qa in data.questions_and_answers|slice(9) %}</p>\r\n\r\n<p style=\"text-transform: uppercase\"><ins><strong>{{ qa.question.text }}</strong></ins></p>\r\n\r\n<p>{{ qa.answer.text }}</p>\r\n\r\n<p>{% endfor %}</p>\r\n\r\n<p> </p>\r\n\r\n<p>{% for signature in data.signatures %} {% endfor %}</p>\r\n\r\n<table width=\"100%\">\r\n	<tbody>\r\n		<tr>\r\n			<td style=\"text-align: \r\n                        {% if loop.index == 1 %}left\r\n                        {% elseif loop.index == 2 %}right\r\n                        {% else %}center\r\n                        {% endif %};\"><img alt=\"Signature\" height=\"150\" src=\"{{ signature.signature }}\" width=\"150\" />\r\n			<p>{{ signature.user.username }}</p>\r\n			</td>\r\n		</tr>\r\n	</tbody>\r\n</table>\r\n', `updated_at`=CURRENT_TIMESTAMP WHERE `id`=8";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1756470059.08804;i:4;a:1:{i:0;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:184;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7989448;}i:64;a:6:{i:0;s:73:"SELECT * FROM `notification_trigger` WHERE `route`='document-type/update'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.09352;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8048416;}i:67;a:6:{i:0;s:90:"UPDATE `audit_entry` SET `duration`=0.11436486244202, `memory_max`=8080648 WHERE `id`=3784";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1756470059.094615;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:8052472;}}}";s:9:"profiling";s:25047:"a:3:{s:6:"memory";i:8723768;s:4:"time";d:0.12169694900512695;s:8:"messages";a:36:{i:10;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:1756470059.003178;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5559360;}i:11;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:1756470059.012986;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5602664;}i:12;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.013012;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5602448;}i:13;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.015299;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5615080;}i:15;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.015385;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5617280;}i:16;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.01616;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5619856;}i:18;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.016702;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5638128;}i:19;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.018238;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5640656;}i:21;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.020678;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6107288;}i:22;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.021134;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6109656;}i:27;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.038305;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7050992;}i:28;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.040574;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7060424;}i:30;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.040633;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7062624;}i:31;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.041421;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7064536;}i:33;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.041569;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7063176;}i:34;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.043566;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7065032;}i:40;a:6:{i:0;s:167:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('document-type/update', '1', '::1', 0, 'POST', '2025-08-29 13:20:59')";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1756470059.068029;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7682560;}i:41;a:6:{i:0;s:167:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('document-type/update', '1', '::1', 0, 'POST', '2025-08-29 13:20:59')";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1756470059.073128;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7684352;}i:44;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `document_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.074061;i:4;a:2:{i:0;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:259;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:143;s:8:"function";s:9:"findModel";s:5:"class";s:42:"backend\controllers\DocumentTypeController";s:4:"type";s:2:"->";}}i:5;i:7727976;}i:45;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `document_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.0763;i:4;a:2:{i:0;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:259;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:143;s:8:"function";s:9:"findModel";s:5:"class";s:42:"backend\controllers\DocumentTypeController";s:4:"type";s:2:"->";}}i:5;i:7736712;}i:47;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.076358;i:4;a:2:{i:0;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:259;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:143;s:8:"function";s:9:"findModel";s:5:"class";s:42:"backend\controllers\DocumentTypeController";s:4:"type";s:2:"->";}}i:5;i:7739672;}i:48;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.077252;i:4;a:2:{i:0;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:259;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:143;s:8:"function";s:9:"findModel";s:5:"class";s:42:"backend\controllers\DocumentTypeController";s:4:"type";s:2:"->";}}i:5;i:7742216;}i:50;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_type' AND `kcu`.`TABLE_NAME` = 'document_type'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.077384;i:4;a:2:{i:0;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:259;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:143;s:8:"function";s:9:"findModel";s:5:"class";s:42:"backend\controllers\DocumentTypeController";s:4:"type";s:2:"->";}}i:5;i:7741112;}i:51;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_type' AND `kcu`.`TABLE_NAME` = 'document_type'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.079007;i:4;a:2:{i:0;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:259;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:143;s:8:"function";s:9:"findModel";s:5:"class";s:42:"backend\controllers\DocumentTypeController";s:4:"type";s:2:"->";}}i:5;i:7744576;}i:53;a:6:{i:0;s:44:"SELECT * FROM `document_type` WHERE `id`='8'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.079118;i:4;a:2:{i:0;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:259;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:143;s:8:"function";s:9:"findModel";s:5:"class";s:42:"backend\controllers\DocumentTypeController";s:4:"type";s:2:"->";}}i:5;i:7744984;}i:54;a:6:{i:0;s:44:"SELECT * FROM `document_type` WHERE `id`='8'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.07965;i:4;a:2:{i:0;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:259;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:143;s:8:"function";s:9:"findModel";s:5:"class";s:42:"backend\controllers\DocumentTypeController";s:4:"type";s:2:"->";}}i:5;i:7749008;}i:56;a:6:{i:0;s:67:"SELECT EXISTS(SELECT * FROM `user` WHERE `user`.`id`=1) AS `result`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.084985;i:4;a:1:{i:0;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:184;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7985768;}i:57;a:6:{i:0;s:67:"SELECT EXISTS(SELECT * FROM `user` WHERE `user`.`id`=1) AS `result`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.086648;i:4;a:1:{i:0;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:184;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7986696;}i:59;a:6:{i:0;s:67:"SELECT EXISTS(SELECT * FROM `user` WHERE `user`.`id`=1) AS `result`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.086808;i:4;a:1:{i:0;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:184;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7989088;}i:60;a:6:{i:0;s:67:"SELECT EXISTS(SELECT * FROM `user` WHERE `user`.`id`=1) AS `result`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.0876;i:4;a:1:{i:0;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:184;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7990016;}i:62;a:6:{i:0;s:2234:"UPDATE `document_type` SET `handmatig_invoer`=1, `layout_template`='<h1 style=\"text-align: center;\"><b>Ministerie van Justitie en Politie</b></h1>\r\n\r\n<h3 style=\"text-align: center;\">Onderdirectoraat Forensische Zorg</h3>\r\n\r\n<h3 style=\"text-align: center;\">Afdeling: Forensisch Maatschappelijke Zorg</h3>\r\n\r\n<h3 style=\"text-align: center;\">Tibitistraat 4 Paramaribo</h3>\r\n\r\n<h3 style=\"text-align: center;\">Tel: 427197 toestel 2720 of 2711</h3>\r\n\r\n<hr />\r\n<p><strong>Paramaribo, {{ currentDate }}</strong></p>\r\n\r\n<p> </p>\r\n\r\n<p><strong>Ons kenmerk: F.M.Z No ..... / 20</strong></p>\r\n\r\n<p> </p>\r\n\r\n<h4><b>VOORLICHTINGSRAPPORT UITGEBRACHT AAN DE HEER/MEVROUW RECHTER IN HET .......... KANTON BETREFFENDE:</b></h4>\r\n\r\n<hr />\r\n<p> </p>\r\n\r\n<table align=\"left\" border=\"0\" cellpadding=\"1\" cellspacing=\"0\" style=\"width: 500px;\">\r\n	<tbody>\r\n		<tr>\r\n			<td>\r\n			<p>NAAM:</p>\r\n			</td>\r\n			<td>\r\n			<p> {{ data.questions_and_answers[0].answer.text }}</p>\r\n			</td>\r\n		</tr>\r\n		<tr>\r\n			<td>\r\n			<p>VOORNAAM:</p>\r\n			</td>\r\n			<td>\r\n			<p> {{ data.questions_and_answers[1].answer.text }}</p>\r\n			</td>\r\n		</tr>\r\n		<tr>\r\n			<td>\r\n			<p>GEB.DD. / PLAATS:</p>\r\n			</td>\r\n			<td>\r\n			<p> {{ data.questions_and_answers[2].answer.text }}</p>\r\n			</td>\r\n		</tr>\r\n	</tbody>\r\n</table>\r\n\r\n<p> </p>\r\n\r\n<p>{% for qa in data.questions_and_answers|slice(9) %}</p>\r\n\r\n<p style=\"text-transform: uppercase\"><ins><strong>{{ qa.question.text }}</strong></ins></p>\r\n\r\n<p>{{ qa.answer.text }}</p>\r\n\r\n<p>{% endfor %}</p>\r\n\r\n<p> </p>\r\n\r\n<p>{% for signature in data.signatures %} {% endfor %}</p>\r\n\r\n<table width=\"100%\">\r\n	<tbody>\r\n		<tr>\r\n			<td style=\"text-align: \r\n                        {% if loop.index == 1 %}left\r\n                        {% elseif loop.index == 2 %}right\r\n                        {% else %}center\r\n                        {% endif %};\"><img alt=\"Signature\" height=\"150\" src=\"{{ signature.signature }}\" width=\"150\" />\r\n			<p>{{ signature.user.username }}</p>\r\n			</td>\r\n		</tr>\r\n	</tbody>\r\n</table>\r\n', `updated_at`=CURRENT_TIMESTAMP WHERE `id`=8";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1756470059.088057;i:4;a:1:{i:0;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:184;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7990368;}i:63;a:6:{i:0;s:2234:"UPDATE `document_type` SET `handmatig_invoer`=1, `layout_template`='<h1 style=\"text-align: center;\"><b>Ministerie van Justitie en Politie</b></h1>\r\n\r\n<h3 style=\"text-align: center;\">Onderdirectoraat Forensische Zorg</h3>\r\n\r\n<h3 style=\"text-align: center;\">Afdeling: Forensisch Maatschappelijke Zorg</h3>\r\n\r\n<h3 style=\"text-align: center;\">Tibitistraat 4 Paramaribo</h3>\r\n\r\n<h3 style=\"text-align: center;\">Tel: 427197 toestel 2720 of 2711</h3>\r\n\r\n<hr />\r\n<p><strong>Paramaribo, {{ currentDate }}</strong></p>\r\n\r\n<p> </p>\r\n\r\n<p><strong>Ons kenmerk: F.M.Z No ..... / 20</strong></p>\r\n\r\n<p> </p>\r\n\r\n<h4><b>VOORLICHTINGSRAPPORT UITGEBRACHT AAN DE HEER/MEVROUW RECHTER IN HET .......... KANTON BETREFFENDE:</b></h4>\r\n\r\n<hr />\r\n<p> </p>\r\n\r\n<table align=\"left\" border=\"0\" cellpadding=\"1\" cellspacing=\"0\" style=\"width: 500px;\">\r\n	<tbody>\r\n		<tr>\r\n			<td>\r\n			<p>NAAM:</p>\r\n			</td>\r\n			<td>\r\n			<p> {{ data.questions_and_answers[0].answer.text }}</p>\r\n			</td>\r\n		</tr>\r\n		<tr>\r\n			<td>\r\n			<p>VOORNAAM:</p>\r\n			</td>\r\n			<td>\r\n			<p> {{ data.questions_and_answers[1].answer.text }}</p>\r\n			</td>\r\n		</tr>\r\n		<tr>\r\n			<td>\r\n			<p>GEB.DD. / PLAATS:</p>\r\n			</td>\r\n			<td>\r\n			<p> {{ data.questions_and_answers[2].answer.text }}</p>\r\n			</td>\r\n		</tr>\r\n	</tbody>\r\n</table>\r\n\r\n<p> </p>\r\n\r\n<p>{% for qa in data.questions_and_answers|slice(9) %}</p>\r\n\r\n<p style=\"text-transform: uppercase\"><ins><strong>{{ qa.question.text }}</strong></ins></p>\r\n\r\n<p>{{ qa.answer.text }}</p>\r\n\r\n<p>{% endfor %}</p>\r\n\r\n<p> </p>\r\n\r\n<p>{% for signature in data.signatures %} {% endfor %}</p>\r\n\r\n<table width=\"100%\">\r\n	<tbody>\r\n		<tr>\r\n			<td style=\"text-align: \r\n                        {% if loop.index == 1 %}left\r\n                        {% elseif loop.index == 2 %}right\r\n                        {% else %}center\r\n                        {% endif %};\"><img alt=\"Signature\" height=\"150\" src=\"{{ signature.signature }}\" width=\"150\" />\r\n			<p>{{ signature.user.username }}</p>\r\n			</td>\r\n		</tr>\r\n	</tbody>\r\n</table>\r\n', `updated_at`=CURRENT_TIMESTAMP WHERE `id`=8";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1756470059.092433;i:4;a:1:{i:0;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:184;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7993792;}i:65;a:6:{i:0;s:73:"SELECT * FROM `notification_trigger` WHERE `route`='document-type/update'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.09354;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8050824;}i:66;a:6:{i:0;s:73:"SELECT * FROM `notification_trigger` WHERE `route`='document-type/update'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.094304;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8051928;}i:68;a:6:{i:0;s:90:"UPDATE `audit_entry` SET `duration`=0.11436486244202, `memory_max`=8080648 WHERE `id`=3784";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1756470059.094643;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:8053816;}i:69;a:6:{i:0;s:90:"UPDATE `audit_entry` SET `duration`=0.11436486244202, `memory_max`=8080648 WHERE `id`=3784";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1756470059.097853;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:8055216;}}}";s:2:"db";s:24277:"a:1:{s:8:"messages";a:34:{i:12;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.013012;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5602448;}i:13;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.015299;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5615080;}i:15;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.015385;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5617280;}i:16;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.01616;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5619856;}i:18;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.016702;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5638128;}i:19;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.018238;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5640656;}i:21;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.020678;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6107288;}i:22;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.021134;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6109656;}i:27;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.038305;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7050992;}i:28;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.040574;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7060424;}i:30;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.040633;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7062624;}i:31;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.041421;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7064536;}i:33;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.041569;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7063176;}i:34;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.043566;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7065032;}i:40;a:6:{i:0;s:167:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('document-type/update', '1', '::1', 0, 'POST', '2025-08-29 13:20:59')";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1756470059.068029;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7682560;}i:41;a:6:{i:0;s:167:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('document-type/update', '1', '::1', 0, 'POST', '2025-08-29 13:20:59')";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1756470059.073128;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7684352;}i:44;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `document_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.074061;i:4;a:2:{i:0;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:259;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:143;s:8:"function";s:9:"findModel";s:5:"class";s:42:"backend\controllers\DocumentTypeController";s:4:"type";s:2:"->";}}i:5;i:7727976;}i:45;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `document_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.0763;i:4;a:2:{i:0;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:259;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:143;s:8:"function";s:9:"findModel";s:5:"class";s:42:"backend\controllers\DocumentTypeController";s:4:"type";s:2:"->";}}i:5;i:7736712;}i:47;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.076358;i:4;a:2:{i:0;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:259;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:143;s:8:"function";s:9:"findModel";s:5:"class";s:42:"backend\controllers\DocumentTypeController";s:4:"type";s:2:"->";}}i:5;i:7739672;}i:48;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.077252;i:4;a:2:{i:0;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:259;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:143;s:8:"function";s:9:"findModel";s:5:"class";s:42:"backend\controllers\DocumentTypeController";s:4:"type";s:2:"->";}}i:5;i:7742216;}i:50;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_type' AND `kcu`.`TABLE_NAME` = 'document_type'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.077384;i:4;a:2:{i:0;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:259;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:143;s:8:"function";s:9:"findModel";s:5:"class";s:42:"backend\controllers\DocumentTypeController";s:4:"type";s:2:"->";}}i:5;i:7741112;}i:51;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_type' AND `kcu`.`TABLE_NAME` = 'document_type'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.079007;i:4;a:2:{i:0;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:259;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:143;s:8:"function";s:9:"findModel";s:5:"class";s:42:"backend\controllers\DocumentTypeController";s:4:"type";s:2:"->";}}i:5;i:7744576;}i:53;a:6:{i:0;s:44:"SELECT * FROM `document_type` WHERE `id`='8'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.079118;i:4;a:2:{i:0;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:259;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:143;s:8:"function";s:9:"findModel";s:5:"class";s:42:"backend\controllers\DocumentTypeController";s:4:"type";s:2:"->";}}i:5;i:7744984;}i:54;a:6:{i:0;s:44:"SELECT * FROM `document_type` WHERE `id`='8'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.07965;i:4;a:2:{i:0;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:259;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:143;s:8:"function";s:9:"findModel";s:5:"class";s:42:"backend\controllers\DocumentTypeController";s:4:"type";s:2:"->";}}i:5;i:7749008;}i:56;a:6:{i:0;s:67:"SELECT EXISTS(SELECT * FROM `user` WHERE `user`.`id`=1) AS `result`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.084985;i:4;a:1:{i:0;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:184;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7985768;}i:57;a:6:{i:0;s:67:"SELECT EXISTS(SELECT * FROM `user` WHERE `user`.`id`=1) AS `result`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.086648;i:4;a:1:{i:0;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:184;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7986696;}i:59;a:6:{i:0;s:67:"SELECT EXISTS(SELECT * FROM `user` WHERE `user`.`id`=1) AS `result`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.086808;i:4;a:1:{i:0;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:184;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7989088;}i:60;a:6:{i:0;s:67:"SELECT EXISTS(SELECT * FROM `user` WHERE `user`.`id`=1) AS `result`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.0876;i:4;a:1:{i:0;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:184;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7990016;}i:62;a:6:{i:0;s:2234:"UPDATE `document_type` SET `handmatig_invoer`=1, `layout_template`='<h1 style=\"text-align: center;\"><b>Ministerie van Justitie en Politie</b></h1>\r\n\r\n<h3 style=\"text-align: center;\">Onderdirectoraat Forensische Zorg</h3>\r\n\r\n<h3 style=\"text-align: center;\">Afdeling: Forensisch Maatschappelijke Zorg</h3>\r\n\r\n<h3 style=\"text-align: center;\">Tibitistraat 4 Paramaribo</h3>\r\n\r\n<h3 style=\"text-align: center;\">Tel: 427197 toestel 2720 of 2711</h3>\r\n\r\n<hr />\r\n<p><strong>Paramaribo, {{ currentDate }}</strong></p>\r\n\r\n<p> </p>\r\n\r\n<p><strong>Ons kenmerk: F.M.Z No ..... / 20</strong></p>\r\n\r\n<p> </p>\r\n\r\n<h4><b>VOORLICHTINGSRAPPORT UITGEBRACHT AAN DE HEER/MEVROUW RECHTER IN HET .......... KANTON BETREFFENDE:</b></h4>\r\n\r\n<hr />\r\n<p> </p>\r\n\r\n<table align=\"left\" border=\"0\" cellpadding=\"1\" cellspacing=\"0\" style=\"width: 500px;\">\r\n	<tbody>\r\n		<tr>\r\n			<td>\r\n			<p>NAAM:</p>\r\n			</td>\r\n			<td>\r\n			<p> {{ data.questions_and_answers[0].answer.text }}</p>\r\n			</td>\r\n		</tr>\r\n		<tr>\r\n			<td>\r\n			<p>VOORNAAM:</p>\r\n			</td>\r\n			<td>\r\n			<p> {{ data.questions_and_answers[1].answer.text }}</p>\r\n			</td>\r\n		</tr>\r\n		<tr>\r\n			<td>\r\n			<p>GEB.DD. / PLAATS:</p>\r\n			</td>\r\n			<td>\r\n			<p> {{ data.questions_and_answers[2].answer.text }}</p>\r\n			</td>\r\n		</tr>\r\n	</tbody>\r\n</table>\r\n\r\n<p> </p>\r\n\r\n<p>{% for qa in data.questions_and_answers|slice(9) %}</p>\r\n\r\n<p style=\"text-transform: uppercase\"><ins><strong>{{ qa.question.text }}</strong></ins></p>\r\n\r\n<p>{{ qa.answer.text }}</p>\r\n\r\n<p>{% endfor %}</p>\r\n\r\n<p> </p>\r\n\r\n<p>{% for signature in data.signatures %} {% endfor %}</p>\r\n\r\n<table width=\"100%\">\r\n	<tbody>\r\n		<tr>\r\n			<td style=\"text-align: \r\n                        {% if loop.index == 1 %}left\r\n                        {% elseif loop.index == 2 %}right\r\n                        {% else %}center\r\n                        {% endif %};\"><img alt=\"Signature\" height=\"150\" src=\"{{ signature.signature }}\" width=\"150\" />\r\n			<p>{{ signature.user.username }}</p>\r\n			</td>\r\n		</tr>\r\n	</tbody>\r\n</table>\r\n', `updated_at`=CURRENT_TIMESTAMP WHERE `id`=8";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1756470059.088057;i:4;a:1:{i:0;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:184;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7990368;}i:63;a:6:{i:0;s:2234:"UPDATE `document_type` SET `handmatig_invoer`=1, `layout_template`='<h1 style=\"text-align: center;\"><b>Ministerie van Justitie en Politie</b></h1>\r\n\r\n<h3 style=\"text-align: center;\">Onderdirectoraat Forensische Zorg</h3>\r\n\r\n<h3 style=\"text-align: center;\">Afdeling: Forensisch Maatschappelijke Zorg</h3>\r\n\r\n<h3 style=\"text-align: center;\">Tibitistraat 4 Paramaribo</h3>\r\n\r\n<h3 style=\"text-align: center;\">Tel: 427197 toestel 2720 of 2711</h3>\r\n\r\n<hr />\r\n<p><strong>Paramaribo, {{ currentDate }}</strong></p>\r\n\r\n<p> </p>\r\n\r\n<p><strong>Ons kenmerk: F.M.Z No ..... / 20</strong></p>\r\n\r\n<p> </p>\r\n\r\n<h4><b>VOORLICHTINGSRAPPORT UITGEBRACHT AAN DE HEER/MEVROUW RECHTER IN HET .......... KANTON BETREFFENDE:</b></h4>\r\n\r\n<hr />\r\n<p> </p>\r\n\r\n<table align=\"left\" border=\"0\" cellpadding=\"1\" cellspacing=\"0\" style=\"width: 500px;\">\r\n	<tbody>\r\n		<tr>\r\n			<td>\r\n			<p>NAAM:</p>\r\n			</td>\r\n			<td>\r\n			<p> {{ data.questions_and_answers[0].answer.text }}</p>\r\n			</td>\r\n		</tr>\r\n		<tr>\r\n			<td>\r\n			<p>VOORNAAM:</p>\r\n			</td>\r\n			<td>\r\n			<p> {{ data.questions_and_answers[1].answer.text }}</p>\r\n			</td>\r\n		</tr>\r\n		<tr>\r\n			<td>\r\n			<p>GEB.DD. / PLAATS:</p>\r\n			</td>\r\n			<td>\r\n			<p> {{ data.questions_and_answers[2].answer.text }}</p>\r\n			</td>\r\n		</tr>\r\n	</tbody>\r\n</table>\r\n\r\n<p> </p>\r\n\r\n<p>{% for qa in data.questions_and_answers|slice(9) %}</p>\r\n\r\n<p style=\"text-transform: uppercase\"><ins><strong>{{ qa.question.text }}</strong></ins></p>\r\n\r\n<p>{{ qa.answer.text }}</p>\r\n\r\n<p>{% endfor %}</p>\r\n\r\n<p> </p>\r\n\r\n<p>{% for signature in data.signatures %} {% endfor %}</p>\r\n\r\n<table width=\"100%\">\r\n	<tbody>\r\n		<tr>\r\n			<td style=\"text-align: \r\n                        {% if loop.index == 1 %}left\r\n                        {% elseif loop.index == 2 %}right\r\n                        {% else %}center\r\n                        {% endif %};\"><img alt=\"Signature\" height=\"150\" src=\"{{ signature.signature }}\" width=\"150\" />\r\n			<p>{{ signature.user.username }}</p>\r\n			</td>\r\n		</tr>\r\n	</tbody>\r\n</table>\r\n', `updated_at`=CURRENT_TIMESTAMP WHERE `id`=8";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1756470059.092433;i:4;a:1:{i:0;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\DocumentTypeController.php";s:4:"line";i:184;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7993792;}i:65;a:6:{i:0;s:73:"SELECT * FROM `notification_trigger` WHERE `route`='document-type/update'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.09354;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8050824;}i:66;a:6:{i:0;s:73:"SELECT * FROM `notification_trigger` WHERE `route`='document-type/update'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756470059.094304;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8051928;}i:68;a:6:{i:0;s:90:"UPDATE `audit_entry` SET `duration`=0.11436486244202, `memory_max`=8080648 WHERE `id`=3784";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1756470059.094643;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:8053816;}i:69;a:6:{i:0;s:90:"UPDATE `audit_entry` SET `duration`=0.11436486244202, `memory_max`=8080648 WHERE `id`=3784";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1756470059.097853;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:8055216;}}}";s:5:"event";s:5739:"a:32:{i:0;a:5:{s:4:"time";d:1756470059.001376;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:1;a:5:{s:4:"time";d:1756470059.012978;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:2;a:5:{s:4:"time";d:1756470059.021374;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:3;a:5:{s:4:"time";d:1756470059.021401;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:4;a:5:{s:4:"time";d:1756470059.02744;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:5;a:5:{s:4:"time";d:1756470059.054259;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:6;a:5:{s:4:"time";d:1756470059.062079;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:7;a:5:{s:4:"time";d:1756470059.062139;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:8;a:5:{s:4:"time";d:1756470059.066532;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:9;a:5:{s:4:"time";d:1756470059.073426;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:10;a:5:{s:4:"time";d:1756470059.07344;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:11;a:5:{s:4:"time";d:1756470059.073666;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:42:"backend\controllers\DocumentTypeController";}i:12;a:5:{s:4:"time";d:1756470059.074006;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:13;a:5:{s:4:"time";d:1756470059.080326;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:14;a:5:{s:4:"time";d:1756470059.080359;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:15;a:5:{s:4:"time";d:1756470059.082659;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:16;a:5:{s:4:"time";d:1756470059.08302;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:17;a:5:{s:4:"time";d:1756470059.083215;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:18;a:5:{s:4:"time";d:1756470059.086701;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:19;a:5:{s:4:"time";d:1756470059.086728;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:20;a:5:{s:4:"time";d:1756470059.087634;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:21;a:5:{s:4:"time";d:1756470059.087717;s:4:"name";s:12:"beforeUpdate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:22;a:5:{s:4:"time";d:1756470059.092477;s:4:"name";s:11:"afterUpdate";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:23;a:5:{s:4:"time";d:1756470059.093141;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:42:"backend\controllers\DocumentTypeController";}i:24;a:5:{s:4:"time";d:1756470059.093422;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:25;a:5:{s:4:"time";d:1756470059.094351;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:26;a:5:{s:4:"time";d:1756470059.094457;s:4:"name";s:12:"beforeUpdate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:27;a:5:{s:4:"time";d:1756470059.097927;s:4:"name";s:11:"afterUpdate";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:28;a:5:{s:4:"time";d:1756470059.097947;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:29;a:5:{s:4:"time";d:1756470059.097967;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:30;a:5:{s:4:"time";d:1756470059.098916;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:31;a:5:{s:4:"time";d:1756470059.098982;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:91:"a:3:{s:5:"start";d:**********.980029;s:3:"end";d:1756470059.102056;s:6:"memory";i:8723768;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:325:"a:3:{s:8:"messages";a:1:{i:36;a:6:{i:0;s:56:"Pretty URL not enabled. Using default URL parsing logic.";i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1756470059.054283;i:4;a:0:{}i:5;i:7240960;}}s:5:"route";s:20:"document-type/update";s:6:"action";s:58:"backend\controllers\DocumentTypeController::actionUpdate()";}";s:7:"request";s:19421:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:302;s:14:"requestHeaders";a:20:{s:12:"content-type";s:33:"application/x-www-form-urlencoded";s:14:"content-length";s:4:"3491";s:14:"sec-fetch-dest";s:8:"document";s:14:"sec-fetch-user";s:2:"?1";s:14:"sec-fetch-mode";s:8:"navigate";s:14:"sec-fetch-site";s:11:"same-origin";s:25:"upgrade-insecure-requests";s:1:"1";s:6:"origin";s:21:"http://localhost:8005";s:18:"sec-ch-ua-platform";s:9:""Windows"";s:16:"sec-ch-ua-mobile";s:2:"?0";s:9:"sec-ch-ua";s:65:""Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"";s:10:"user-agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:7:"referer";s:72:"http://localhost:8005/backoffice/index.php?r=document-type%2Fupdate&id=8";s:4:"host";s:14:"localhost:8005";s:6:"cookie";s:957:"_identity-frontend=ea504c4adbb00d93bcc48cc313183654e5c3f219dbab61d7d91fbc33aaa0c4a5a%3A2%3A%7Bi%3A0%3Bs%3A18%3A%22_identity-frontend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; sidebar-collapse=false; _identity-backend=39d1ba9859efd2bc37503bb1ffdf6627160ca396ce3e4675a5f71e3c133c960ba%3A2%3A%7Bi%3A0%3Bs%3A17%3A%22_identity-backend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; advanced-backend-fmz=kg5944qo99nlrpbhm7q5konqli; _csrf-backend=917abb8469aa4d698f81356ac859964e5a8cac9582dcc38b00411aefe9ca5df9a%3A2%3A%7Bi%3A0%3Bs%3A13%3A%22_csrf-backend%22%3Bi%3A1%3Bs%3A32%3A%22TwezWto5toyW6sW07LndJjdZNt6UE7DV%22%3B%7D; advanced-frontend-fmz=moh7rs0lensqa7tdlnifptpb87; _csrf-frontend=dd73e6ec16db6a655690a449fe4e4fa789f76c9c2c95dfa96614b330565202a0a%3A2%3A%7Bi%3A0%3Bs%3A14%3A%22_csrf-frontend%22%3Bi%3A1%3Bs%3A32%3A%22ca_8DpXaaaTHYgxFa4WS3IK6vVsvuynG%22%3B%7D";s:15:"accept-language";s:14:"en-US,en;q=0.9";s:15:"accept-encoding";s:23:"gzip, deflate, br, zstd";s:6:"accept";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:10:"connection";s:10:"keep-alive";s:13:"cache-control";s:9:"max-age=0";}s:15:"responseHeaders";a:10:{s:12:"X-Powered-By";s:9:"PHP/8.3.3";s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:35:"no-store, no-cache, must-revalidate";s:6:"Pragma";s:8:"no-cache";s:8:"Location";s:72:"http://localhost:8005/backoffice/index.php?r=document-type%2Fupdate&id=8";s:12:"Content-Type";s:24:"text/html; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"68b19b2b08599";s:16:"X-Debug-Duration";s:3:"120";s:12:"X-Debug-Link";s:64:"/backoffice/index.php?r=debug%2Fdefault%2Fview&tag=68b19b2b08599";s:10:"Set-Cookie";s:311:"_identity-backend=39d1ba9859efd2bc37503bb1ffdf6627160ca396ce3e4675a5f71e3c133c960ba%3A2%3A%7Bi%3A0%3Bs%3A17%3A%22_identity-backend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; expires=Sun, 28 Sep 2025 12:20:59 GMT; Max-Age=2592000; path=/; HttpOnly; SameSite=Lax";}s:5:"route";s:20:"document-type/update";s:6:"action";s:58:"backend\controllers\DocumentTypeController::actionUpdate()";s:12:"actionParams";a:1:{s:2:"id";s:1:"8";}s:7:"general";a:5:{s:6:"method";s:4:"POST";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:3:{s:12:"Content Type";s:33:"application/x-www-form-urlencoded";s:3:"Raw";s:3491:"_csrf-backend=1_jIeJSlU5ESdm3Sq8VSS5bEMAo3rTe_AgTbrzFiH6-Dj60Cw9E8pGYZFIWdtgV7oYhebn3HU-VMcO36dFVb-Q%3D%3D&DocumentType%5Btype%5D=VOORLICHTINGSRAPPORT&DocumentType%5Blayout_template%5D=%3Ch1+style%3D%22text-align%3A+center%3B%22%3E%3Cb%3EMinisterie+van+Justitie+en+Politie%3C%2Fb%3E%3C%2Fh1%3E%0D%0A%0D%0A%3Ch3+style%3D%22text-align%3A+center%3B%22%3EOnderdirectoraat+Forensische+Zorg%3C%2Fh3%3E%0D%0A%0D%0A%3Ch3+style%3D%22text-align%3A+center%3B%22%3EAfdeling%3A+Forensisch+Maatschappelijke+Zorg%3C%2Fh3%3E%0D%0A%0D%0A%3Ch3+style%3D%22text-align%3A+center%3B%22%3ETibitistraat+4+Paramaribo%3C%2Fh3%3E%0D%0A%0D%0A%3Ch3+style%3D%22text-align%3A+center%3B%22%3ETel%3A+427197+toestel+2720+of+2711%3C%2Fh3%3E%0D%0A%0D%0A%3Chr+%2F%3E%0D%0A%3Cp%3E%3Cstrong%3EParamaribo%2C+%7B%7B+currentDate+%7D%7D%3C%2Fstrong%3E%3C%2Fp%3E%0D%0A%0D%0A%3Cp%3E%C2%A0%3C%2Fp%3E%0D%0A%0D%0A%3Cp%3E%3Cstrong%3EOns+kenmerk%3A+F.M.Z+No+.....+%2F+20%3C%2Fstrong%3E%3C%2Fp%3E%0D%0A%0D%0A%3Cp%3E%C2%A0%3C%2Fp%3E%0D%0A%0D%0A%3Ch4%3E%3Cb%3EVOORLICHTINGSRAPPORT+UITGEBRACHT+AAN+DE+HEER%2FMEVROUW+RECHTER+IN+HET+..........+KANTON+BETREFFENDE%3A%3C%2Fb%3E%3C%2Fh4%3E%0D%0A%0D%0A%3Chr+%2F%3E%0D%0A%3Cp%3E%C2%A0%3C%2Fp%3E%0D%0A%0D%0A%3Ctable+align%3D%22left%22+border%3D%220%22+cellpadding%3D%221%22+cellspacing%3D%220%22+style%3D%22width%3A+500px%3B%22%3E%0D%0A%09%3Ctbody%3E%0D%0A%09%09%3Ctr%3E%0D%0A%09%09%09%3Ctd%3E%0D%0A%09%09%09%3Cp%3ENAAM%3A%3C%2Fp%3E%0D%0A%09%09%09%3C%2Ftd%3E%0D%0A%09%09%09%3Ctd%3E%0D%0A%09%09%09%3Cp%3E%C2%A0%7B%7B+data.questions_and_answers%5B0%5D.answer.text+%7D%7D%3C%2Fp%3E%0D%0A%09%09%09%3C%2Ftd%3E%0D%0A%09%09%3C%2Ftr%3E%0D%0A%09%09%3Ctr%3E%0D%0A%09%09%09%3Ctd%3E%0D%0A%09%09%09%3Cp%3EVOORNAAM%3A%3C%2Fp%3E%0D%0A%09%09%09%3C%2Ftd%3E%0D%0A%09%09%09%3Ctd%3E%0D%0A%09%09%09%3Cp%3E%C2%A0%7B%7B+data.questions_and_answers%5B1%5D.answer.text+%7D%7D%3C%2Fp%3E%0D%0A%09%09%09%3C%2Ftd%3E%0D%0A%09%09%3C%2Ftr%3E%0D%0A%09%09%3Ctr%3E%0D%0A%09%09%09%3Ctd%3E%0D%0A%09%09%09%3Cp%3EGEB.DD.+%2F+PLAATS%3A%3C%2Fp%3E%0D%0A%09%09%09%3C%2Ftd%3E%0D%0A%09%09%09%3Ctd%3E%0D%0A%09%09%09%3Cp%3E%C2%A0%7B%7B+data.questions_and_answers%5B2%5D.answer.text+%7D%7D%3C%2Fp%3E%0D%0A%09%09%09%3C%2Ftd%3E%0D%0A%09%09%3C%2Ftr%3E%0D%0A%09%3C%2Ftbody%3E%0D%0A%3C%2Ftable%3E%0D%0A%0D%0A%3Cp%3E%C2%A0%3C%2Fp%3E%0D%0A%0D%0A%3Cp%3E%7B%25+for+qa+in+data.questions_and_answers%7Cslice%289%29+%25%7D%3C%2Fp%3E%0D%0A%0D%0A%3Cp+style%3D%22text-transform%3A+uppercase%22%3E%3Cins%3E%3Cstrong%3E%7B%7B+qa.question.text+%7D%7D%3C%2Fstrong%3E%3C%2Fins%3E%3C%2Fp%3E%0D%0A%0D%0A%3Cp%3E%7B%7B+qa.answer.text+%7D%7D%3C%2Fp%3E%0D%0A%0D%0A%3Cp%3E%7B%25+endfor+%25%7D%3C%2Fp%3E%0D%0A%0D%0A%3Cp%3E%C2%A0%3C%2Fp%3E%0D%0A%0D%0A%3Cp%3E%7B%25+for+signature+in+data.signatures+%25%7D+%7B%25+endfor+%25%7D%3C%2Fp%3E%0D%0A%0D%0A%3Ctable+width%3D%22100%25%22%3E%0D%0A%09%3Ctbody%3E%0D%0A%09%09%3Ctr%3E%0D%0A%09%09%09%3Ctd+style%3D%22text-align%3A+%0D%0A++++++++++++++++++++++++%7B%25+if+loop.index+%3D%3D+1+%25%7Dleft%0D%0A++++++++++++++++++++++++%7B%25+elseif+loop.index+%3D%3D+2+%25%7Dright%0D%0A++++++++++++++++++++++++%7B%25+else+%25%7Dcenter%0D%0A++++++++++++++++++++++++%7B%25+endif+%25%7D%3B%22%3E%3Cimg+alt%3D%22Signature%22+height%3D%22150%22+src%3D%22%7B%7B+signature.signature+%7D%7D%22+width%3D%22150%22+%2F%3E%0D%0A%09%09%09%3Cp%3E%7B%7B+signature.user.username+%7D%7D%3C%2Fp%3E%0D%0A%09%09%09%3C%2Ftd%3E%0D%0A%09%09%3C%2Ftr%3E%0D%0A%09%3C%2Ftbody%3E%0D%0A%3C%2Ftable%3E%0D%0A&DocumentType%5Bhandmatig_invoer%5D=0&DocumentType%5Bhandmatig_invoer%5D=1";s:7:"Decoded";a:2:{s:13:"_csrf-backend";s:88:"1_jIeJSlU5ESdm3Sq8VSS5bEMAo3rTe_AgTbrzFiH6-Dj60Cw9E8pGYZFIWdtgV7oYhebn3HU-VMcO36dFVb-Q==";s:12:"DocumentType";a:3:{s:4:"type";s:20:"VOORLICHTINGSRAPPORT";s:15:"layout_template";s:1926:"<h1 style="text-align: center;"><b>Ministerie van Justitie en Politie</b></h1>

<h3 style="text-align: center;">Onderdirectoraat Forensische Zorg</h3>

<h3 style="text-align: center;">Afdeling: Forensisch Maatschappelijke Zorg</h3>

<h3 style="text-align: center;">Tibitistraat 4 Paramaribo</h3>

<h3 style="text-align: center;">Tel: 427197 toestel 2720 of 2711</h3>

<hr />
<p><strong>Paramaribo, {{ currentDate }}</strong></p>

<p> </p>

<p><strong>Ons kenmerk: F.M.Z No ..... / 20</strong></p>

<p> </p>

<h4><b>VOORLICHTINGSRAPPORT UITGEBRACHT AAN DE HEER/MEVROUW RECHTER IN HET .......... KANTON BETREFFENDE:</b></h4>

<hr />
<p> </p>

<table align="left" border="0" cellpadding="1" cellspacing="0" style="width: 500px;">
	<tbody>
		<tr>
			<td>
			<p>NAAM:</p>
			</td>
			<td>
			<p> {{ data.questions_and_answers[0].answer.text }}</p>
			</td>
		</tr>
		<tr>
			<td>
			<p>VOORNAAM:</p>
			</td>
			<td>
			<p> {{ data.questions_and_answers[1].answer.text }}</p>
			</td>
		</tr>
		<tr>
			<td>
			<p>GEB.DD. / PLAATS:</p>
			</td>
			<td>
			<p> {{ data.questions_and_answers[2].answer.text }}</p>
			</td>
		</tr>
	</tbody>
</table>

<p> </p>

<p>{% for qa in data.questions_and_answers|slice(9) %}</p>

<p style="text-transform: uppercase"><ins><strong>{{ qa.question.text }}</strong></ins></p>

<p>{{ qa.answer.text }}</p>

<p>{% endfor %}</p>

<p> </p>

<p>{% for signature in data.signatures %} {% endfor %}</p>

<table width="100%">
	<tbody>
		<tr>
			<td style="text-align: 
                        {% if loop.index == 1 %}left
                        {% elseif loop.index == 2 %}right
                        {% else %}center
                        {% endif %};"><img alt="Signature" height="150" src="{{ signature.signature }}" width="150" />
			<p>{{ signature.user.username }}</p>
			</td>
		</tr>
	</tbody>
</table>
";s:16:"handmatig_invoer";s:1:"1";}}}s:6:"SERVER";a:108:{s:13:"_FCGI_X_PIPE_";s:53:"\\.\pipe\IISFCGI-ad81a991-7012-4e01-9c81-464ba6da51d1";s:5:"PHPRC";s:26:"C:\Program Files\PHP\v8.3\";s:21:"PHP_FCGI_MAX_REQUESTS";s:5:"10000";s:15:"ALLUSERSPROFILE";s:14:"C:\ProgramData";s:7:"APPDATA";s:56:"C:\WINDOWS\system32\config\systemprofile\AppData\Roaming";s:15:"APP_POOL_CONFIG";s:57:"C:\inetpub\temp\apppools\Reclassering\Reclassering.config";s:11:"APP_POOL_ID";s:12:"Reclassering";s:17:"ChocolateyInstall";s:25:"C:\ProgramData\chocolatey";s:18:"CommonProgramFiles";s:29:"C:\Program Files\Common Files";s:23:"CommonProgramFiles(x86)";s:35:"C:\Program Files (x86)\Common Files";s:18:"CommonProgramW6432";s:29:"C:\Program Files\Common Files";s:12:"COMPUTERNAME";s:10:"EGOV-L-046";s:7:"ComSpec";s:27:"C:\WINDOWS\system32\cmd.exe";s:10:"DriverData";s:38:"C:\Windows\System32\Drivers\DriverData";s:10:"IGCCSVC_DB";s:416:"AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAxsjL008UHEujcYXte9n1xAQAAAACAAAAAAAQZgAAAAEAACAAAACG6V8fH1MiQpt+vTHr85FUmGEpkihjwnphUZ8EBmjXIgAAAAAOgAAAAAIAACAAAAAwiEFpINOfvm27eQP7A6KJKkU7BiIp8dfO5XJb9O9V82AAAABOuhJMP+CaR040CClff82PsaeGd4XybaOdhylSvqlmGruwc3EJIvD6UYcnWqB0s7SJb8fFsrKIKR8cZ/eXSEmMUiHF6J1xrcv30HL80qkrgc0BXBLoS19xuBSzihq7gPZAAAAABjLonwgGXzz5g0gBw2ytpgnFnasAK0EsnQP6jk1iL/gtzNQgs4cOrzDs1Y9Qn4IMfeSQ9eZqer9cZQRh9WmStg==";s:12:"LOCALAPPDATA";s:54:"C:\WINDOWS\system32\config\systemprofile\AppData\Local";s:20:"NUMBER_OF_PROCESSORS";s:2:"12";s:14:"OnlineServices";s:15:"Online Services";s:2:"OS";s:10:"Windows_NT";s:4:"Path";s:582:"C:\Python313\Scripts\;C:\Python313\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Program Files\PHP\v8.3;C:\ProgramData\ComposerSetup\bin;C:\ProgramData\ComposerSetup\bin\composer.bat;C:\Program Files (x86)\HP\HP OCR\DB_Lib\;C:\Program Files\HP\Common\HPDestPlgIn\;C:\Program Files (x86)\HP\Common\HPDestPlgIn\;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Program Files\GitHub CLI\;C:\WINDOWS\system32\config\systemprofile\AppData\Local\Microsoft\WindowsApps";s:7:"PATHEXT";s:62:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW";s:12:"platformcode";s:2:"AN";s:22:"PROCESSOR_ARCHITECTURE";s:5:"AMD64";s:20:"PROCESSOR_IDENTIFIER";s:51:"Intel64 Family 6 Model 186 Stepping 3, GenuineIntel";s:15:"PROCESSOR_LEVEL";s:1:"6";s:18:"PROCESSOR_REVISION";s:4:"ba03";s:11:"ProgramData";s:14:"C:\ProgramData";s:12:"ProgramFiles";s:16:"C:\Program Files";s:17:"ProgramFiles(x86)";s:22:"C:\Program Files (x86)";s:12:"ProgramW6432";s:16:"C:\Program Files";s:12:"PSModulePath";s:93:"C:\Program Files\WindowsPowerShell\Modules;C:\WINDOWS\system32\WindowsPowerShell\v1.0\Modules";s:6:"PUBLIC";s:15:"C:\Users\<USER>\WINDOWS";s:4:"TEMP";s:15:"C:\WINDOWS\TEMP";s:3:"TMP";s:15:"C:\WINDOWS\TEMP";s:10:"USERDOMAIN";s:3:"GOV";s:8:"USERNAME";s:11:"EGOV-L-046$";s:11:"USERPROFILE";s:40:"C:\WINDOWS\system32\config\systemprofile";s:6:"windir";s:10:"C:\WINDOWS";s:17:"ZES_ENABLE_SYSMAN";s:1:"1";s:14:"ORIG_PATH_INFO";s:21:"/backoffice/index.php";s:3:"URL";s:21:"/backoffice/index.php";s:15:"SERVER_SOFTWARE";s:18:"Microsoft-IIS/10.0";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:18:"SERVER_PORT_SECURE";s:1:"0";s:11:"SERVER_PORT";s:4:"8005";s:11:"SERVER_NAME";s:9:"localhost";s:11:"SCRIPT_NAME";s:21:"/backoffice/index.php";s:15:"SCRIPT_FILENAME";s:41:"C:\Web\Reclassering\backend\web\index.php";s:11:"REQUEST_URI";s:51:"/backoffice/index.php?r=document-type%2Fupdate&id=8";s:14:"REQUEST_METHOD";s:4:"POST";s:11:"REMOTE_USER";s:0:"";s:11:"REMOTE_PORT";s:5:"56339";s:11:"REMOTE_HOST";s:3:"::1";s:11:"REMOTE_ADDR";s:3:"::1";s:12:"QUERY_STRING";s:29:"r=document-type%2Fupdate&id=8";s:15:"PATH_TRANSLATED";s:41:"C:\Web\Reclassering\backend\web\index.php";s:10:"LOGON_USER";s:0:"";s:10:"LOCAL_ADDR";s:3:"::1";s:18:"INSTANCE_META_PATH";s:11:"/LM/W3SVC/2";s:13:"INSTANCE_NAME";s:12:"RECLASSERING";s:11:"INSTANCE_ID";s:1:"2";s:20:"HTTPS_SERVER_SUBJECT";s:0:"";s:19:"HTTPS_SERVER_ISSUER";s:0:"";s:19:"HTTPS_SECRETKEYSIZE";s:0:"";s:13:"HTTPS_KEYSIZE";s:0:"";s:5:"HTTPS";s:3:"off";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:13:"DOCUMENT_ROOT";s:32:"C:\Web\Reclassering\frontend\web";s:12:"CONTENT_TYPE";s:33:"application/x-www-form-urlencoded";s:14:"CONTENT_LENGTH";s:4:"3491";s:12:"CERT_SUBJECT";s:0:"";s:17:"CERT_SERIALNUMBER";s:0:"";s:11:"CERT_ISSUER";s:0:"";s:10:"CERT_FLAGS";s:0:"";s:11:"CERT_COOKIE";s:0:"";s:9:"AUTH_USER";s:0:"";s:13:"AUTH_PASSWORD";s:0:"";s:9:"AUTH_TYPE";s:0:"";s:18:"APPL_PHYSICAL_PATH";s:32:"C:\Web\Reclassering\backend\web\";s:12:"APPL_MD_PATH";s:27:"/LM/W3SVC/2/ROOT/backoffice";s:20:"IIS_UrlRewriteModule";s:13:"7,1,1993,2351";s:19:"HTTP_SEC_FETCH_DEST";s:8:"document";s:19:"HTTP_SEC_FETCH_USER";s:2:"?1";s:19:"HTTP_SEC_FETCH_MODE";s:8:"navigate";s:19:"HTTP_SEC_FETCH_SITE";s:11:"same-origin";s:30:"HTTP_UPGRADE_INSECURE_REQUESTS";s:1:"1";s:11:"HTTP_ORIGIN";s:21:"http://localhost:8005";s:23:"HTTP_SEC_CH_UA_PLATFORM";s:9:""Windows"";s:21:"HTTP_SEC_CH_UA_MOBILE";s:2:"?0";s:14:"HTTP_SEC_CH_UA";s:65:""Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"";s:15:"HTTP_USER_AGENT";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:12:"HTTP_REFERER";s:72:"http://localhost:8005/backoffice/index.php?r=document-type%2Fupdate&id=8";s:9:"HTTP_HOST";s:14:"localhost:8005";s:11:"HTTP_COOKIE";s:957:"_identity-frontend=ea504c4adbb00d93bcc48cc313183654e5c3f219dbab61d7d91fbc33aaa0c4a5a%3A2%3A%7Bi%3A0%3Bs%3A18%3A%22_identity-frontend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; sidebar-collapse=false; _identity-backend=39d1ba9859efd2bc37503bb1ffdf6627160ca396ce3e4675a5f71e3c133c960ba%3A2%3A%7Bi%3A0%3Bs%3A17%3A%22_identity-backend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; advanced-backend-fmz=kg5944qo99nlrpbhm7q5konqli; _csrf-backend=917abb8469aa4d698f81356ac859964e5a8cac9582dcc38b00411aefe9ca5df9a%3A2%3A%7Bi%3A0%3Bs%3A13%3A%22_csrf-backend%22%3Bi%3A1%3Bs%3A32%3A%22TwezWto5toyW6sW07LndJjdZNt6UE7DV%22%3B%7D; advanced-frontend-fmz=moh7rs0lensqa7tdlnifptpb87; _csrf-frontend=dd73e6ec16db6a655690a449fe4e4fa789f76c9c2c95dfa96614b330565202a0a%3A2%3A%7Bi%3A0%3Bs%3A14%3A%22_csrf-frontend%22%3Bi%3A1%3Bs%3A32%3A%22ca_8DpXaaaTHYgxFa4WS3IK6vVsvuynG%22%3B%7D";s:20:"HTTP_ACCEPT_LANGUAGE";s:14:"en-US,en;q=0.9";s:20:"HTTP_ACCEPT_ENCODING";s:23:"gzip, deflate, br, zstd";s:11:"HTTP_ACCEPT";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:17:"HTTP_CONTENT_TYPE";s:33:"application/x-www-form-urlencoded";s:19:"HTTP_CONTENT_LENGTH";s:4:"3491";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:18:"HTTP_CACHE_CONTROL";s:9:"max-age=0";s:9:"FCGI_ROLE";s:9:"RESPONDER";s:8:"PHP_SELF";s:21:"/backoffice/index.php";s:18:"REQUEST_TIME_FLOAT";d:**********.973528;s:12:"REQUEST_TIME";i:**********;}s:3:"GET";a:2:{s:1:"r";s:20:"document-type/update";s:2:"id";s:1:"8";}s:4:"POST";a:2:{s:13:"_csrf-backend";s:88:"1_jIeJSlU5ESdm3Sq8VSS5bEMAo3rTe_AgTbrzFiH6-Dj60Cw9E8pGYZFIWdtgV7oYhebn3HU-VMcO36dFVb-Q==";s:12:"DocumentType";a:3:{s:4:"type";s:20:"VOORLICHTINGSRAPPORT";s:15:"layout_template";s:1926:"<h1 style="text-align: center;"><b>Ministerie van Justitie en Politie</b></h1>

<h3 style="text-align: center;">Onderdirectoraat Forensische Zorg</h3>

<h3 style="text-align: center;">Afdeling: Forensisch Maatschappelijke Zorg</h3>

<h3 style="text-align: center;">Tibitistraat 4 Paramaribo</h3>

<h3 style="text-align: center;">Tel: 427197 toestel 2720 of 2711</h3>

<hr />
<p><strong>Paramaribo, {{ currentDate }}</strong></p>

<p> </p>

<p><strong>Ons kenmerk: F.M.Z No ..... / 20</strong></p>

<p> </p>

<h4><b>VOORLICHTINGSRAPPORT UITGEBRACHT AAN DE HEER/MEVROUW RECHTER IN HET .......... KANTON BETREFFENDE:</b></h4>

<hr />
<p> </p>

<table align="left" border="0" cellpadding="1" cellspacing="0" style="width: 500px;">
	<tbody>
		<tr>
			<td>
			<p>NAAM:</p>
			</td>
			<td>
			<p> {{ data.questions_and_answers[0].answer.text }}</p>
			</td>
		</tr>
		<tr>
			<td>
			<p>VOORNAAM:</p>
			</td>
			<td>
			<p> {{ data.questions_and_answers[1].answer.text }}</p>
			</td>
		</tr>
		<tr>
			<td>
			<p>GEB.DD. / PLAATS:</p>
			</td>
			<td>
			<p> {{ data.questions_and_answers[2].answer.text }}</p>
			</td>
		</tr>
	</tbody>
</table>

<p> </p>

<p>{% for qa in data.questions_and_answers|slice(9) %}</p>

<p style="text-transform: uppercase"><ins><strong>{{ qa.question.text }}</strong></ins></p>

<p>{{ qa.answer.text }}</p>

<p>{% endfor %}</p>

<p> </p>

<p>{% for signature in data.signatures %} {% endfor %}</p>

<table width="100%">
	<tbody>
		<tr>
			<td style="text-align: 
                        {% if loop.index == 1 %}left
                        {% elseif loop.index == 2 %}right
                        {% else %}center
                        {% endif %};"><img alt="Signature" height="150" src="{{ signature.signature }}" width="150" />
			<p>{{ signature.user.username }}</p>
			</td>
		</tr>
	</tbody>
</table>
";s:16:"handmatig_invoer";s:1:"1";}}s:6:"COOKIE";a:7:{s:18:"_identity-frontend";s:158:"ea504c4adbb00d93bcc48cc313183654e5c3f219dbab61d7d91fbc33aaa0c4a5a:2:{i:0;s:18:"_identity-frontend";i:1;s:46:"[1,"R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW",2592000]";}";s:16:"sidebar-collapse";s:5:"false";s:17:"_identity-backend";s:157:"39d1ba9859efd2bc37503bb1ffdf6627160ca396ce3e4675a5f71e3c133c960ba:2:{i:0;s:17:"_identity-backend";i:1;s:46:"[1,"R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW",2592000]";}";s:20:"advanced-backend-fmz";s:26:"kg5944qo99nlrpbhm7q5konqli";s:13:"_csrf-backend";s:139:"917abb8469aa4d698f81356ac859964e5a8cac9582dcc38b00411aefe9ca5df9a:2:{i:0;s:13:"_csrf-backend";i:1;s:32:"TwezWto5toyW6sW07LndJjdZNt6UE7DV";}";s:21:"advanced-frontend-fmz";s:26:"moh7rs0lensqa7tdlnifptpb87";s:14:"_csrf-frontend";s:140:"dd73e6ec16db6a655690a449fe4e4fa789f76c9c2c95dfa96614b330565202a0a:2:{i:0;s:14:"_csrf-frontend";i:1;s:32:"ca_8DpXaaaTHYgxFa4WS3IK6vVsvuynG";}";}s:5:"FILES";a:0:{}s:7:"SESSION";a:3:{s:7:"__flash";a:0:{}s:4:"__id";i:1;s:9:"__authKey";s:32:"R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW";}}";s:4:"user";s:1549:"a:5:{s:2:"id";i:1;s:8:"identity";a:12:{s:2:"id";s:1:"1";s:8:"username";s:11:"'SuperUser'";s:8:"auth_key";s:34:"'R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW'";s:13:"password_hash";s:62:"'$2y$13$uUSLwNI3so2bzilzzZdKJ.C1qmfyTdLRRT1XVP8jYO1c38iE6dfxS'";s:20:"password_reset_token";s:4:"null";s:5:"email";s:17:"'<EMAIL>'";s:6:"status";s:2:"10";s:10:"created_at";s:10:"1741788198";s:10:"updated_at";s:10:"1749661338";s:18:"verification_token";s:45:"'oZxacBEp5N7LZAqXc3s1haihOCLK8dLU_1741788198'";s:7:"role_id";s:1:"1";s:12:"access_token";s:66:"'CjYetJZp6PI5vAvKuqm6TDSC_2kYfe_LtOkrjMVtEo4IseTg3J-r-Gp3gMgH-pr7'";}s:10:"attributes";a:12:{i:0;a:2:{s:9:"attribute";s:2:"id";s:5:"label";s:2:"Id";}i:1;a:2:{s:9:"attribute";s:8:"username";s:5:"label";s:8:"Username";}i:2;a:2:{s:9:"attribute";s:8:"auth_key";s:5:"label";s:8:"Auth Key";}i:3;a:2:{s:9:"attribute";s:13:"password_hash";s:5:"label";s:8:"Password";}i:4;a:2:{s:9:"attribute";s:20:"password_reset_token";s:5:"label";s:20:"Password Reset Token";}i:5;a:2:{s:9:"attribute";s:5:"email";s:5:"label";s:5:"Email";}i:6;a:2:{s:9:"attribute";s:6:"status";s:5:"label";s:6:"Status";}i:7;a:2:{s:9:"attribute";s:10:"created_at";s:5:"label";s:10:"Created At";}i:8;a:2:{s:9:"attribute";s:10:"updated_at";s:5:"label";s:10:"Updated At";}i:9;a:2:{s:9:"attribute";s:18:"verification_token";s:5:"label";s:18:"Verification Token";}i:10;a:2:{s:9:"attribute";s:7:"role_id";s:5:"label";s:4:"Role";}i:11;a:2:{s:9:"attribute";s:12:"access_token";s:5:"label";s:12:"Access Token";}}s:13:"rolesProvider";N;s:19:"permissionsProvider";N;}";s:5:"asset";s:6:"a:0:{}";s:7:"summary";a:13:{s:3:"tag";s:13:"68b19b2b08599";s:3:"url";s:72:"http://localhost:8005/backoffice/index.php?r=document-type%2Fupdate&id=8";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:3:"::1";s:4:"time";d:**********.973528;s:10:"statusCode";i:302;s:8:"sqlCount";i:17;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:8723768;s:14:"processingTime";d:0.12169694900512695;}s:10:"exceptions";a:0:{}}