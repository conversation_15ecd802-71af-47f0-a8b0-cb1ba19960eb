<?php

namespace common\components;

use Yii;
use yii\helpers\ArrayHelper;
use common\models\Notification;
use common\models\NotificationTrigger;
use common\models\NotificationUser;
use common\models\User;

class NotificationManager
{
    public static function trigger($notificationKey, $data = [])
    {
        $notification = Notification::find()
            ->where(['key' => $notificationKey, 'enabled' => true])
            ->with('notificationRoles')
            ->one();

        if (!$notification) {
            return;
        }

        $message = $notification->message_template;
        foreach ($data as $key => $value) {
            $message = str_replace("{{$key}}", $value, $message);
        }

        // Get role IDs directly from the notification roles relationship
        $roleIds = ArrayHelper::getColumn($notification->notificationRoles, 'role_id');
        if (empty($roleIds)) {
            Yii::warning("No roles assigned to notification: {$notificationKey}", __METHOD__);
            return;
        }

        $users = User::find()->where(['role_id' => $roleIds])->all();
        if (empty($users)) {
            Yii::warning("No users found for roles: " . implode(', ', $roleIds), __METHOD__);
            return;
        }

        foreach ($users as $user) {
            $userNotif = new NotificationUser([
                'notification_id' => $notification->id,
                'user_id' => $user->id,
                'message' => $message,
            ]);

            if (!$userNotif->save()) {
                Yii::error("Notification failed for user {$user->id}: " . json_encode($userNotif->errors), __METHOD__);
                continue;
            }

            if ($notification->send_email && !empty($user->email)) {
                try {
                    self::sendEmail($user, $notification->title, $message);
                } catch (\Exception $e) {
                    Yii::error("Email sending failed for user {$user->id}: " . $e->getMessage(), __METHOD__);
                }
            }
        }
    }

    public static function triggerByRoute($route, $modelId = null)
    {
        $trigger = NotificationTrigger::find()->where(['route' => $route])->one();
        if (!$trigger) return;

        $modelClass = $trigger->model_class;
        $idParam = $trigger->model_id_param;

        $id = $modelId ??
            Yii::$app->request->getBodyParam($idParam) ??
            Yii::$app->request->getQueryParam($idParam);

        if (!$id || !class_exists($modelClass)) return;

        $model = $modelClass::findOne($id);
        if (!$model) return;

        $data = self::buildMessageData($model, $trigger);
        self::trigger($trigger->notification_key, $data);
    }

    public static function buildMessageData($model, $trigger)
    {
        $fieldMap = json_decode($trigger->fields, true);
        $data = [];

        if (is_array($fieldMap)) {
            foreach ($fieldMap as $placeholder => $attributePath) {
                $data[$placeholder] = self::getAttributeByPath($model, $attributePath);
            }
        }

        $data['username'] = Yii::$app->user->identity->username ?? 'Guest';
        return $data;
    }

    protected static function getAttributeByPath($model, $path)
    {
        $parts = explode('.', $path);
        $value = $model;
        foreach ($parts as $part) {
            if (is_object($value) && isset($value->{$part})) {
                $value = $value->{$part};
            } else {
                return null;
            }
        }
        return $value;
    }

    protected static function sendEmail($user, $subject, $body)
    {
        try {
            $message = Yii::$app->mailer->compose()
                ->setFrom([Yii::$app->params['senderEmail'] => Yii::$app->params['senderName']])
                ->setTo($user->email)
                ->setSubject($subject)
                ->setHtmlBody(self::formatEmailBody($body, $user))
                ->setTextBody(strip_tags($body));

            if ($message->send()) {
                Yii::info("Notification email sent to {$user->email} with subject: {$subject}", __METHOD__);
                return true;
            } else {
                Yii::error("Failed to send notification email to {$user->email}", __METHOD__);
                return false;
            }
        } catch (\Exception $e) {
            Yii::error("Email sending exception for {$user->email}: " . $e->getMessage(), __METHOD__);
            return false;
        }
    }

    protected static function formatEmailBody($body, $user)
    {
        $html = "
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
                .content { padding: 20px; }
                .footer { background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 20px; font-size: 12px; color: #666; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h2>" . Yii::$app->name . " - Notification</h2>
                </div>
                <div class='content'>
                    <p>Hello " . htmlspecialchars($user->username ?? 'User') . ",</p>
                    <p>" . nl2br(htmlspecialchars($body)) . "</p>
                </div>
                <div class='footer'>
                    <p>This is an automated notification from " . Yii::$app->name . ".</p>
                    <p>Please do not reply to this email.</p>
                </div>
            </div>
        </body>
        </html>";

        return $html;
    }
}
