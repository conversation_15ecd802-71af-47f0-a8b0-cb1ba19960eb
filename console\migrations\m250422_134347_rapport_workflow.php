<?php

use yii\db\Migration;

class m250422_134347_rapport_workflow extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('rapport_workflow', [
            'id' => $this->primaryKey(),
            'name' => $this->string(255)->notNull(),
            'description' => $this->string(255),
            'from_role' => $this->integer()->notNull(),
            'to_role' => $this->integer()->notNull(),
            'from_rapport_status_id' => $this->integer()->notNull(),
            'to_rapport_status_id' => $this->integer()->notNull(),
            'created_at' => $this->timestamp()->notNull()->defaultExpression('CURRENT_TIMESTAMP'),
            'updated_at' => $this->timestamp()->notNull()->defaultExpression('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'),
            'created_by' => $this->integer(),
            'updated_by' => $this->integer(),
        ]);

        // Add foreign key for `from_status_id`
        $this->addForeignKey(
            'fk_rapport_workflow_from_status_id',
            'rapport_workflow',
            'from_rapport_status_id',
            'rapport_status', // <-- replace this with the actual status table name
            'id',
            'RESTRICT',
            'CASCADE'
        );

        // Add foreign key for `to_status_id`
        $this->addForeignKey(
            'fk_rapport_workflow_to_status_id',
            'rapport_workflow',
            'to_rapport_status_id',
            'rapport_status', // <-- replace this too with your actual status table
            'id',
            'RESTRICT',
            'CASCADE'
        );

        // Add foreign key for `created_by`
        $this->addForeignKey(
            'fk_rapport_workflow_created_by',
            'rapport_workflow',
            'created_by',
            'user',
            'id',
            'SET NULL',
            'CASCADE'
        );

        // Add foreign key for `updated_by`
        $this->addForeignKey(
            'fk_rapport_workflow_updated_by',
            'rapport_workflow',
            'updated_by',
            'user',
            'id',
            'SET NULL',
            'CASCADE'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropForeignKey('fk_rapport_workflow_from_status_id', 'rapport_workflow');
        $this->dropForeignKey('fk_rapport_workflow_to_status_id', 'rapport_workflow');
        $this->dropForeignKey('fk_rapport_workflow_created_by', 'rapport_workflow');
        $this->dropForeignKey('fk_rapport_workflow_updated_by', 'rapport_workflow');
        $this->dropTable('rapport_workflow');
    }
}
