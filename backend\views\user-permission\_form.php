<?php

use common\models\User;
use kartik\select2\Select2;
use kartik\switchinput\SwitchInput;
use yii\helpers\Html;
use yii\bootstrap5\ActiveForm;
use yii\helpers\ArrayHelper;
use yii\helpers\Url;
use yii\web\JsExpression;

/* @var $this yii\web\View */
/* @var $model common\models\UserPermission */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="user-permission-form">

    <?php $form = ActiveForm::begin(); ?>

    <?= $form->field($model, 'user_id')->widget(Select2::className(), [
        'data' => ArrayHelper::map(User::find()->all(), 'id', 'username'),
        'options' => [
            'placeholder' => 'Select a user...',
        ],
        'pluginOptions' => [
            'allowClear' => true,
            'dropdownParent' => '#ajaxCrudModal'
        ]
    ]); ?>

    <?= $form->field($model, 'route')->widget(Select2::class, [
        'options' => ['placeholder' => 'Select route...'],
        'pluginOptions' => [
            'allowClear' => true,
            'multiple' => true,
            'ajax' => [
                'url' => Url::to(['user-permission/get-routes']),
                'dataType' => 'json',
                'data' => new JsExpression('function(params) { return {q:params.term}; }')
            ],
            'dropdownParent' => '#ajaxCrudModal'
        ],
    ]) ?>

    <?= $form->field($model, 'can_access')->widget(SwitchInput::classname(), [
        'type' => SwitchInput::CHECKBOX,
    ]) ?>



    <?php if (!Yii::$app->request->isAjax) { ?>
        <div class="form-group">
            <?= Html::submitButton($model->isNewRecord ? 'Create' : 'Update', ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']) ?>
        </div>
    <?php } ?>

    <?php ActiveForm::end(); ?>

</div>