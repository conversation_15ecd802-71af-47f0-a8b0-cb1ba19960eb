<?php

use yii\helpers\Html;
use kartik\select2\Select2;
use yii\helpers\ArrayHelper;
use common\models\DocumentType;
use common\models\DocumenttypeVraag;
use common\models\Vraag;
use kartik\form\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\DocumenttypeVraag */
/* @var $form yii\widgets\ActiveForm */

// Prepare data for dropdowns
$documents = ArrayHelper::map(DocumentType::find()->all(), 'id', 'type');
$questions = Vraag::find()->all();

// Get existing questions and their order if editing
$existingQuestions = [];
$existingOrders = [];
if (!$model->isNewRecord) {
    $existingQuestions = ArrayHelper::map(
        DocumenttypeVraag::find()
            ->where(['document_id' => $model->document_id])
            ->all(),
        'vraag_id',
        'vraag_id'
    );
    $existingOrders = ArrayHelper::map(
        DocumenttypeVraag::find()
            ->where(['document_id' => $model->document_id])
            ->all(),
        'vraag_id',
        'vraag_nummer'
    );
}
?>

<div class="documentnaam-vraag-form">
    <?php $form = ActiveForm::begin(); ?>

    <?= $form->field($model, 'document_id')->widget(Select2::classname(), [
        'data' => $documents,
        'options' => [
            'placeholder' => 'Select a document...',
        ],
        'pluginOptions' => [
            'allowClear' => true,
            'dropdownParent' => '#ajaxCrudModal'
        ]
    ]); ?>

    <div class="card mt-3">
        <div class="card-header">
            <h5 class="mb-0">Selecteer de vragen voor dit document</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th style="width: 80px">Select</th>
                            <th>Vraag</th>
                            <th style="width: 120px">Order</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($questions as $question): ?>
                            <tr>
                                <td>
                                    <div class="form-check">
                                        <input type="checkbox"
                                            class="form-check-input"
                                            name="Questions[]"
                                            value="<?= $question->id ?>"
                                            id="question_<?= $question->id ?>"
                                            <?= isset($existingQuestions[$question->id]) ? 'checked' : '' ?>>
                                    </div>
                                </td>
                                <td>
                                    <label class="form-check-label" for="question_<?= $question->id ?>">
                                        <?= Html::encode($question->vraag) ?>
                                    </label>
                                </td>
                                <td>
                                    <input type="number"
                                        class="form-control form-control-sm"
                                        name="QuestionsOrder[<?= $question->id ?>]"
                                        value="<?= isset($existingOrders[$question->id]) ? $existingOrders[$question->id] : '' ?>"
                                        min="1">
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <?php if (!Yii::$app->request->isAjax) { ?>
        <div class="form-group mt-3">
            <?= Html::submitButton($model->isNewRecord ? 'Create' : 'Update', ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']) ?>
        </div>
    <?php } ?>

    <?php ActiveForm::end(); ?>
</div>

<?php
$script = <<<JS
$(document).ready(function() {
    let selectedQuestions = new Set();
    
    // Initialize selected questions on page load
    $('.form-check-input:checked').each(function() {
        selectedQuestions.add($(this).val());
        updateOrder($(this).val(), true);
    });

    // Handle checkbox changes
    $('.form-check-input').on('change', function() {
        const questionId = $(this).val();
        const orderInput = $(this).closest('tr').find('input[type="number"]');
        
        if (this.checked) {
            selectedQuestions.add(questionId);
            updateOrder(questionId, true);
        } else {
            selectedQuestions.delete(questionId);
            orderInput.val('');
            reorderAll();
        }
    });

    function updateOrder(questionId, isNew = false) {
        const orderInput = $('input[name="QuestionsOrder[' + questionId + ']"]');
        if (isNew) {
            orderInput.val(selectedQuestions.size);
        }
    }

    function reorderAll() {
        let order = 1;
        $('.form-check-input:checked').each(function() {
            const questionId = $(this).val();
            $('input[name="QuestionsOrder[' + questionId + ']"]').val(order);
            order++;
        });
    }
});
JS;
$this->registerJs($script);
?>