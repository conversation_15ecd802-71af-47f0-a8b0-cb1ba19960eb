<?php

use yii\helpers\Html;
use yii\helpers\Url;

$location = Yii::$app->id === 'app-frontend' ? 'frontend' : 'backend';

?>

<li class="nav-item dropdown" id="<?= $id ?>">
    <a class="nav-link" data-toggle="dropdown" href="#">
        <i data-lucide="bell-dot" style="width: 18px; height: 18px;"></i>
        <?php if ($unreadCount > 0): ?>
            <span class="badge badge-warning navbar-badge notifications-counter text-white"><?= $unreadCount ?></span>
        <?php endif; ?>
    </a>
    <div class="dropdown-menu dropdown-menu-lg dropdown-menu-right notifications-dropdown">
        <span class="dropdown-header"><?= $unreadCount ?> Notifications</span>
        <div class="notifications-list">
            <?php if (!empty($userNotifications)): ?>
                <?php foreach ($userNotifications as $userNotification): ?>
                    <?php
                    try {
                        $notification = $userNotification->notification;
                        if (!$notification) {
                            continue;
                        }
                    } catch (\Throwable $e) {
                        continue;
                    }
                    ?>
                    <div class="dropdown-divider"></div>
                    <a href="<?= Url::to([$location === 'backend' ? '/document/comments' : '/document/view', 'id' => $notification->document_id]) ?>"
                        class="dropdown-item notification-item <?= !$userNotification->is_read ? 'unread' : '' ?>"
                        data-notification-id="<?= $userNotification->id ?>">
                        <div class="d-flex align-items-start">
                            <div class="notification-icon me-3">
                                <i data-lucide="circle-alert"
                                    style="width: 18px; height: 18px;"></i>
                            </div>
                            <div class="flex-grow-1 overflow-hidden">
                                <h6 class="dropdown-item-title mb-1">
                                    <?= Html::encode($notification->title) ?>
                                </h6>
                                <p class="text-sm mb-1"><?= Html::encode($notification->message) ?></p>
                                <p class="text-sm text-muted mb-0">
                                    <i data-lucide="clock" style="width: 14px; height: 14px;"></i>
                                    <?= $this->context->getTimeElapsed($notification->created_at) ?>
                                </p>
                            </div>
                        </div>
                    </a>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="dropdown-divider"></div>
                <div class="dropdown-item text-center py-3">
                    <i data-lucide="bell-off" class="text-muted mb-2" style="width: 24px; height: 24px;"></i>
                    <p class="text-muted mb-0">No notifications yet</p>
                </div>
            <?php endif; ?>
        </div>
        <?php if (!empty($userNotifications)): ?>
            <div class="dropdown-divider"></div>
            <a href="<?= Url::to(['/notification/index']) ?>" class="dropdown-item dropdown-footer">
                See All Notifications
            </a>
        <?php endif; ?>
    </div>
</li>

<?php
$css = <<<CSS
    /* Notification dropdown container */
    .notifications-dropdown {
        width: 350px;
        max-width: 90vw;
    }

    /* Notifications list with scrolling */
    .notifications-list {
        max-height: 50vh;
        overflow-y: auto;
        overflow-x: hidden;
        scrollbar-width: thin;
        scrollbar-color: #6c757d #f8f9fa;
    }

    /* Custom scrollbar styling for webkit browsers */
    .notifications-list::-webkit-scrollbar {
        width: 6px;
    }

    .notifications-list::-webkit-scrollbar-track {
        background: #f8f9fa;
        border-radius: 3px;
    }

    .notifications-list::-webkit-scrollbar-thumb {
        background: #6c757d;
        border-radius: 3px;
    }

    .notifications-list::-webkit-scrollbar-thumb:hover {
        background: #495057;
    }

    /* Keep header and footer fixed */
    .dropdown-header {
        position: sticky;
        top: 0;
        background: #fff;
        padding: 0.75rem 1rem;
        border-bottom: 1px solid #dee2e6;
        z-index: 1;
    }

    .dropdown-footer {
        position: sticky;
        bottom: 0;
        background: #fff;
        padding: 0.75rem 1rem;
        border-top: 1px solid #dee2e6;
        text-align: center;
        z-index: 1;
    }

    /* Notification items */
    .notification-item.unread {
        background-color: rgba(0, 0, 0, 0.05);
    }

    .notification-item:hover {
        background-color: rgba(0, 0, 0, 0.075);
    }

    .notification-item {
        white-space: normal;
        word-wrap: break-word;
        padding: 0.75rem 1rem;
    }

    /* Title styling */
    .dropdown-item-title {
        font-size: 0.9rem;
        margin: 0;
        font-weight: 600;
        word-wrap: break-word;
        overflow-wrap: break-word;
    }

    /* Message text */
    .notification-item p {
        word-wrap: break-word;
        overflow-wrap: break-word;
        white-space: normal;
    }

    /* Icon styling */
    .notification-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 35px;
        height: 35px;
        border-radius: 50%;
        background-color: rgba(0, 0, 0, 0.05);
        flex-shrink: 0;
    }

    /* Mark as read button container */
    .ms-2 {
        flex-shrink: 0;
    }
CSS;

$this->registerCss($css);
?>