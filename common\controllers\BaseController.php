<?php

namespace common\controllers;

use Yii;
use yii\web\Controller;
use yii\web\ForbiddenHttpException;
use yii\filters\AccessControl;

class BaseController extends Controller
{
    public function behaviors()
    {
        return [
            'access' => [
                'class' => AccessControl::class,
                'except' => ['login', 'error'], // optionally allow login, error
                'rules' => [
                    [
                        'allow' => true,
                        'roles' => ['@'], // authenticated only
                    ],
                ],
            ],
        ];
    }

    public function beforeAction($action)
    {
        $route = '/' . $this->id . '/' . $action->id;

        if (!Yii::$app->accessHelper->canAccess($route)) {
            throw new ForbiddenHttpException('You are not allowed to access this page.');
        }

        return parent::beforeAction($action);
    }
}
