<?php

use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\DocumentSignature */
?>
<div class="document-signature-view">

    <?= DetailView::widget([
        'model' => $model,
        'attributes' => [
            'id',
            'document_id',
            'user_id',
            [
                'attribute' => 'signature_base64',
                'format' => 'raw',
                'value' => function ($model) {
                    $base64 = \common\components\SignatureHelper::decodeAndDecrypt($model->signature_base64, $model->user_id);
                    if ($base64) {
                        return '<img src="' . $base64 . '" alt="Signature" style="max-width:300px;max-height:120px;border:1px solid #ccc;border-radius:4px;" />';
                    }
                    return '<span class="text-muted">No signature</span>';
                }
            ],
            'created_at',
            'signed_at',
            'updated_at',
            'version',
            'ip_address',
            'user_agent',
        ],
    ]) ?>

</div>