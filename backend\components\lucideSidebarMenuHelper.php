<?php

namespace backend\components;

use Yii;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\helpers\ArrayHelper;

class LucideSidebarMenuHelper extends \hail812\adminlte\widgets\Menu
{
    /**
     * @var string the current route
     */
    protected $currentRoute;

    /**
     * @var string the current URL
     */
    protected $currentUrl;

    /**
     * @inheritdoc
     */
    public $linkTemplate = '<a class="nav-link {active}" href="{url}" {target}>{icon} <p>{label}</p></a>';

    /**
     * @inheritdoc
     */
    public $submenuTemplate = "\n<ul class='nav nav-treeview'>\n{items}\n</ul>\n";

    /**
     * @inheritdoc
     */
    public $parentItemTemplate = '<li class="nav-item has-treeview {active} {open}">
        <a class="nav-link {active}" href="{url}" {target}>
            {icon} <p>{label}<i class="right fas fa-angle-right"></i></p>
        </a>
        {submenu}
    </li>';

    /**
     * @inheritdoc
     */
    public function init()
    {
        parent::init();
        $this->currentRoute = Yii::$app->controller->route;
        $this->currentUrl = Yii::$app->request->url;
    }

    /**
     * @inheritdoc
     */
    protected function isItemActive($item)
    {
        if (isset($item['url'])) {
            $url = Url::to($item['url']);
            if (is_array($item['url'])) {
                $route = $item['url'][0];
                if (strpos($route, '/') !== 0) {
                    $route = '/' . $route;
                }

                // Check if the current route matches this item's route
                if ('/' . $this->currentRoute === $route) {
                    return true;
                }
            }

            // Also check the full URL for an exact match
            if ($url === $this->currentUrl) {
                return true;
            }
        }

        // Check if any child items are active
        if (isset($item['items'])) {
            foreach ($item['items'] as $child) {
                if ($this->isItemActive($child)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * @inheritdoc
     */
    protected function renderItems($items)
    {
        $lines = [];
        foreach ($items as $i => $item) {
            $options = array_merge($this->itemOptions, ArrayHelper::getValue($item, 'options', []));

            // Check if this item or any of its children is active
            $active = $this->isItemActive($item);

            if (isset($item['items'])) {
                // This is a parent item
                $item['submenu'] = $this->renderSubmenu($item, $active);
                $template = $this->parentItemTemplate;

                $replacements = [
                    '{label}' => $item['label'],
                    '{icon}' => empty($item['icon']) ? '' : $this->renderLucideIcon($item),
                    '{url}' => isset($item['url']) ? Url::to($item['url']) : '#',
                    '{active}' => $active ? 'active' : '',
                    '{open}' => $active ? 'menu-open' : '',
                    '{submenu}' => $item['submenu'],
                    '{target}' => isset($item['target']) ? 'target="' . $item['target'] . '"' : ''
                ];
            } else {
                // This is a regular item
                $template = $this->linkTemplate;

                $replacements = [
                    '{label}' => $item['label'],
                    '{icon}' => empty($item['icon']) ? '' : $this->renderLucideIcon($item),
                    '{url}' => isset($item['url']) ? Url::to($item['url']) : '#',
                    '{active}' => $active ? 'active' : '',
                    '{target}' => isset($item['target']) ? 'target="' . $item['target'] . '"' : ''
                ];
            }

            $lines[] = strtr($template, $replacements);
        }

        return implode("\n", $lines);
    }

    /**
     * @inheritdoc
     */
    protected function renderSubmenu($item, $active)
    {
        if (!isset($item['items'])) {
            return '';
        }

        $lines = [];
        foreach ($item['items'] as $child) {
            $childActive = $this->isItemActive($child);

            $template = '<li class="nav-item">
                <a class="nav-link {active}" href="{url}" {target}>
                    {icon} <p>{label}</p>
                </a>
            </li>';

            $lines[] = strtr($template, [
                '{label}' => $child['label'],
                '{icon}' => empty($child['icon']) ? '' : $this->renderLucideIcon($child),
                '{url}' => isset($child['url']) ? Url::to($child['url']) : '#',
                '{active}' => $childActive ? 'active' : '',
                '{target}' => isset($child['target']) ? 'target="' . $child['target'] . '"' : ''
            ]);
        }

        return strtr($this->submenuTemplate, [
            '{items}' => implode("\n", $lines)
        ]);
    }

    /**
     * Renders the Lucide icon
     */
    protected function renderLucideIcon($item)
    {
        if (empty($item['icon'])) {
            return '';
        }

        if (isset($item['iconType']) && $item['iconType'] === 'lucide') {
            return Html::tag('i', '', [
                'data-lucide' => $item['icon'],
                'class' => 'nav-icon lucide-icon-small',
                'style' => 'width: 18px; height: 18px;'
            ]);
        }

        return Html::tag('i', '', ['class' => 'nav-icon ' . $item['icon']]);
    }
}
