<?php

namespace common\widgets;

use yii\base\Widget;
use yii\bootstrap5\ActiveField;
use yii\helpers\Html;

class PasswordInputWidget extends Widget
{
    public $model;
    public $attribute = 'password';
    public $placeholder = 'Password';
    public $maxLength = true;
    public $form;

    public function run()
    {
        return $this->form->field($this->model, $this->attribute, [
            'options' => ['class' => 'form-group position-relative'],
            'inputTemplate' => '
                <div class="position-relative">
                    {input}
                    <button type="button" class="btn position-absolute top-50 translate-middle-y toggle-password" style="background: #fff; border: none; right:4px">
                        <i data-lucide="eye" style="width: 18px; height: 18px"></i>
                    </button>
                </div>
            '
        ])->passwordInput([
            'maxlength' => $this->maxLength,
            'placeholder' => $this->placeholder,
            'class' => 'form-control'
        ])->label(false);
    }
}
