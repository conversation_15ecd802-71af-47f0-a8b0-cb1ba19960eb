<?php

namespace console\controllers;

use Yii;
use yii\console\Controller;

class RouteController extends Controller
{
    public function actionClearCache()
    {
        $cacheKey = 'all-application-routes';

        // Check if cache exists first
        $cached = Yii::$app->cache->get($cacheKey);
        echo "Cache before delete: " . ($cached === false ? 'NOT FOUND' : 'EXISTS (' . count($cached) . ' routes)') . "\n";

        $result = Yii::$app->cache->delete($cacheKey);

        if ($result) {
            echo "Cache cleared successfully.\n";
        } else {
            echo "Cache delete failed or cache not found.\n";
        }

        // Force regeneration to test
        echo "Forcing route regeneration...\n";
        $routeService = Yii::$app->get('routeService');
        $routes = $routeService->getAllRoutes();
        echo "Generated " . count($routes) . " routes:\n";
        foreach (array_slice($routes, 0, 10) as $route) {
            echo "  $route\n";
        }
        if (count($routes) > 10) {
            echo "  ... and " . (count($routes) - 10) . " more\n";
        }

        return 0;
    }
}
