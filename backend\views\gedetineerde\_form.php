<?php
use yii\helpers\Html;
use yii\bootstrap5\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\Gedetineerde */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="gedetineerde-form">

    <?php $form = ActiveForm::begin(); ?>

    <?= $form->field($model, 'regnr')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'naam')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'voornaam')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'idnr')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'verzekeringskaartnr')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'geboortedatum')->textInput() ?>

    <?= $form->field($model, 'created_by')->textInput() ?>

    <?= $form->field($model, 'updated_by')->textInput() ?>

    <?= $form->field($model, 'created_at')->textInput() ?>

    <?= $form->field($model, 'updated_at')->textInput() ?>

  
	<?php if (!Yii::$app->request->isAjax){ ?>
	  	<div class="form-group">
	        <?= Html::submitButton($model->isNewRecord ? 'Create' : 'Update', ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']) ?>
	    </div>
	<?php } ?>

    <?php ActiveForm::end(); ?>
    
</div>
