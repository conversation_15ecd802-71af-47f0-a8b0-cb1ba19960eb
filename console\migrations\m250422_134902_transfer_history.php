<?php

use yii\db\Migration;

class m250422_134902_transfer_history extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('transfer_history', [
            'id' => $this->primaryKey(),
            'rapport_id' => $this->integer()->notNull()->unsigned(),
            'from_status_id' => $this->integer(),
            'to_status_id' => $this->integer(),
            'from_role' => $this->integer(),
            'to_role' => $this->integer(),
            'action_by' => $this->integer(),
            'created_at' => $this->timestamp()->notNull()->defaultExpression('CURRENT_TIMESTAMP'),
            'updated_at' => $this->timestamp()->notNull()->defaultExpression('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'),
            'created_by' => $this->integer(),
            'updated_by' => $this->integer(),
        ]);

        // Add foreign keys
        $this->addForeignKey(
            'fk_transfer_history_rapport',
            'transfer_history',
            'rapport_id',
            'rapport',
            'id',
            'RESTRICT',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk_transfer_history_from_status',
            'transfer_history',
            'from_status_id',
            'rapport_status',
            'id',
            'SET NULL',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk_transfer_history_to_status',
            'transfer_history',
            'to_status_id',
            'rapport_status',
            'id',
            'SET NULL',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk_transfer_history_action_by',
            'transfer_history',
            'action_by',
            'user',
            'id',
            'SET NULL',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk_transfer_history_created_by',
            'transfer_history',
            'created_by',
            'user',
            'id',
            'SET NULL',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk_transfer_history_updated_by',
            'transfer_history',
            'updated_by',
            'user',
            'id',
            'SET NULL',
            'CASCADE'
        );

        // Add index for better performance on history queries
        $this->createIndex(
            'idx_transfer_history_rapport_id',
            'transfer_history',
            'rapport_id'
        );

        $this->createIndex('idx_transfer_history_from_status', 'transfer_history', 'from_status_id');
        $this->createIndex('idx_transfer_history_to_status', 'transfer_history', 'to_status_id');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('transfer_history');
        $this->dropForeignKey('fk_transfer_history_rapport', 'transfer_history');
        $this->dropForeignKey('fk_transfer_history_from_status', 'transfer_history');
        $this->dropForeignKey('fk_transfer_history_to_status', 'transfer_history');
        $this->dropForeignKey('fk_transfer_history_action_by', 'transfer_history');
        $this->dropForeignKey('fk_transfer_history_created_by', 'transfer_history');
        $this->dropForeignKey('fk_transfer_history_updated_by', 'transfer_history');
        $this->dropIndex('idx_transfer_history_rapport_id', 'transfer_history');
    }
}
