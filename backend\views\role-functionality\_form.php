<?php

use yii\helpers\Html;
use yii\bootstrap5\ActiveForm;
use kartik\select2\Select2;
use yii\helpers\ArrayHelper;
use common\models\Role;
use common\models\Functionality;
use kartik\switchinput\SwitchInput;

/* @var $this yii\web\View */
/* @var $model common\models\RoleFunctionality */
/* @var $form yii\widgets\ActiveForm */

// Prepare data for dropdowns
$roles = ArrayHelper::map(Role::find()->all(), 'id', 'name');
$functionalities = ArrayHelper::map(Functionality::find()->all(), 'id', 'name');
?>

<div class="role-functionality-form">

    <?php $form = ActiveForm::begin(); ?>

    <?= $form->field($model, 'role_id')->widget(Select2::classname(), [
        'data' => $roles,
        'options' => [
            'placeholder' => 'Select a role...',
        ],
        'pluginOptions' => [
            'allowClear' => true,
            'dropdownParent' => '#ajaxCrudModal'
        ]
    ]); ?>

    <?= $form->field($model, 'functionaliteit_id')->widget(Select2::classname(), [
        'data' => $functionalities,
        'options' => [
            'placeholder' => 'Select a functionality...',
        ],
        'pluginOptions' => [
            'allowClear' => true,
            'dropdownParent' => '#ajaxCrudModal'
        ]
    ]); ?>

    <?= $form->field($model, 'active')->widget(SwitchInput::classname(), [
        'type' => SwitchInput::CHECKBOX,
    ]) ?>

    <?php if (!Yii::$app->request->isAjax) { ?>
        <div class="form-group">
            <?= Html::submitButton(
                $model->isNewRecord ? 'Create' : 'Update',
                ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']
            ) ?>
        </div>
    <?php } ?>

    <?php ActiveForm::end(); ?>

</div>