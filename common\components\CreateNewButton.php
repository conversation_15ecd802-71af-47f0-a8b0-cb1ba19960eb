<?php

namespace common\components;

use Yii;
use yii\base\Widget;
use yii\helpers\Html;

class CreateNewButton extends Widget
{
    /**
     * @var string The title that appears in the modal and button tooltip
     */
    public $title;

    /**
     * @var string Custom button label (optional)
     */
    public $label;

    /**
     * @var array Additional HTML options for the button
     */
    public $options = [];

    /**
     * @var bool Whether to show the icon
     */
    public $showIcon = true;

    public function init()
    {
        parent::init();

        if ($this->label === null) {
            $this->label = Yii::t('yii2-ajaxcrud', 'New');
        }

        // Default options
        $this->options = array_merge([
            'role' => 'modal-remote',
            'title' => Yii::t('yii2-ajaxcrud', 'Create New') . ' ' . $this->title,
            'class' => 'btn btn-outline-primary d-flex align-items-center gap-2'
        ], $this->options);
    }

    public function run()
    {
        $icon = $this->showIcon
            ? '<i data-lucide="file-plus-2" class="lucide-icon-small" style="width: 18px; height: 18px;"></i> '
            : '';

        return Html::a(
            $icon . $this->label,
            ['create'],
            $this->options
        );
    }
}
