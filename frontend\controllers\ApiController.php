<?php

namespace frontend\controllers;

use common\models\Rapport;
use Yii;
use yii\web\Controller;
use yii\web\Response;
use yii\web\BadRequestHttpException;
use common\models\User;
use yii\filters\auth\HttpBearerAuth;
use yii\filters\ContentNegotiator;

class ApiController extends Controller
{
    public $enableCsrfValidation = false; // Disable CSRF for API POSTs

    public function behaviors()
    {
        $behaviors = parent::behaviors();

        $behaviors['contentNegotiator'] = [
            'class' => ContentNegotiator::class,
            'formats' => [
                'application/json' => Response::FORMAT_JSON,
            ],
        ];

        // Add Bearer token auth (only for specific actions or all)
        $behaviors['authenticator'] = [
            'class' => HttpBearerAuth::class,
            'except' => ['login'], // don't require auth for login
        ];
        return $behaviors;
    }

    public function actionLogin()
    {
        $request = Yii::$app->request;
        $username = $request->post('username');
        $password = $request->post('password');

        $user = User::findByUsername($username); // Create this method if needed (check status)

        if (!$user || !$user->validatePassword($password)) {
            throw new BadRequestHttpException('Invalid username or password.');
        }

        // Generate new access_token if needed
        if (empty($user->access_token)) {
            $user->access_token = Yii::$app->security->generateRandomString(64);
            $user->save(false);
        }

        return [
            'access_token' => $user->access_token,
            'user' => $user->username,
        ];
    }

    public function actionRapport()
    {
        // Fetch rapport records (filter by user if needed)
        $rapporten = Rapport::find()
            ->with('documentNaam', 'rapportAntwoords') // eager-load relation
            ->all();

        return array_map(function ($rapport) {
            return [
                'id' => $rapport->id,
                'rapport' => $rapport->documentNaam->naam,
                // add other rapport fields you need
                // 'antwoords' => array_map(function ($antwoord) {
                //     return [
                //         'id' => $antwoord->id,
                //         'vraag' => $antwoord->vraag,
                //         'antwoord' => $antwoord->antwoord,
                //         // add any other fields from antwoord
                //     ];
                // }, $rapport->antwoords),
            ];
        }, $rapporten);
    }
}
