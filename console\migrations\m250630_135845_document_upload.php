<?php

use yii\db\Migration;

class m250630_135845_document_upload extends Migration
{
    public function safeUp()
    {
        $this->createTable('{{%document_upload}}', [
            'id' => $this->primaryKey(),
            'user_id' => $this->integer()->notNull(),
            'filename' => $this->string()->notNull(),
            'original_filename' => $this->string()->notNull(),
            'file_path' => $this->string()->notNull(),
            'file_type' => $this->string(50),
            'file_size' => $this->integer(),
            'created_at' => $this->integer()->notNull(),
            'updated_at' => $this->integer()->notNull(),
        ]);

        $this->addForeignKey(
            'fk-document-upload-user_id',
            '{{%document_upload}}',
            'user_id',
            '{{%user}}',
            'id',
            'CASCADE',
            'CASCADE'
        );
    }

    public function safeDown()
    {
        $this->dropForeignKey('fk-document-upload-user_id', '{{%document_upload}}');
        $this->dropTable('{{%document_upload}}');
    }
}
