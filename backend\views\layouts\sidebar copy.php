<?php

use common\models\MenuItem;

$location = 'backend';

$rawMenuItems = MenuItem::find()
    ->where(['visible' => 1, 'location' => $location])
    ->orderBy(['sort_order' => SORT_ASC])
    ->asArray()
    ->all();

$menuItems = buildMenuTree($rawMenuItems);

// Only show these if the user is superUser
// if (Yii::$app->user->identity->role_id === 1) {
//     $superUserMenu = [
//         [
//             'label' => 'Ontwikkelaar Tools',
//             'header' => true,
//         ],
//         [
//             'label' => 'Gii',
//             'url' => ['/gii'],
//             'icon' => 'hammer',
//             'iconType' => 'lucide',
//             'target' => '_blank',
//         ],
//         [
//             'label' => 'Sidebar',
//             'url' => ['/menu-item'],
//             'icon' => 'link-2',
//             'iconType' => 'lucide',
//         ]
//     ];

//     $menuItems = array_merge($menuItems, $superUserMenu);
// }

?>
<aside class="main-sidebar bg-white elevation-4">
    <!-- Brand Logo -->
    <a href="/backoffice" class="brand-link">
        <img src="<?= $assetDir ?>/img/AdminLTELogo.png" alt="AdminLTE Logo" class="brand-image img-circle elevation-3" style="opacity: .8">
        <span class="brand-text font-weight-light"><?= Yii::$app->name ?></span>
    </a>

    <!-- Sidebar -->
    <div class="sidebar">
        <!-- Sidebar Menu -->
        <nav class="mt-4">
            <?php
            if (isAdminOrSuperAdmin()) {
                echo \backend\components\LucideSidebarMenuHelper::widget([
                    'items' => $menuItems
                ]);
            } else {
                // Navbar non superUser
                echo \common\widgets\SidebarWidget::widget();
            }
            ?>
        </nav>
    </div>
</aside>