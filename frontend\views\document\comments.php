<?php

use yii\widgets\DetailView;
use yii\helpers\Html;
use yii\bootstrap5\ActiveForm;
use yii\helpers\Url;
use kartik\mpdf\Pdf;

/* @var $this yii\web\View */
/* @var $model common\models\Rapport */
/* @var $rapportData array */

// Get the PDF URL
$pdfUrl = Url::to(['rapport/pdf', 'id' => $model->id]);
// Get current URL for redirect
$currentUrl = Url::current();
?>

<div class="rapport-view row">
    <!-- Left side - PDF Preview -->
    <div class="col-md-6">
        <div class="card-body">
            <!-- PDF Preview in iframe -->
            <iframe
                src="<?= $pdfUrl ?>"
                width="100%"
                height="800px"
                style="border: none;"
                title="Rapport PDF Preview">
            </iframe>
        </div>
    </div>

    <!-- Right side - Feedback Form -->
    <div class="col-md-6">
        <div class="card">
            <!-- <div class="card-header">
                <h5 class="card-title mb-0">Add Feedback</h5>
            </div> -->
            <div class="card-body">
                <?php $form = ActiveForm::begin([
                    'id' => 'feedback-form',
                    'action' => ['/rapport-feedback/create'],
                    'enableAjaxValidation' => false,
                ]); ?>

                <?= Html::activeHiddenInput($feedbackModel, 'rapport_id', ['value' => $model->id]) ?>
                <?= Html::hiddenInput('redirect_url', $currentUrl, ['id' => 'redirect-url']) ?>

                <?= $form->field($feedbackModel, 'comment')->textarea([
                    'rows' => 6,
                    'placeholder' => 'Enter your feedback here...',
                ]) ?>

                <div class="form-group">
                    <?= Html::submitButton('Save', [
                        'class' => 'btn btn-primary',
                        'id' => 'submit-feedback'
                    ]) ?>
                </div>

                <?php ActiveForm::end(); ?>

                <!-- Display Existing Feedback -->
                <?php if (!empty($model->rapportFeedbacks)): ?>
                    <h5 class="mt-4">Previous Feedback</h5>
                    <div class="previous-feedback" style="max-height: 400px; overflow-y: auto; padding-right: 10px;">
                        <?php foreach ($model->rapportFeedbacks as $feedback): ?>
                            <div class="card mb-3">
                                <div class="card-body">
                                    <h6 class="card-subtitle mb-2 text-muted">
                                        By <?= Html::encode($feedback->createdBy->username) ?>
                                        on <?= Yii::$app->formatter->asDatetime($feedback->created_at) ?>
                                    </h6>
                                    <p class="card-text"><?= Html::encode($feedback->comment) ?></p>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php
$script = <<<JS
$('#feedback-form').on('beforeSubmit', function(e) {
    e.preventDefault();
    
    $.ajax({
        url: $(this).attr('action'),
        type: 'POST',
        data: $(this).serialize() + '&redirect_url=' + $('#redirect-url').val(),
        success: function(response) {
            if (response.success) {
                // Redirect to the same page
                window.location.href = $('#redirect-url').val();
            } else {
                alert('Error saving feedback: ' + (response.content || 'Unknown error'));
            }
        },
        error: function(xhr, status, error) {
            alert('Error submitting feedback: ' + error);
        }
    });
    
    return false;
});

// Add custom scrollbar styling
document.querySelector('.previous-feedback').style.scrollbarWidth = 'thin';
document.querySelector('.previous-feedback').style.scrollbarColor = '#6c757d #f8f9fa';
JS;
$this->registerJs($script);

// Add custom CSS
$css = <<<CSS
.previous-feedback::-webkit-scrollbar {
    width: 6px;
}

.previous-feedback::-webkit-scrollbar-track {
    background: #f8f9fa;
    border-radius: 3px;
}

.previous-feedback::-webkit-scrollbar-thumb {
    background: #6c757d;
    border-radius: 3px;
}

.previous-feedback::-webkit-scrollbar-thumb:hover {
    background: #495057;
}
CSS;
$this->registerCss($css);
?>