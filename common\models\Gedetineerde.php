<?php

namespace common\models;

use Yii;
use yii\behaviors\BlameableBehavior;

/**
 * This is the model class for table "gedetineerde".
 *
 * @property int $id
 * @property int $source_id
 * @property string|null $regnr
 * @property string $naam
 * @property string $voornaam
 * @property string|null $idnr
 * @property string|null $verzekeringskaartnr
 * @property string $geboortedatum
 * @property int $created_by
 * @property int $updated_by
 * @property string|null $created_at
 * @property string|null $updated_at
 *
 * @property User $createdBy
 * @property User $updatedBy
 */
class Gedetineerde extends \yii\db\ActiveRecord
{

    public function behaviors()
    {
        return [
            [
                'class' => BlameableBehavior::class,
                'createdByAttribute' => 'created_by',
                'updatedByAttribute' => 'updated_by',
                'value' => function () {
                    return Yii::$app->user->id;
                }
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'gedetineerde';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['regnr', 'idnr', 'verzekeringskaartnr'], 'default', 'value' => null],
            [['naam', 'voornaam', 'source_id'], 'required'],
            [['geboortedatum', 'created_at', 'updated_at'], 'safe'],
            [['created_by', 'updated_by', 'source_id'], 'integer'],
            [['regnr', 'naam', 'voornaam', 'idnr', 'verzekeringskaartnr'], 'string', 'max' => 255],
            [['source_id'], 'unique', 'message' => 'Deze gedetineerde is al geïmporteerd.'],
            [['created_by'], 'exist', 'skipOnError' => true, 'targetClass' => User::class, 'targetAttribute' => ['created_by' => 'id']],
            [['updated_by'], 'exist', 'skipOnError' => true, 'targetClass' => User::class, 'targetAttribute' => ['updated_by' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'source_id' => 'Bron ID',
            'regnr' => 'Registratie Nummer',
            'naam' => 'Achternaam',
            'voornaam' => 'Voornaam',
            'idnr' => 'Identificatie Nummer',
            'verzekeringskaartnr' => 'Verzekeringskaartnummer',
            'geboortedatum' => 'Geboortedatum',
            'created_by' => 'Aangemaakt Door',
            'updated_by' => 'Bijgewerkt Door',
            'created_at' => 'Aangemaakt Op',
            'updated_at' => 'Bijgewerkt Op',
        ];
    }

    /**
     * Gets query for [[CreatedBy]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getCreatedBy()
    {
        return $this->hasOne(User::class, ['id' => 'created_by']);
    }

    /**
     * Gets query for [[UpdatedBy]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUpdatedBy()
    {
        return $this->hasOne(User::class, ['id' => 'updated_by']);
    }

    /**
     * Gets query for [[UpdatedBy]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getDocuments()
    {
        return $this->hasMany(Document::class, ['id' => 'id']);
    }
}
