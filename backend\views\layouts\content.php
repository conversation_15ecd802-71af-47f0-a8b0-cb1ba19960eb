<?php
/* @var $content string */

use yii\bootstrap5\Breadcrumbs;
?>
<div class="content-wrapper px-2">
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <?php
                    echo Breadcrumbs::widget([
                        'links' => isset($this->params['breadcrumbs']) ? $this->params['breadcrumbs'] : [],
                        'homeLink' => [
                            'label' => '<i data-lucide="house" style="width:18px; height:18px"></i>', // Lucide home icon
                            'url' => Yii::$app->homeUrl,
                            'encode' => false, // Important to render HTML
                        ],
                        'options' => [
                            'class' => 'breadcrumb float-sm-left'
                        ]
                    ]);
                    ?>
                </div><!-- /.col -->
                <div class="col-sm-6">
                    <!-- <h1 class="m-0">
                        <?php
                        if (!is_null($this->title)) {
                            echo \yii\helpers\Html::encode($this->title);
                        } else {
                            echo \yii\helpers\Inflector::camelize($this->context->id);
                        }
                        ?>
                    </h1> -->
                </div><!-- /.col -->
            </div><!-- /.row -->
        </div><!-- /.container-fluid -->
    </div>
    <!-- /.content-header -->

    <!-- Main content -->
    <div class="content pb-3">
        <div class="card rounded-3">
            <div class="card-body rounded-3">
                <?= $content ?><!-- /.container-fluid -->
            </div>
        </div>
    </div>
    <!-- /.content -->
</div>