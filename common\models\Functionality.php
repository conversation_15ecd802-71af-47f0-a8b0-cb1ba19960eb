<?php

namespace common\models;

use Yii;
use yii\db\Expression;
use yii\behaviors\TimestampBehavior;
use yii\behaviors\BlameableBehavior;

/**
 * This is the model class for table "functionality".
 *
 * @property int $id
 * @property string $name
 * @property string $type
 * @property string $parameters
 * @property string $controller
 * @property string|null $description
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property string|null $created_at
 * @property string|null $updated_at
 *
 * @property User $createdBy
 * @property User $updatedBy
 */
class Functionality extends \yii\db\ActiveRecord
{
    private $_originalName;

    public function init()
    {
        parent::init();
        $this->_originalName = $this->name;
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }

        // Add "/" prefix to parameters if it doesn't already have one
        if ($this->parameters && !str_starts_with($this->parameters, '/')) {
            $this->parameters = '/' . $this->parameters;
        }

        // Automatically set the name as "Controller - Type" (both capitalized)
        if (!empty($this->controller) && !empty($this->type)) {
            $controller = ucwords(str_replace(['-', '_'], ' ', $this->controller));
            $this->name = $controller . ' - ' . $this->type;
        }


        return true;
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'functionality';
    }

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'createdAtAttribute' => 'created_at',
                'updatedAtAttribute' => 'updated_at',
                'value' => new Expression('CURRENT_TIMESTAMP'),
            ],
            [
                'class' => BlameableBehavior::class,
                'createdByAttribute' => 'created_by',
                'updatedByAttribute' => 'updated_by',
                'value' => function () {
                    return Yii::$app->user->id;
                }
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['description', 'created_by', 'updated_by'], 'default', 'value' => null],
            [['controller', 'type'], 'required'],
            [['description', 'parameters', 'controller'], 'string'],
            [['created_by', 'updated_by'], 'integer'],
            [['created_at', 'updated_at'], 'safe'],
            [['name', 'type', 'controller'], 'string', 'max' => 100],
            [['created_by'], 'exist', 'skipOnError' => true, 'targetClass' => User::class, 'targetAttribute' => ['created_by' => 'id']],
            [['updated_by'], 'exist', 'skipOnError' => true, 'targetClass' => User::class, 'targetAttribute' => ['updated_by' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'name' => 'Name',
            'type' => 'Type',
            'parameters' => 'Parameter',
            'controller' => 'Controller',
            'description' => 'Description',
            'created_by' => 'Created By',
            'updated_by' => 'Updated By',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Gets query for [[CreatedBy]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getCreatedBy()
    {
        return $this->hasOne(User::class, ['id' => 'created_by']);
    }

    /**
     * Gets query for [[UpdatedBy]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUpdatedBy()
    {
        return $this->hasOne(User::class, ['id' => 'updated_by']);
    }

    /**
     * Generates a controller route from the functionality name or parameters
     * If parameters exists, use that directly (it should already have the leading /)
     * Otherwise, generate from name: "Rapporten - Create" becomes "/rapporten"
     * @return string
     */
    public function getControllerRoute()
    {
        // If parameters exists and is not empty, use it
        if (!empty($this->parameters)) {
            // Ensure parameters starts with /
            return str_starts_with($this->parameters, '/') ? $this->parameters : '/' . $this->parameters;
        }

        // Fallback to generating route from name
        // Split the string by "-" and trim whitespace
        $parts = explode('-', $this->name);
        // Take only the first part (controller name) and trim it
        $controllerName = trim($parts[0]);
        // Convert to lowercase and replace spaces with dashes
        $route = '/' . strtolower(str_replace(' ', '-', $controllerName));

        return $route;
    }
}
