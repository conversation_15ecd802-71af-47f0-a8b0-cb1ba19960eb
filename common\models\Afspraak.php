<?php

namespace common\models;

use Yii;
use yii\behaviors\BlameableBehavior;

/**
 * This is the model class for table "afspraak".
 *
 * @property int $id
 * @property string $title
 * @property string|null $description
 * @property string $date
 * @property string|null $start_time
 * @property string|null $end_time
 * @property string|null $color
 * @property string|null $created_at
 * @property int|null $created_by
 *
 * @property User $createdBy
 */
class Afspraak extends \yii\db\ActiveRecord
{


    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'afspraak';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['description', 'start_time', 'end_time', 'created_by'], 'default', 'value' => null],
            // [['color'], 'default', 'value' => '#0d6efd'],
            [['title', 'date'], 'required'],
            [['description'], 'string'],
            [['date', 'start_time', 'end_time', 'created_at'], 'safe'],
            [['created_by'], 'integer'],
            [['title'], 'string', 'max' => 255],
            [['color'], 'string', 'max' => 45],
            [['start_time'], 'validateTimeSlotOverlap'],
            [['created_by'], 'exist', 'skipOnError' => true, 'targetClass' => User::class, 'targetAttribute' => ['created_by' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'title' => 'Title',
            'description' => 'Description',
            'date' => 'Date',
            'start_time' => 'Start Time',
            'end_time' => 'End Time',
            'color' => 'Color',
            'created_at' => 'Created At',
            'created_by' => 'Created By',
        ];
    }

    /**
     * Gets query for [[CreatedBy]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getCreatedBy()
    {
        return $this->hasOne(User::class, ['id' => 'created_by']);
    }

    public function beforeSave($insert)
    {
        if (parent::beforeSave($insert)) {
            if ($insert && empty($this->color)) {
                $this->color = sprintf('#%06X', mt_rand(0, 0xFFFFFF));
            }
            return true;
        }
        return false;
    }

    public function beforeValidate()
    {
        if (parent::beforeValidate()) {
            if ($this->start_time && empty($this->end_time)) {
                $start = \DateTime::createFromFormat('H:i:s', $this->start_time);
                if ($start) {
                    $start->modify('+30 minutes');
                    $this->end_time = $start->format('H:i:s');
                }
            }
            return true;
        }
        return false;
    }

    public function behaviors()
    {
        return [
            [
                'class' => BlameableBehavior::class,
                'createdByAttribute' => 'created_by',
                'updatedByAttribute' => false
            ],
        ];
    }

    public function validateTimeSlotOverlap($attribute, $params)
    {
        $query = self::find()
            ->where(['date' => $this->date])
            ->andWhere([
                'or',
                [
                    'and',
                    ['<=', 'start_time', $this->start_time],
                    ['>', 'end_time', $this->start_time],
                ],
                [
                    'and',
                    ['<', 'start_time', $this->end_time],
                    ['>=', 'end_time', $this->end_time],
                ],
                [
                    'and',
                    ['>=', 'start_time', $this->start_time],
                    ['<=', 'end_time', $this->end_time],
                ],
            ]);

        if (!$this->isNewRecord) {
            $query->andWhere(['<>', 'id', $this->id]); // exclude self
        }

        if ($query->exists()) {
            $this->addError($attribute, 'This time slot overlaps with an existing booking.');
        }
    }
}
