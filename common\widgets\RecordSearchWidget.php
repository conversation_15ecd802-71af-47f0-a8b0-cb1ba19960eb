<?php

namespace common\widgets;

use Yii;
use yii\base\Widget;
use yii\helpers\Html;

class RecordSearchWidget extends Widget
{
    public $id = 'search-record';
    public $placeholder = 'Search...';
    public $width = 'w-75';
    public $paramName = 'globalSearch';

    public function run()
    {
        // Extract model part dynamically from id (e.g., document-naam-search → DocumentNaamSearch)
        $idParts = explode('-', $this->id);
        array_pop($idParts); // Remove 'search' from the end
        $modelPrefix = implode('', array_map('ucfirst', $idParts)) . 'Search';

        $searchParam = $modelPrefix . '[' . $this->paramName . ']';

        // Fetch current value
        $currentValue = Yii::$app->request->get($modelPrefix)[$this->paramName] ?? '';

        return '<div class="' . $this->width . '">
            <div class="input-group" 
                 data-param-name="' . Html::encode($searchParam) . '" 
                 data-search-id="' . $this->id . '"
                 data-model-prefix="' . $modelPrefix . '">
                 
                <span class="input-group-text bg-white">
                    <i data-lucide="search" style="width: 18px; height: 18px;"></i>
                </span>
                
                <input type="search" 
                       id="' . $this->id . '" 
                       class="form-control record-search-input"
                       placeholder="' . Html::encode($this->placeholder) . '" 
                       value="' . Html::encode($currentValue) . '" 
                       data-param-name="' . Html::encode($searchParam) . '"
                       data-model-prefix="' . $modelPrefix . '">
            </div>
        </div>';
    }
}
