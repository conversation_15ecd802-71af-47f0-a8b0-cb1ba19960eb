version: '3.2'

services:

  frontend:
    build: frontend
    ports:
      - 20080:80
    volumes:
      # Re-use local composer cache via host-volume
      - ~/.composer-docker/cache:/root/.composer/cache:delegated
      # Mount source-code for development
      - ./:/app

  backend:
    build: backend
    ports:
      - 21080:80
    volumes:
      # Re-use local composer cache via host-volume
      - ~/.composer-docker/cache:/root/.composer/cache:delegated
      # Mount source-code for development
      - ./:/app

  mysql:
    image: mysql:5.7
    environment:
      - MYSQL_ROOT_PASSWORD=verysecret
      - MYSQL_DATABASE=yii2advanced
      - MYSQL_USER=yii2advanced
      - MYSQL_PASSWORD=secret

  #pgsql:
  #  image: postgres:9.5
  #  environment:
  #    - POSTGRES_DB=yii2advanced
  #    - POSTGRES_USER=yii2advanced
  #    - POSTGRES_PASSWORD=secret