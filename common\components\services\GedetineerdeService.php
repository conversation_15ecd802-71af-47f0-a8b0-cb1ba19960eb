<?php

namespace common\components\services;

use common\components\BaseApiService;

class GedetineerdeService extends BaseApiService
{
    public function __construct()
    {
        parent::__construct(
            \Yii::$app->params['apiBaseUrl'],
            \Yii::$app->params['gedetineerde.token']
        );
    }

    public function getGedetineerde()
    {
        try {
            $result = $this->get('gedetineerde');
            if (!$result) {
                \Yii::warning("Failed to fetch gedetineerde data from API", __METHOD__);
                return ['data' => []];
            }
            return $result;
        } catch (\Exception $e) {
            \Yii::error("Error fetching gedetineerde data: " . $e->getMessage(), __METHOD__);
            return ['data' => []];
        }
    }

    public function getOneGedetineerde($id)
    {
        try {
            if (empty($id)) {
                \Yii::warning("Empty ID provided to getOneGedetineerde", __METHOD__);
                return null;
            }

            $result = $this->get('gedetineerde?persoonid=' . urlencode($id))['data'][0];
            if (!$result) {
                \Yii::warning("Gedetineerde with ID {$id} not found in API", __METHOD__);
                return null;
            }
            return $result;
        } catch (\Exception $e) {
            \Yii::error("Error fetching gedetineerde with ID {$id}: " . $e->getMessage(), __METHOD__);
            return null;
        }
    }

    public function search($search)
    {
        try {
            $result = $this->get('gedetineerde?s=' . urlencode($search));
            if (!$result) {
                \Yii::warning("Failed to fetch gedetineerde data from API", __METHOD__);
                return ['data' => []];
            }
            return $result;
        } catch (\Exception $e) {
            \Yii::error("Error fetching gedetineerde data: " . $e->getMessage(), __METHOD__);
            return ['data' => []];
        }
    }
}
