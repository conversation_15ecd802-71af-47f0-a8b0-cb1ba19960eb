# Notification System Documentation

## Overview

This notification system automatically creates notifications when specific routes are accessed and can optionally send email notifications. The system is designed to work without requiring manual code modifications for each new route.

## Database Structure

### Tables

1. **notification** - Stores notification templates
2. **notification_trigger** - Maps routes to notifications
3. **notification_role** - Assigns notifications to user roles
4. **notification_user** - Stores individual user notifications

## How It Works

1. **Route Monitoring**: The `NotificationListener` component monitors all route access
2. **Trigger Matching**: When a route is accessed, it checks for matching triggers in `notification_trigger`
3. **Notification Creation**: If a trigger matches, it creates notifications for users with assigned roles
4. **Email Sending**: If enabled, emails are sent to users based on the notification settings

## Setup Instructions

### 1. Run Migrations

```bash
php yii migrate
```

### 2. Set Up Example Notification (Document Creation)

Run the example migration:

```bash
php yii migrate/up m250715_000000_setup_document_create_notification
```

### 3. Assign Roles to Notifications

Use the console command to assign roles:

```bash
# List available notifications
php yii notification/list

# Assign a role to a notification
php yii notification/assign-role document_created admin
php yii notification/assign-role document_created manager
```

### 4. Enable Email Notifications (Optional)

```bash
# Enable email for a notification
php yii notification/enable-email document_created

# Disable email for a notification
php yii notification/disable-email document_created
```

## Configuration

### Creating a New Notification

1. **Add Notification Record**:
```sql
INSERT INTO notification (key, title, message_template, enabled, send_email) 
VALUES ('my_notification', 'My Notification', 'User {username} performed action on {item_name}', 1, 0);
```

2. **Add Notification Trigger**:
```sql
INSERT INTO notification_trigger (route, notification_key, trigger_type, model_class, model_id_param, fields, link_template)
VALUES ('my-controller/my-action', 'my_notification', 'create', 'common\\models\\MyModel', 'id', 
        '{"item_name": "name", "description": "description"}', '/my-controller/view?id={id}');
```

3. **Assign Roles**:
```sql
INSERT INTO notification_role (notification_id, role_id) 
VALUES ((SELECT id FROM notification WHERE key = 'my_notification'), 1);
```

### Message Template Variables

Use `{variable_name}` in message templates. Available variables:

- `{username}` - Current user's username (automatically added)
- Custom variables from the `fields` JSON in notification_trigger

### Trigger Types

- **create** - Triggers on POST requests (for creation actions)
- **update** - Triggers on POST/PUT/PATCH requests (for update actions)  
- **view** - Triggers on GET requests (for view actions)

## Console Commands

```bash
# List all notifications
php yii notification/list

# List all triggers
php yii notification/list-triggers

# Test a notification
php yii notification/test document_created

# Enable/disable email for a notification
php yii notification/enable-email document_created
php yii notification/disable-email document_created

# Assign role to notification
php yii notification/assign-role document_created admin
```

## Email Configuration

### 1. Configure Mailer

Edit `environments/dev/common/config/main-local.php` (or prod equivalent):

```php
'mailer' => [
    'class' => \yii\symfonymailer\Mailer::class,
    'viewPath' => '@common/mail',
    'useFileTransport' => false, // Set to false for real emails
    'transport' => [
        'scheme' => 'smtps',
        'host' => 'your-smtp-server.com',
        'username' => '<EMAIL>',
        'password' => 'your-password',
        'port' => 465,
    ],
],
```

### 2. Configure Email Parameters

Edit `common/config/params.php`:

```php
return [
    'senderEmail' => '<EMAIL>',
    'senderName' => 'Your App Name',
    // ... other params
];
```

## Troubleshooting

### Check Logs

Notification errors are logged to the application log. Check:
- `runtime/logs/app.log`

### Common Issues

1. **No notifications created**: Check if roles are assigned to the notification
2. **Emails not sending**: Verify mailer configuration and email settings
3. **Wrong trigger timing**: Ensure trigger_type matches the HTTP method

### Debug Commands

```bash
# Test notification manually
php yii notification/test document_created

# Check notification configuration
php yii notification/list
php yii notification/list-triggers
```

## Example: Document Creation Notification

This example shows a complete setup for document creation notifications:

1. **Notification**: Key `document_created`, triggers when documents are created
2. **Route**: `document/create` 
3. **Message**: "A new document "{document_name}" has been created by {username}."
4. **Variables**: 
   - `document_name` from `Document.filename`
   - `username` (automatically added)

The system will automatically create notifications for all users with assigned roles when someone creates a document.
