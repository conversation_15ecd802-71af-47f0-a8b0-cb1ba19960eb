<?php

use yii\widgets\DetailView;
use yii\helpers\Html;
use yii\bootstrap5\ActiveForm;
use yii\helpers\Url;
use kartik\mpdf\Pdf;

/* @var $this yii\web\View */
/* @var $model common\models\Document */
/* @var $documentData array */

$this->title = 'Document: ' . $model->filename;

// Get the PDF URL
$pdfUrl = Url::to(['document/pdf', 'id' => $model->id]);
// Get current URL for redirect
$currentUrl = Url::current();

$isAJax = Yii::$app->request->isAjax;
?>

<div class="document-view row">
    <!-- Left side - PDF Preview -->
    <div class="<?= !$isAJax ? 'col-md-6' : null ?>">
        <!-- PDF Preview in iframe -->
        <iframe
            src="<?= $pdfUrl ?>"
            width="100%"
            height="800px"
            style="border: none; border-radius: 6px;"
            title="Document PDF Preview">
        </iframe>
    </div>

    <?php if (!$isAJax): ?>
        <!-- Right side - Feedback Form -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-body">
                    <?php if (!empty($model->documentFeedbacks)): ?>
                        <h5 class="">Feedback</h5>
                        <div class="previous-feedback" style="max-height: 400px; overflow-y: auto; padding-right: 10px;">
                            <?php foreach ($model->documentFeedbacks as $feedback): ?>
                                <div class="card mb-3">
                                    <div class="card-body">
                                        <h6 class="card-subtitle mb-2 text-muted">
                                            By <?= Html::encode($feedback->createdBy->username) ?>
                                            on <?= Yii::$app->formatter->asDatetime($feedback->created_at) ?>
                                        </h6>
                                        <p class="card-text"><?= Html::encode($feedback->comment) ?></p>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>

                    <div class="mt-2">
                        <a href="<?= Url::to(['document/update', 'id' => $model->id]) ?>" class="btn btn-primary">
                            Verbeter Document
                        </a>
                    </div>
                </div>
            </div>
        </div>
</div>
</div>
<?php endif; ?>

<?php
$script = <<<JS
$('#feedback-form').on('beforeSubmit', function(e) {
    e.preventDefault();
    
    $.ajax({
        url: $(this).attr('action'),
        type: 'POST',
        data: $(this).serialize() + '&redirect_url=' + $('#redirect-url').val(),
        success: function(response) {
            if (response.success) {
                // Redirect to the same page
                window.location.href = $('#redirect-url').val();
            } else {
                alert('Error saving feedback: ' + (response.content || 'Unknown error'));
            }
        },
        error: function(xhr, status, error) {
            alert('Error submitting feedback: ' + error);
        }
    });
    
    return false;
});

// Add custom scrollbar styling
document.querySelector('.previous-feedback').style.scrollbarWidth = 'thin';
document.querySelector('.previous-feedback').style.scrollbarColor = '#6c757d #f8f9fa';
JS;
$this->registerJs($script);

// Add custom CSS
$css = <<<CSS
.previous-feedback::-webkit-scrollbar {
    width: 6px;
}

.previous-feedback::-webkit-scrollbar-track {
    background: #f8f9fa;
    border-radius: 3px;
}

.previous-feedback::-webkit-scrollbar-thumb {
    background: #6c757d;
    border-radius: 3px;
}

.previous-feedback::-webkit-scrollbar-thumb:hover {
    background: #495057;
}
CSS;
$this->registerCss($css);
?>