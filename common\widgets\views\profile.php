<?php

use yii\helpers\Html;
?>
<div class="nav-item dropdown">
    <a href="#" class="nav-link d-flex align-items-center" data-toggle="dropdown">
        <i data-lucide="user-round" class="align-middle" style="width: 18px; height: 18px;"></i>
        <span class="d-none d-sm-inline-block ms-2"><?= Html::encode($user->username) ?></span>
    </a>
    <div class="dropdown-menu dropdown-menu-right">
        <span class="dropdown-item dropdown-header">
            <strong><?= Html::encode($user->username) ?></strong><br>
            <small class="text-muted"><?= Html::encode($user->email) ?></small>
        </span>
        <div class="dropdown-divider"></div>
        <?= Html::a(
            '<i data-lucide="user-cog" class="align-middle me-2" style="width: 16px; height: 16px;"></i> Profile',
            ['/user/profile'],
            ['class' => 'dropdown-item d-flex align-items-center']
        ) ?>
        <?= Html::a(
            '<i data-lucide="log-out" class="align-middle me-2" style="width: 16px; height: 16px;"></i> Sign Out',
            ['/site/logout'],
            ['class' => 'dropdown-item d-flex align-items-center', 'data-method' => 'post']
        ) ?>
    </div>
</div>

<?php
$css = <<<CSS
    .dropdown-menu-right {
        right: 0;
        left: auto;
    }
    
    .dropdown-header {
        pointer-events: none;
    }
    
    .dropdown-item:hover {
        background-color: rgba(0, 0, 0, 0.05);
    }
    
    .nav-item.dropdown > .nav-link:hover {
        background-color: transparent;
    }
CSS;
$this->registerCss($css);

$js = <<<JS
    lucide.createIcons();
JS;
$this->registerJs($js);
?>