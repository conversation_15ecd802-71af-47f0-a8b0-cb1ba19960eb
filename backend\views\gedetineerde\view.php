<?php

use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\Gedetineerde|stdClass */
/* @var $isApiRecord boolean */
?>
<div class="gedetineerde-view">
    <?php if ($isApiRecord): ?>
        <div class="alert alert-info">
            Dit record komt van het KPA systeem.
        </div>
    <?php endif; ?>

    <?php if (isset($isApiRecord) && $isApiRecord): ?>
        <?php
        // For API records, create a custom attribute list
        $apiAttributes = [];
        $modelVars = is_object($model) ? get_object_vars($model) : (array)$model;

        // Define a mapping for better labels
        $labelMapping = [
            'persoonid' => 'Persoon ID',
            'regnr' => 'Registratie Nummer',
            'naam' => 'Achternaam',
            'voornaam' => 'Voornaam',
            'idnr' => 'Identificatie Nummer',
            'verzekeringskaartnr' => 'Verzekeringskaartnummer',
            'geboortedatum' => 'Geboortedatum',
            'pk' => 'Primary Key',
            'id' => 'ID'
        ];

        foreach ($modelVars as $key => $value) {
            // Skip internal properties
            if (in_array($key, ['isApiRecord', 'getAttributeLabel'])) {
                continue;
            }

            $apiAttributes[] = [
                'attribute' => $key,
                'label' => $labelMapping[$key] ?? ucfirst(str_replace('_', ' ', $key)),
                'value' => is_array($value) ? json_encode($value) : $value,
            ];
        }
        ?>

        <?= DetailView::widget([
            'model' => $model,
            'attributes' => $apiAttributes,
        ]) ?>
    <?php else: ?>
        <?= DetailView::widget([
            'model' => $model,
            'attributes' => [
                'id',
                'source_id',
                'regnr',
                'naam',
                'voornaam',
                'idnr',
                'verzekeringskaartnr',
                'geboortedatum',
                'created_by',
                'updated_by',
                'created_at',
                'updated_at',
            ],
        ]) ?>
    <?php endif; ?>

</div>