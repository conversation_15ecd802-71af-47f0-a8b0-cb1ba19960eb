<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%role_notification}}`.
 */
class m250707_145038_create_role_notification_table extends Migration
{
    public function safeUp()
    {
        $this->createTable('{{%notification_role}}', [
            'notification_id' => $this->integer()->notNull(),
            'role_id' => $this->integer()->notNull(),
        ]);
        $this->addPrimaryKey('pk_notification_role_id', '{{%notification_role}}', ['notification_id', 'role_id']);
        $this->addForeignKey('fk_notification_role_notification', '{{%notification_role}}', 'notification_id', '{{%notification}}', 'id', 'CASCADE');
        $this->addForeignKey('fk_notification_role_role', '{{%notification_role}}', 'role_id', '{{%role}}', 'id', 'CASCADE');
    }

    public function safeDown()
    {
        $this->dropForeign<PERSON>ey('fk_notification_role_notification', '{{%notification_role}}');
        $this->dropForeign<PERSON>ey('fk_notification_role_role', '{{%notification_role}}');
        $this->dropTable('{{%notification_role}}');
    }
}
