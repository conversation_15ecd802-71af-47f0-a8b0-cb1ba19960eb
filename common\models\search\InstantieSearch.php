<?php

namespace common\models\search;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\Instantie;

/**
 * InstantieSearch represents the model behind the search form about `common\models\Instantie`.
 */
class InstantieSearch extends Instantie
{
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'instantie_type_id', 'parent_id', 'created_by', 'updated_by'], 'integer'],
            [['instantie', 'afkorting', 'adres', 'telnr', 'created_at', 'updated_at'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = Instantie::find();

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        $query->andFilterWhere([
            'id' => $this->id,
            'instantie_type_id' => $this->instantie_type_id,
            'parent_id' => $this->parent_id,
            'created_by' => $this->created_by,
            'updated_by' => $this->updated_by,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ]);

        $query->andFilterWhere(['like', 'instantie', $this->instantie])
            ->andFilterWhere(['like', 'afkorting', $this->afkorting])
            ->andFilterWhere(['like', 'adres', $this->adres])
            ->andFilterWhere(['like', 'telnr', $this->telnr]);

        return $dataProvider;
    }
}
