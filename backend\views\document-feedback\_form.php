<?php

use yii\helpers\Html;
use yii\bootstrap5\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\RapportFeedback */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="rapport-feedback-form">

    <?php $form = ActiveForm::begin(['id' => 'rapport-feedback-form']); ?>

    <?= $form->field($model, 'document_id')->textInput() ?>

    <?= $form->field($model, 'comment')->textarea(['rows' => 6]) ?>

    <?= $form->field($model, 'created_by')->textInput() ?>

    <?= $form->field($model, 'updated_by')->textInput() ?>

    <?= $form->field($model, 'created_at')->textInput() ?>

    <?= $form->field($model, 'updated_at')->textInput() ?>


    <?php if (!Yii::$app->request->isAjax) { ?>
        <div class="form-group">
            <?= Html::submitButton($model->isNewRecord ? 'Create' : 'Update', ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']) ?>
        </div>
    <?php } ?>

    <?php ActiveForm::end(); ?>

</div>

<script>
    $(document).ready(function() {
        $('#rapport-feedback-form').on('beforeSubmit', function(e) {
            e.preventDefault();

            var form = $(this);
            var submitBtn = form.find(':submit');
            submitBtn.prop('disabled', true);

            $.ajax({
                url: form.attr('action'),
                type: 'POST',
                data: form.serialize(),
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        Toast.fire({
                            icon: 'success',
                            title: response.message
                        });

                        if (response.forceReload) {
                            $.pjax.reload({
                                container: response.forceReload
                            });
                        }

                        if (response.redirect_url) {
                            setTimeout(function() {
                                window.location.href = response.redirect_url;
                            }, 1500);
                        }

                        $('#ajaxCrudModal').modal('hide');
                    } else {
                        Toast.fire({
                            icon: 'error',
                            title: response.message || 'An error occurred'
                        });
                    }
                },
                error: function(xhr, status, error) {
                    Toast.fire({
                        icon: 'error',
                        title: 'Error submitting form: ' + error
                    });
                },
                complete: function() {
                    submitBtn.prop('disabled', false);
                }
            });
            return false;
        });
    });
</script>