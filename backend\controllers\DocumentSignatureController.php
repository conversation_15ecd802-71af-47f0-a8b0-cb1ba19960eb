<?php

namespace backend\controllers;

use common\components\SignatureHelper;
use Yii;
use common\models\DocumentSignature;
use common\models\search\DocumentSignatureSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use \yii\web\Response;
use yii\helpers\Html;

/**
 * DocumentSignatureController implements the CRUD actions for DocumentSignature model.
 */
class DocumentSignatureController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['post'],
                    'bulkdelete' => ['post'],
                ],
            ],
        ];
    }

    /**
     * Lists all DocumentSignature models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new DocumentSignatureSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        // Only show signatures for the logged-in user
        $dataProvider->query->andWhere(['user_id' => Yii::$app->user->id]);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }


    /**
     * Displays a single DocumentSignature model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        $request = Yii::$app->request;
        if ($request->isAjax) {
            Yii::$app->response->format = Response::FORMAT_JSON;
            return [
                'title' => "DocumentSignature #" . $id,
                'content' => $this->renderAjax('view', [
                    'model' => $this->findModel($id),
                ]),
                'footer' => Html::button(Yii::t('yii2-ajaxcrud', 'Close'), ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal']) .
                    Html::a(Yii::t('yii2-ajaxcrud', 'Update'), ['update', 'id' => $id], ['class' => 'btn btn-primary', 'role' => 'modal-remote'])
            ];
        } else {
            return $this->render('view', [
                'model' => $this->findModel($id),
            ]);
        }
    }

    /**
     * Creates a new DocumentSignature model.
     * For ajax request will return json object
     * and for non-ajax request if creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $request = Yii::$app->request;
        $model = new DocumentSignature();

        if ($request->isAjax) {
            /*
            *   Process for ajax request
            */
            Yii::$app->response->format = Response::FORMAT_JSON;
            if ($request->isGet) {
                return [
                    'title' => Yii::t('yii2-ajaxcrud', 'Create New') . " DocumentSignature",
                    'content' => $this->renderAjax('create', [
                        'model' => $model,
                    ]),
                    'footer' => Html::button(Yii::t('yii2-ajaxcrud', 'Close'), ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal']) .
                        Html::button(Yii::t('yii2-ajaxcrud', 'Create'), ['class' => 'btn btn-primary', 'type' => 'submit'])
                ];
            } else if ($model->load($request->post()) && $model->save()) {
                return [
                    'forceReload' => '#crud-datatable-pjax',
                    'title' => Yii::t('yii2-ajaxcrud', 'Create New') . " DocumentSignature",
                    'content' => '<span class="text-success">' . Yii::t('yii2-ajaxcrud', 'Create') . ' DocumentSignature ' . Yii::t('yii2-ajaxcrud', 'Success') . '</span>',
                    'footer' =>  Html::button(Yii::t('yii2-ajaxcrud', 'Close'), ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal']) .
                        Html::a(Yii::t('yii2-ajaxcrud', 'Create More'), ['create'], ['class' => 'btn btn-primary', 'role' => 'modal-remote'])
                ];
            } else {
                return [
                    'title' => Yii::t('yii2-ajaxcrud', 'Create New') . " DocumentSignature",
                    'content' => $this->renderAjax('create', [
                        'model' => $model,
                    ]),
                    'footer' => Html::button(Yii::t('yii2-ajaxcrud', 'Close'), ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal']) .
                        Html::button(Yii::t('yii2-ajaxcrud', 'Save'), ['class' => 'btn btn-primary', 'type' => 'submit'])
                ];
            }
        } else {
            /*
            *   Process for non-ajax request
            */
            if ($model->load($request->post()) && $model->save()) {
                return $this->redirect(['view', 'id' => $model->id]);
            } else {
                return $this->render('create', [
                    'model' => $model,
                ]);
            }
        }
    }

    /**
     * Updates an existing DocumentSignature model.
     * For ajax request will return json object
     * and for non-ajax request if update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $request = Yii::$app->request;
        $model = $this->findModel($id);

        if ($request->isAjax) {
            /*
            *   Process for ajax request
            */
            Yii::$app->response->format = Response::FORMAT_JSON;
            if ($request->isGet) {
                return [
                    'title' => Yii::t('yii2-ajaxcrud', 'Update') . " DocumentSignature #" . $id,
                    'content' => $this->renderAjax('update', [
                        'model' => $model,
                    ]),
                    'footer' => Html::button(Yii::t('yii2-ajaxcrud', 'Close'), ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal']) .
                        Html::button(Yii::t('yii2-ajaxcrud', 'Save'), ['class' => 'btn btn-primary', 'type' => 'submit'])
                ];
            } else if ($model->load($request->post()) && $model->save()) {
                return [
                    'forceReload' => '#crud-datatable-pjax',
                    'title' => "DocumentSignature #" . $id,
                    'content' => $this->renderAjax('view', [
                        'model' => $model,
                    ]),
                    'footer' => Html::button(Yii::t('yii2-ajaxcrud', 'Close'), ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal']) .
                        Html::a(Yii::t('yii2-ajaxcrud', 'Update'), ['update', 'id' => $id], ['class' => 'btn btn-primary', 'role' => 'modal-remote'])
                ];
            } else {
                return [
                    'title' => Yii::t('yii2-ajaxcrud', 'Update') . " DocumentSignature #" . $id,
                    'content' => $this->renderAjax('update', [
                        'model' => $model,
                    ]),
                    'footer' => Html::button(Yii::t('yii2-ajaxcrud', 'Close'), ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal']) .
                        Html::button(Yii::t('yii2-ajaxcrud', 'Save'), ['class' => 'btn btn-primary', 'type' => 'submit'])
                ];
            }
        } else {
            /*
            *   Process for non-ajax request
            */
            if ($model->load($request->post()) && $model->save()) {
                return $this->redirect(['view', 'id' => $model->id]);
            } else {
                return $this->render('update', [
                    'model' => $model,
                ]);
            }
        }
    }

    /**
     * Delete an existing DocumentSignature model.
     * For ajax request will return json object
     * and for non-ajax request if deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $request = Yii::$app->request;
        $this->findModel($id)->delete();

        if ($request->isAjax) {
            /*
            *   Process for ajax request
            */
            Yii::$app->response->format = Response::FORMAT_JSON;
            return ['forceClose' => true, 'forceReload' => '#crud-datatable-pjax'];
        } else {
            /*
            *   Process for non-ajax request
            */
            return $this->redirect(['index']);
        }
    }

    /**
     * Delete multiple existing DocumentSignature model.
     * For ajax request will return json object
     * and for non-ajax request if deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionBulkdelete()
    {
        $request = Yii::$app->request;
        $pks = explode(',', $request->post('pks')); // Array or selected records primary keys
        foreach ($pks as $pk) {
            $model = $this->findModel($pk);
            $model->delete();
        }

        if ($request->isAjax) {
            /*
            *   Process for ajax request
            */
            Yii::$app->response->format = Response::FORMAT_JSON;
            return ['forceClose' => true, 'forceReload' => '#crud-datatable-pjax'];
        } else {
            /*
            *   Process for non-ajax request
            */
            return $this->redirect(['index']);
        }
    }

    /**
     * Finds the DocumentSignature model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return DocumentSignature the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = DocumentSignature::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    public function actionSaveSignature($id)
    {
        $request = Yii::$app->request;
        $model = new \common\models\DocumentSignature();

        if ($request->isAjax) {
            Yii::$app->response->format = Response::FORMAT_JSON;

            if ($model->load($request->post())) {
                $rawSignature = $model->signature_base64;

                // Check if signature is empty
                if (empty($rawSignature)) {
                    return [
                        'success' => false,
                        'message' => 'No signature data received. Please try again.',
                    ];
                }

                // Validate signature format
                if (strpos($rawSignature, 'data:image') !== 0) {
                    return [
                        'success' => false,
                        'message' => 'Invalid signature format. Please clear and try again.',
                    ];
                }

                try {
                    // Encrypt the signature
                    $encryptedSignature = SignatureHelper::encryptAndEncode($rawSignature, Yii::$app->user->id);
                    $model->signature_base64 = $encryptedSignature;
                    $model->user_id = Yii::$app->user->id;
                    $model->signed_at = date('Y-m-d H:i:s');
                    $model->ip_address = Yii::$app->request->userIP;
                    $model->document_id = $id;
                    $model->user_agent = Yii::$app->request->userAgent;

                    if ($model->save()) {
                        // Log successful save
                        Yii::info("Signature saved successfully for user {$model->user_id} on post {$id}", 'signature');

                        return [
                            'success' => true,
                            // 'forceReload' => '#crud-datatable-pjax',
                            'message' => 'Ondertekening successvol'
                        ];
                    } else {
                        // Log validation errors
                        Yii::error('Signature save failed: ' . print_r($model->getErrors(), true), 'signature');

                        $errorMessages = [];
                        foreach ($model->getErrors() as $field => $errors) {
                            $errorMessages[] = implode(', ', $errors);
                        }

                        return [
                            'success' => false,
                            'message' => 'Validation failed: ' . implode('. ', $errorMessages),
                        ];
                    }
                } catch (\Exception $e) {
                    // Log the exception
                    Yii::error('Exception during signature save: ' . $e->getMessage(), 'signature');

                    return [
                        'success' => false,
                        'message' => 'An error occurred while processing your signature. Please try again.',
                    ];
                }
            } else {
                // Log form load failure
                Yii::error('Failed to load signature form data: ' . print_r($request->post(), true), 'signature');

                return [
                    'success' => false,
                    'message' => 'Failed to process form data. Please refresh and try again.',
                ];
            }
        } else {
            // Handle non-AJAX requests
            Yii::$app->session->removeAllFlashes();

            // Only process form if it was actually submitted
            if ($request->isPost && $model->load($request->post())) {
                $rawSignature = $model->signature_base64;

                try {
                    $encryptedSignature = SignatureHelper::encryptAndEncode($rawSignature, Yii::$app->user->id);
                    $model->signature_base64 = $encryptedSignature;
                    $model->user_id = Yii::$app->user->id;
                    $model->signed_at = date('Y-m-d H:i:s');
                    $model->ip_address = Yii::$app->request->userIP;
                    $model->document_id = $id;
                    $model->user_agent = Yii::$app->request->userAgent;

                    if ($model->save()) {
                        Yii::$app->session->setFlash('success', 'Signature placed successfully!');
                        return $this->redirect(['view', 'id' => $model->id]);
                    } else {
                        $model->addError('signature_base64', 'Failed to save signature.');
                    }
                } catch (\Exception $e) {
                    $model->addError('signature_base64', 'An error occurred while saving the signature.');
                }
            }

            // Create a fresh model for the form if not processing submission
            // if (!$request->isPost) {
            //     $model = new \common\models\DocumentSignature();
            //     $model->post_id = $id; // Set the post_id for the form
            // }

            return $this->render('_signature-form', [
                'model' => $model,
            ]);
        }
    }
}
