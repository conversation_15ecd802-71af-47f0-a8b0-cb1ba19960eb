<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "instantie".
 *
 * @property int $id
 * @property int|null $instantie_type_id
 * @property int|null $parent_id
 * @property string $instantie
 * @property string $afkorting
 * @property string $adres
 * @property string $telnr
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property string|null $created_at
 * @property string|null $updated_at
 *
 * @property User $createdBy
 * @property User $updatedBy
 */
class Instantie extends \yii\db\ActiveRecord
{


    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'instantie';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['instantie_type_id', 'parent_id', 'created_by', 'updated_by'], 'default', 'value' => null],
            [['instantie_type_id', 'parent_id', 'created_by', 'updated_by'], 'integer'],
            // [['instantie', 'afkorting', 'adres', 'telnr'], 'required'],
            [['created_at', 'updated_at'], 'safe'],
            [['instantie', 'afkorting', 'adres', 'telnr'], 'string', 'max' => 255],
            [['created_by'], 'exist', 'skipOnError' => true, 'targetClass' => User::class, 'targetAttribute' => ['created_by' => 'id']],
            [['updated_by'], 'exist', 'skipOnError' => true, 'targetClass' => User::class, 'targetAttribute' => ['updated_by' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'instantie_type_id' => 'Instantie Type ID',
            'parent_id' => 'Parent ID',
            'instantie' => 'Instantie',
            'afkorting' => 'Afkorting',
            'adres' => 'Adres',
            'telnr' => 'Telnr',
            'created_by' => 'Created By',
            'updated_by' => 'Updated By',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Gets query for [[CreatedBy]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getCreatedBy()
    {
        return $this->hasOne(User::class, ['id' => 'created_by']);
    }

    /**
     * Gets query for [[UpdatedBy]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUpdatedBy()
    {
        return $this->hasOne(User::class, ['id' => 'updated_by']);
    }
}
