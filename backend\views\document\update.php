<?php

use yii\helpers\Html;
use kartik\form\ActiveForm;
use yii\helpers\Url;

/* @var $this yii\web\View */
/* @var $model common\models\Rapport */

$this->title = 'Update Document';

// Get rapport data using the helper function
$documentData = getDocument($model->id);
?>
<div class="rapport-update row">
    <!-- Left side - Questions and Answers -->
    <div class="col-md-6">
        <div class="card rounded-3">
            <div class="card-body">
                <h3 class="mb-3"><?= $documentData['data']['document']['type'] ?></h3>
                <?php $form = ActiveForm::begin([
                    'id' => 'rapport-form',
                    'enableAjaxValidation' => false,
                    'options' => [
                        'data-redirect-url' => Url::to(['view', 'id' => $model->id])
                    ]
                ]); ?>

                <?php if (!empty($documentData['data']['questions_and_answers'])): ?>
                    <div id="questions-container">
                        <?php foreach ($documentData['data']['questions_and_answers'] as $qa): ?>
                            <div class="form-group mb-3">
                                <label class="control-label">
                                    <?= $qa['question']['number'] ?>. <?= Html::encode($qa['question']['text']) ?>
                                </label>
                                <?= Html::textInput(
                                    "Answers[{$qa['question']['id']}]",
                                    $qa['answer']['text'] ?? '',
                                    [
                                        'class' => 'form-control',
                                        'required' => true,
                                        'data-question-id' => $qa['question']['id']
                                    ]
                                ) ?>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <div class="form-group">
                        <?= Html::submitButton('Save', ['class' => 'btn btn-success']) ?>
                    </div>
                <?php endif; ?>

                <?php ActiveForm::end(); ?>
            </div>
        </div>
    </div>

    <!-- Right side - Comments -->
    <div class="col-md-6">
        <div class="card rounded-3">
            <div class="card-body">
                <h4>Comments</h4>
                <?php if (!empty($documentData['data']['feedback'])): ?>
                    <div class="previous-feedback" style="max-height: 600px; overflow-y: auto; padding: 10px;">
                        <?php foreach ($documentData['data']['feedback'] as $feedback): ?>
                            <div class="card mb-3">
                                <div class="card-body">
                                    <h6 class="card-subtitle mb-2 text-muted">
                                        By <?= Html::encode($feedback['created_by']['username']) ?>
                                        on <?= Yii::$app->formatter->asDatetime($feedback['created_at']) ?>
                                    </h6>
                                    <p class="card-text"><?= Html::encode($feedback['comment']) ?></p>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <p class="text-muted">No comments yet.</p>
                <?php endif; ?>
                <div class="mt-2">
                    <a href="<?= Url::to(['document/comments', 'id' => $model->id]) ?>" class="btn btn-outline-primary rounded-2">
                        Plaats feedback
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$script = <<<JS
// Initialize Toast configuration
const Toast = Swal.mixin({
    toast: true,
    position: 'top-end',
    showConfirmButton: false,
    timer: 3000,
    timerProgressBar: true,
    didOpen: (toast) => {
        toast.addEventListener('mouseenter', Swal.stopTimer)
        toast.addEventListener('mouseleave', Swal.resumeTimer)
    }
});

$('#rapport-form').on('submit', function(e) {
    e.preventDefault(); // Prevent the default form submission
    
    let form = $(this);
    $.ajax({
        url: form.attr('action'),
        type: 'POST',
        data: form.serialize(),
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                Toast.fire({
                    icon: 'success',
                    title: response.message
                });
                
                // Optional: Redirect after delay
                setTimeout(function() {
                    window.location.href = form.data('redirect-url') || window.location.href;
                }, 1500);
            } else {
                Toast.fire({
                    icon: 'error',
                    title: response.message || 'An error occurred'
                });
            }
        },
        error: function(xhr, status, error) {
            Toast.fire({
                icon: 'error',
                title: 'Error submitting form: ' + error
            });
        }
    });
    return false;
});
JS;
$this->registerJs($script);
?>