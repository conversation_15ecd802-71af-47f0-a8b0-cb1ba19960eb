<?php

namespace common\components;

use Yii;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\helpers\ArrayHelper;

class LucideSidebarMenuHelper extends \hail812\adminlte\widgets\Menu
{
    /**
     * @var string the current route
     */
    protected $currentRoute;

    /**
     * @var string the current controller ID
     */
    protected $currentController;

    /**
     * @var string the current module ID
     */
    protected $currentModule;

    /**
     * @inheritdoc
     */
    public $linkTemplate = '<a class="nav-link {active}" href="{url}" {target}>{icon} <p>{label}</p></a>';

    /**
     * @inheritdoc
     */
    public $submenuTemplate = "\n<ul class='nav nav-treeview' {show}>\n{items}\n</ul>\n";

    /**
     * @inheritdoc
     */
    public $parentItemTemplate = '<li class="nav-item {active} {open}">
        <a class="nav-link {active}" href="{url}" {target}>
            {icon} <p>{label}<i class="right fas fa-angle-right"></i></p>
        </a>
        {submenu}
    </li>';

    /**
     * @inheritdoc
     */
    public $headerTemplate = '<li class="nav-header text-muted fw-medium">{label}</li>';

    /**
     * @inheritdoc
     */
    public function init()
    {
        parent::init();

        // Initialize current route info
        $this->currentRoute = Yii::$app->controller->route;
        $this->currentController = Yii::$app->controller->id;
        $this->currentModule = Yii::$app->controller->module->id == 'app-backend' ?
            null : Yii::$app->controller->module->id;
    }

    /**
     * Checks if the given item is active
     * @param array $item the menu item to check
     * @return boolean whether the item is active
     */
    protected function isItemActive($item)
    {
        if (isset($item['active'])) {
            return $item['active'];
        }

        // Check if URL matches current route
        if (isset($item['url'])) {
            $route = is_array($item['url']) ? $item['url'][0] : $item['url'];
            if ($route !== '#') {
                // Convert route to a normalized format
                if (strpos($route, '/') !== 0) {
                    $route = '/' . $route;
                }

                // Different matching strategies
                if (isset($item['activeMatch']) && $item['activeMatch'] === 'exact') {
                    // Exact match
                    return '/' . $this->currentRoute === $route;
                } else if (isset($item['activeMatch']) && $item['activeMatch'] === 'controller') {
                    // Controller match (any action in the controller)
                    $itemController = $this->getControllerFromRoute($route);
                    return $itemController === $this->currentController;
                } else if (isset($item['activeMatch']) && $item['activeMatch'] === 'prefix') {
                    // Prefix match
                    return strpos('/' . $this->currentRoute, $route) === 0;
                } else {
                    // Default: exact route match
                    return '/' . $this->currentRoute === $route;
                }
            }
        }

        return false;
    }

    /**
     * Extracts controller ID from a route
     * @param string $route the route
     * @return string the controller ID
     */
    protected function getControllerFromRoute($route)
    {
        $parts = explode('/', trim($route, '/'));
        return isset($parts[0]) ? $parts[0] : '';
    }

    /**
     * Renders the menu items
     * @param array $items the menu items to be rendered
     * @return string the rendering result
     */
    protected function renderItems($items)
    {
        $n = count($items);
        $lines = [];
        foreach ($items as $i => $item) {
            // Skip divider directly
            if (isset($item['header']) && $item['header']) {
                $lines[] = $this->renderHeader($item);
                continue;
            }

            $options = array_merge($this->itemOptions, ArrayHelper::getValue($item, 'options', []));

            // Determine if the item is active
            $item['active'] = $this->isItemActive($item);

            // If item has children, check if any child is active
            if (isset($item['items'])) {
                foreach ($item['items'] as &$child) {
                    $childActive = $this->isItemActive($child);
                    $child['active'] = $childActive;
                    if ($childActive) {
                        $item['active'] = true;
                    }
                }
            }

            $tag = ArrayHelper::remove($options, 'tag', 'li');
            $class = ['nav-item'];
            if ($item['active']) {
                $class[] = 'active';
            }

            if (isset($item['items'])) {
                $class[] = 'has-treeview';
                if ($item['active']) {
                    $class[] = 'menu-open';
                }
            }

            $options['class'] = implode(' ', $class);

            $lines[] = Html::beginTag($tag, $options);
            $lines[] = $this->renderItem($item);
            $lines[] = Html::endTag($tag);
        }

        return implode("\n", $lines);
    }

    /**
     * Renders a header item.
     * @param array $item the header item to be rendered
     * @return string the rendering result
     */
    protected function renderHeader($item)
    {
        return strtr($this->headerTemplate, [
            '{label}' => $item['label']
        ]);
    }

    /**
     * Renders a menu item.
     * @param array $item the menu item to be rendered
     * @return string the rendering result
     */
    protected function renderItem($item)
    {
        // Handle nested items
        if (isset($item['items'])) {
            $submenuTemplate = $this->submenuTemplate;

            $submenu = $this->renderItems($item['items']);
            $show = $item['active'] ? "style='display: block'" : '';
            $submenu = strtr($submenuTemplate, [
                '{items}' => $submenu,
                '{show}' => $show
            ]);

            $template = $this->parentItemTemplate;

            return strtr($template, [
                '{label}' => $item['label'],
                '{icon}' => empty($item['icon']) ? '' : $this->renderLucideIcon($item),
                '{url}' => isset($item['url']) ? Url::to($item['url']) : '#',
                '{active}' => $item['active'] ? 'active' : '',
                '{open}' => $item['active'] ? 'menu-open' : '',
                '{submenu}' => $submenu,
                '{target}' => isset($item['target']) ? 'target="' . $item['target'] . '"' : ''
            ]);
        } else {
            $template = $this->linkTemplate;

            return strtr($template, [
                '{label}' => $item['label'],
                '{icon}' => empty($item['icon']) ? '' : $this->renderLucideIcon($item),
                '{url}' => isset($item['url']) ? Url::to($item['url']) : '#',
                '{active}' => $item['active'] ? 'active' : '',
                '{target}' => isset($item['target']) ? 'target="' . $item['target'] . '"' : ''
            ]);
        }
    }

    /**
     * Renders the Lucide icon
     * @param array $item the menu item
     * @return string the rendering result
     */
    protected function renderLucideIcon($item)
    {
        // Make sure icon is a string
        $icon = is_array($item['icon']) ? json_encode($item['icon']) : $item['icon'];

        // Check if it's a Lucide icon
        if (isset($item['iconType']) && $item['iconType'] === 'lucide') {
            return Html::tag('i', '', [
                'data-lucide' => $icon,
                'class' => 'nav-icon lucide-icon-small',
                'style' => 'width: 18px; height: 18px;' // Make icons slightly smaller
            ]);
        }

        // Default AdminLTE icon handling
        return Html::tag('i', '', ['class' => 'nav-icon ' . $icon]);
    }
}
